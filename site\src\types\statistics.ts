/**
 * Типы для статистики и агрегации данных
 */

import type { Part } from '@/lib/types';
import type { CatalogItem } from 'packages/shared-types/src/index';
import type { ComparisonStatus } from '@/lib/comparators';

// ============================================================================
// Numeric Statistics
// ============================================================================

/**
 * Числовая статистика для атрибутов
 */
export type NumericStats = {
  min: number;
  max: number;
  avg: number;
  median: number;
  count: number;
  sum: number;
  stdDev?: number;
  unit?: string | null;
};

// ============================================================================
// Distribution
// ============================================================================

/**
 * Распределение значений для строковых атрибутов
 */
export type ValueDistribution = {
  value: string;
  count: number;
  percentage: number;
};

// ============================================================================
// Attribute Statistics
// ============================================================================

/**
 * Статистика атрибутов (числовая и распределение)
 */
export type AttributeStatistics = {
  templateId: number;
  templateName: string;
  templateTitle: string;
  dataType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'JSON';
  unit?: string | null;

  // Для числовых атрибутов
  numericStats?: NumericStats;

  // Для строковых атрибутов
  distribution?: ValueDistribution[];
  topValues?: ValueDistribution[]; // Топ N наиболее частых значений

  // Общая статистика
  uniqueValues: number;
  totalValues: number;
  missingCount: number;
  missingPercentage: number;
};

// ============================================================================
// Brand Statistics
// ============================================================================

/**
 * Статистика распределения по брендам
 */
export type BrandStatistics = {
  brandId: number;
  brandName: string;
  isOem: boolean;
  count: number;
  percentage: number;
};

// ============================================================================
// Category Statistics
// ============================================================================

/**
 * Статистика распределения по категориям
 */
export type CategoryStatistics = {
  categoryId: number;
  categoryName: string;
  count: number;
  percentage: number;
};

// ============================================================================
// Accuracy Statistics
// ============================================================================

/**
 * Статистика распределения по уровням точности
 */
export type AccuracyStatistics = {
  accuracy: 'EXACT_MATCH' | 'MATCH_WITH_NOTES' | 'PARTIAL_MATCH' | 'LEGACY_MATCH';
  count: number;
  percentage: number;
};

// ============================================================================
// Comparison Statistics
// ============================================================================

/**
 * Статистика сравнений атрибутов
 */
export type ComparisonStatistics = {
  status: ComparisonStatus;
  count: number;
  percentage: number;
};

/**
 * Общее качество соответствия
 */
export type MatchQuality = {
  score: number; // 0-100
  label: 'excellent' | 'good' | 'fair' | 'poor';
  exactMatches: number;
  nearMatches: number;
  mismatches: number;
  missingAttributes: number;
};

// ============================================================================
// Time Series
// ============================================================================

/**
 * Данные временного ряда
 */
export type TimeSeriesData = {
  timestamp: Date;
  value: number;
  label?: string;
};

/**
 * Статистика по временным диапазонам
 */
export type TimeRangeStatistics = {
  range: 'day' | 'week' | 'month' | 'year';
  data: TimeSeriesData[];
  total: number;
  growthRate?: number; // Процент роста
};

// ============================================================================
// Dataset Statistics
// ============================================================================

/**
 * Агрегированная статистика для набора данных
 */
export type DatasetStatistics = {
  totalItems: number;
  attributes: AttributeStatistics[];
  brands?: BrandStatistics[];
  categories?: CategoryStatistics[];
  accuracy?: AccuracyStatistics[];
  comparisons?: ComparisonStatistics[];
  matchQuality?: MatchQuality;
  updatedAt: Date;
};

// ============================================================================
// Chart Data
// ============================================================================

/**
 * Точка данных для визуализации
 */
export type ChartDataPoint = {
  label: string;
  value: number;
  color?: string;
  metadata?: Record<string, unknown>;
};

/**
 * Данные для графиков
 */
export type ChartData = {
  type: 'bar' | 'line' | 'pie' | 'histogram';
  data: ChartDataPoint[];
  title?: string;
  xAxisLabel?: string;
  yAxisLabel?: string;
};

// ============================================================================
// Histogram
// ============================================================================

/**
 * Бин гистограммы для числовых распределений
 */
export type HistogramBin = {
  min: number;
  max: number;
  count: number;
  percentage: number;
};

/**
 * Данные гистограммы
 */
export type HistogramData = {
  bins: HistogramBin[];
  binSize: number;
  totalCount: number;
};

