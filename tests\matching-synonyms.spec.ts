import { test, expect } from '@playwright/test'
import { compareAttributes } from '../api/lib/matching/attributeComparator'
import type { AttributeWithTemplate, SynonymEntry, SynonymGroupInfo } from '../api/lib/matching/attributeComparator'

const uniqKey = (t: number, v: string) => `${t}__${v.trim().toLowerCase()}`

function makeStrTemplate(id: number, name = `t${id}`) {
  return { name, title: name.toUpperCase(), dataType: 'STRING' as const, tolerance: null }
}

function makeAttr(templateId: number, value: string): AttributeWithTemplate {
  return { templateId, value, numericValue: null, template: makeStrTemplate(templateId) }
}

test.describe('compareAttributes: string synonyms with hierarchy', () => {
  test('STRING_EXACT when values equal ignoring case/trim', () => {
    const tId = 1
    const item = new Map<number, AttributeWithTemplate>([[tId, makeAttr(tId, ' Foo ') ]])
    const part = new Map<number, AttributeWithTemplate>([[tId, makeAttr(tId, 'foo') ]])
    const res = compareAttributes({ itemAttrByTemplateId: item, partAttrByTemplateId: part, synonymMap: new Map() as Map<string, SynonymEntry> })
    expect(res.ok).toBeTruthy()
    const d = res.details.find(d => d.templateId === tId)!
    expect(d.kind).toBe('STRING_EXACT')
  })

  test('Same group synonyms choose worst of synonyms levels', () => {
    const tId = 1
    const item = new Map<number, AttributeWithTemplate>([[tId, makeAttr(tId, 'foo') ]])
    const part = new Map<number, AttributeWithTemplate>([[tId, makeAttr(tId, 'fuu') ]])
    const synonymMap = new Map<string, SynonymEntry>([
      [uniqKey(tId, 'foo'), { templateId: tId, groupId: 10, level: 'EXACT', notes: 'a', canonical: 'FOO' }],
      [uniqKey(tId, 'fuu'), { templateId: tId, groupId: 10, level: 'NEAR', notes: 'b', canonical: 'FOO' }],
    ])
    const res = compareAttributes({ itemAttrByTemplateId: item, partAttrByTemplateId: part, synonymMap })
    expect(res.ok).toBeTruthy()
    const d = res.details.find(d => d.templateId === tId)!
    expect(d.kind).toBe('STRING_SYNONYM_NEAR')
    expect(d.synonymLevel).toBe('NEAR')
  })

  test('Different child groups with LEGACY LCA => LEGACY', () => {
    const tId = 2
    const item = new Map<number, AttributeWithTemplate>([[tId, makeAttr(tId, 'foo') ]])
    const part = new Map<number, AttributeWithTemplate>([[tId, makeAttr(tId, 'bar') ]])
    const synonymMap = new Map<string, SynonymEntry>([
      [uniqKey(tId, 'foo'), { templateId: tId, groupId: 10, level: 'EXACT', notes: 'a', canonical: 'FOO' }],
      [uniqKey(tId, 'bar'), { templateId: tId, groupId: 11, level: 'EXACT', notes: 'b', canonical: 'BAR' }],
    ])
    const groupsById = new Map<number, SynonymGroupInfo>([
      [10, { parentId: 1, level: 'EXACT', notes: null, canonical: null }],
      [11, { parentId: 1, level: 'NEAR', notes: 'child note', canonical: null }],
      [1,  { parentId: null, level: 'LEGACY', notes: 'root note', canonical: 'CAN' }],
    ])
    const res = compareAttributes({ itemAttrByTemplateId: item, partAttrByTemplateId: part, synonymMap, groupsById })
    expect(res.ok).toBeTruthy()
    const d = res.details.find(d => d.templateId === tId)!
    expect(d.kind).toBe('STRING_SYNONYM_LEGACY')
    expect(d.synonymLevel).toBe('LEGACY')
    expect(d.canonical).toBe('CAN')
    expect(d.notes).toContain('root note')
  })

  test('No synonyms or no common ancestor => mismatch', () => {
    const tId = 3
    const item = new Map<number, AttributeWithTemplate>([[tId, makeAttr(tId, 'foo') ]])
    const part = new Map<number, AttributeWithTemplate>([[tId, makeAttr(tId, 'bar') ]])
    // synonym only for foo
    const synonymMap = new Map<string, SynonymEntry>([
      [uniqKey(tId, 'foo'), { templateId: tId, groupId: 10, level: 'EXACT', notes: null, canonical: null }],
    ])
    const groupsById = new Map<number, SynonymGroupInfo>([[10, { parentId: null, level: 'EXACT', notes: null, canonical: null }]])
    const res = compareAttributes({ itemAttrByTemplateId: item, partAttrByTemplateId: part, synonymMap, groupsById })
    expect(res.ok).toBeFalsy()
  })
})

