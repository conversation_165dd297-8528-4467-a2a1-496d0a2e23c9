/**
 * Утилиты сравнения атрибутов
 * Извлечено из components/catalog/pro/columns.tsx для переиспользования
 */

import type {
  PartAttribute,
  CatalogItemAttribute,
  AttributeSynonymGroup,
  AttributeSynonym,
} from 'packages/shared-types/src/index';

// Расширенный тип с полными данными о синонимах
type FullAttributeTemplate = {
  id: number;
  name: string;
  title: string;
  dataType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'JSON';
  unit: string | null;
  tolerance: number | null;
  synonymGroups: (AttributeSynonymGroup & { synonyms: AttributeSynonym[] })[];
};

type FullPartAttribute = PartAttribute & { template: FullAttributeTemplate };
type FullCatalogItemAttribute = CatalogItemAttribute & {
  template: FullAttributeTemplate;
};

// ============================================================================
// Types
// ============================================================================

export type ComparisonStatus =
  | 'exact'
  | 'near'
  | 'legacy'
  | 'mismatch'
  | 'missing';

export type ComparisonResult = {
  status: ComparisonStatus;
  notes: string;
  difference?: number; // Для числовых атрибутов
  matchedSynonymGroup?: string; // Для строковых атрибутов
};

// ============================================================================
// Main Comparison Function
// ============================================================================

/**
 * Основная функция сравнения атрибутов
 * Извлечено из columns.tsx (lines 27-59)
 */
export function compareAttributeValues(
  partAttr: FullPartAttribute,
  itemAttr?: FullCatalogItemAttribute
): ComparisonResult {
  // Если атрибут отсутствует у товара
  if (!itemAttr) {
    return {
      status: 'missing',
      notes: 'Атрибут отсутствует',
    };
  }

  const template = partAttr.template;
  const partValue = partAttr.value;
  const itemValue = itemAttr.value;

  // Сравнение числовых атрибутов
  if (template.dataType === 'NUMBER') {
    return compareNumericValues(
      parseFloat(partValue),
      parseFloat(itemValue),
      template.tolerance ?? 0,
      template.unit ?? undefined
    );
  }

  // Сравнение строковых атрибутов
  return compareStringValues(partValue, itemValue, template.synonymGroups);
}

// ============================================================================
// Helper Functions
// ============================================================================

/**
 * Сравнение числовых значений с учетом допуска
 */
export function compareNumericValues(
  partValue: number,
  itemValue: number,
  tolerance: number,
  unit?: string
): ComparisonResult {
  const difference = itemValue - partValue;
  const absDifference = Math.abs(difference);

  if (absDifference <= tolerance) {
    return {
      status: 'exact',
      notes:
        partValue === itemValue
          ? 'Точное совпадение'
          : `Совпадение в пределах допуска (±${tolerance}${unit || ''})`,
      difference: difference,
    };
  }

  return {
    status: 'mismatch',
    notes: `Отклонение: ${difference > 0 ? '+' : ''}${difference.toFixed(3)} ${
      unit || ''
    }`,
    difference: difference,
  };
}

/**
 * Сравнение строковых значений с поддержкой синонимов
 */
export function compareStringValues(
  partValue: string,
  itemValue: string,
  synonymGroups?: (AttributeSynonymGroup & { synonyms: AttributeSynonym[] })[]
): ComparisonResult {
  // Точное совпадение
  if (partValue.toLowerCase() === itemValue.toLowerCase()) {
    return {
      status: 'exact',
      notes: 'Точное совпадение',
    };
  }

  // Проверка синонимов
  if (synonymGroups && synonymGroups.length > 0) {
    for (const group of synonymGroups) {
      const canonicalMatch =
        group.canonicalValue?.toLowerCase() === itemValue.toLowerCase();
      const synonymMatch = group.synonyms.some(
        (s: AttributeSynonym) => s.value.toLowerCase() === itemValue.toLowerCase()
      );

      if (canonicalMatch || synonymMatch) {
        let status: 'exact' | 'near' | 'legacy' = 'exact';

        if (group.compatibilityLevel === 'NEAR') status = 'near';
        if (group.compatibilityLevel === 'LEGACY') status = 'legacy';

        return {
          status,
          notes: group.notes || `Совпадение по группе синонимов "${group.name}"`,
          matchedSynonymGroup: group.name,
        };
      }
    }
  }

  return {
    status: 'mismatch',
    notes: 'Значения не совпадают',
  };
}

/**
 * Найти группу синонимов, содержащую указанное значение
 */
export function findMatchingSynonymGroup(
  value: string,
  synonymGroups: (AttributeSynonymGroup & { synonyms: AttributeSynonym[] })[]
): (AttributeSynonymGroup & { synonyms: AttributeSynonym[] }) | undefined {
  const lowerValue = value.toLowerCase();

  return synonymGroups.find((group) => {
    const canonicalMatch =
      group.canonicalValue?.toLowerCase() === lowerValue;
    const synonymMatch = group.synonyms.some(
      (s: AttributeSynonym) => s.value.toLowerCase() === lowerValue
    );
    return canonicalMatch || synonymMatch;
  });
}

/**
 * Получить Tailwind классы цвета для статуса сравнения
 */
export function getComparisonStatusColor(status: ComparisonStatus): string {
  switch (status) {
    case 'exact':
      return 'text-green-500 bg-green-50 border-green-200';
    case 'near':
      return 'text-blue-500 bg-blue-50 border-blue-200';
    case 'legacy':
      return 'text-purple-500 bg-purple-50 border-purple-200';
    case 'mismatch':
      return 'text-red-500 bg-red-50 border-red-200';
    case 'missing':
      return 'text-yellow-500 bg-yellow-50 border-yellow-200';
    default:
      return 'text-gray-500 bg-gray-50 border-gray-200';
  }
}

/**
 * Получить название иконки lucide-react для статуса
 */
export function getComparisonStatusIcon(
  status: ComparisonStatus
):
  | 'CheckCircle2'
  | 'Check'
  | 'CheckCircle'
  | 'XCircle'
  | 'AlertCircle'
  | 'HelpCircle' {
  switch (status) {
    case 'exact':
      return 'CheckCircle2';
    case 'near':
      return 'Check';
    case 'legacy':
      return 'CheckCircle';
    case 'mismatch':
      return 'XCircle';
    case 'missing':
      return 'AlertCircle';
    default:
      return 'HelpCircle';
  }
}

/**
 * Получить русскую метку для статуса сравнения
 */
export function getComparisonStatusLabel(status: ComparisonStatus): string {
  switch (status) {
    case 'exact':
      return 'Точное совпадение';
    case 'near':
      return 'Близкое совпадение';
    case 'legacy':
      return 'Совпадение (устаревшее)';
    case 'mismatch':
      return 'Несовпадение';
    case 'missing':
      return 'Атрибут отсутствует';
    default:
      return 'Неизвестно';
  }
}

// ============================================================================
// Batch Comparison
// ============================================================================

/**
 * Сравнить полные наборы атрибутов
 */
export function compareAttributeSets(
  partAttributes: FullPartAttribute[],
  itemAttributes: FullCatalogItemAttribute[]
): ComparisonResult[] {
  return partAttributes.map((partAttr) => {
    const itemAttr = itemAttributes.find(
      (attr) => attr.templateId === partAttr.templateId
    );
    return compareAttributeValues(partAttr, itemAttr);
  });
}

/**
 * Рассчитать общий score соответствия (0-100)
 */
export function calculateMatchScore(comparisons: ComparisonResult[]): number {
  if (comparisons.length === 0) return 0;

  const weights: Record<ComparisonStatus, number> = {
    exact: 1.0,
    near: 0.8,
    legacy: 0.6,
    mismatch: 0.0,
    missing: 0.0,
  };

  const totalScore = comparisons.reduce((sum, comparison) => {
    return sum + weights[comparison.status];
  }, 0);

  return Math.round((totalScore / comparisons.length) * 100);
}

/**
 * Получить качество соответствия по score
 */
export function getMatchQuality(
  score: number
): 'excellent' | 'good' | 'fair' | 'poor' {
  if (score >= 90) return 'excellent';
  if (score >= 70) return 'good';
  if (score >= 50) return 'fair';
  return 'poor';
}

// ============================================================================
// Sorting/Filtering
// ============================================================================

/**
 * Сортировать сравнения по приоритету статуса
 */
export function sortByMatchQuality(
  comparisons: ComparisonResult[]
): ComparisonResult[] {
  const statusPriority: Record<ComparisonStatus, number> = {
    exact: 1,
    near: 2,
    legacy: 3,
    mismatch: 4,
    missing: 5,
  };

  return [...comparisons].sort(
    (a, b) => statusPriority[a.status] - statusPriority[b.status]
  );
}

/**
 * Фильтровать сравнения по статусам
 */
export function filterByStatus(
  comparisons: ComparisonResult[],
  statuses: ComparisonStatus[]
): ComparisonResult[] {
  return comparisons.filter((comparison) =>
    statuses.includes(comparison.status)
  );
}

