---
import MainLayout from "../layouts/MainLayout.astro";
import { PricingPlans } from "@/components/landing/PricingPlans";

// Получаем параметры из URL для контекстного сообщения
const { searchParams } = Astro.url;
const reason = searchParams.get('reason');
const feature = searchParams.get('feature');
const from = searchParams.get('from');

// Определяем сообщение в зависимости от причины редиректа
let alertMessage = '';
let alertTitle = '';

if (reason === 'pro_required') {
  alertTitle = 'Требуется PRO подписка';
  
  if (feature === 'part_viewing') {
    alertMessage = 'Доступ к страницам эталонов (Part) и группам взаимозаменяемости доступен только пользователям с PRO подпиской. Оформите подписку, чтобы получить полный доступ к базе аналогов.';
  } else if (feature === 'part_access') {
    alertMessage = 'Эта функция доступна только пользователям с PRO подпиской. Оформите подписку для доступа к расширенным возможностям каталога.';
  } else {
    alertMessage = 'Для доступа к этой странице требуется PRO подписка.';
  }
}

const title = 'Тарифные планы | PartTec';
const description = 'Выберите подходящий тарифный план для доступа к полной базе взаимозаменяемости запчастей';
---

<MainLayout title={title} description={description}>
  <div class="container mx-auto px-4 py-12">
    <!-- Alert Banner для редиректов -->
    {alertMessage && (
      <div class="max-w-4xl mx-auto mb-8">
        <div class="bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
          <div class="flex items-start gap-3">
            <svg class="w-5 h-5 text-amber-600 dark:text-amber-500 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <div class="flex-1">
              <h3 class="font-semibold text-amber-900 dark:text-amber-100 mb-1">{alertTitle}</h3>
              <p class="text-sm text-amber-800 dark:text-amber-200">{alertMessage}</p>
              {from && (
                <p class="text-xs text-amber-700 dark:text-amber-300 mt-2">
                  После оформления подписки вы сможете вернуться на: <code class="bg-amber-100 dark:bg-amber-900/50 px-1 py-0.5 rounded">{from}</code>
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    )}

    <!-- Заголовок страницы -->
    <div class="text-center mb-12">
      <h1 class="text-4xl lg:text-5xl font-bold mb-4">
        Выберите свой <span class="text-primary">тарифный план</span>
      </h1>
      <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
        Получите доступ к полной базе взаимозаменяемости запчастей и расширенным возможностям поиска
      </p>
    </div>

    <!-- Компонент с тарифными планами -->
    <PricingPlans client:load />

    <!-- Дополнительная информация -->
    <div class="max-w-4xl mx-auto mt-16">
      <div class="bg-card border border-border rounded-lg p-6">
        <h2 class="text-2xl font-semibold mb-4">Что включает PRO подписка?</h2>
        <div class="grid md:grid-cols-2 gap-4 text-sm text-muted-foreground">
          <div class="flex items-start gap-2">
            <svg class="w-5 h-5 text-chart-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <span>Полный доступ к группам взаимозаменяемости (Part)</span>
          </div>
          <div class="flex items-start gap-2">
            <svg class="w-5 h-5 text-chart-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <span>Сравнение аналогов по эталонным атрибутам</span>
          </div>
          <div class="flex items-start gap-2">
            <svg class="w-5 h-5 text-chart-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <span>Информация о поставщиках и ценах</span>
          </div>
          <div class="flex items-start gap-2">
            <svg class="w-5 h-5 text-chart-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <span>AI-ассистент для умного подбора запчастей</span>
          </div>
          <div class="flex items-start gap-2">
            <svg class="w-5 h-5 text-chart-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <span>Экспорт данных в Excel/CSV</span>
          </div>
          <div class="flex items-start gap-2">
            <svg class="w-5 h-5 text-chart-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <span>Расширенный поиск по техническим параметрам</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</MainLayout>