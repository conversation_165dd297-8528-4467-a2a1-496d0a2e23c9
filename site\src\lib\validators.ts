/**
 * Утилиты валидации с использованием Zod
 * Используются для валидации форм и данных
 */

import { z } from 'zod';
import type { AttributeTemplate, CatalogSearchFilters } from '@/types/catalog';

// ============================================================================
// Zod Schemas
// ============================================================================

/**
 * Схема валидации поискового запроса
 */
export const searchQuerySchema = z
  .string()
  .min(1, 'Поисковый запрос не может быть пустым')
  .max(200, 'Поисковый запрос слишком длинный')
  .transform((val) => val.trim());

/**
 * Схема валидации SKU
 */
export const skuSchema = z
  .string()
  .min(1, 'SKU не может быть пустым')
  .max(100, 'SKU слишком длинный')
  .regex(/^[a-zA-Z0-9_-]+$/, 'SKU может содержать только буквы, цифры, дефисы и подчеркивания')
  .transform((val) => val.trim().toUpperCase());

/**
 * Схема валидации числового диапазона
 */
export const numericRangeSchema = z
  .object({
    min: z.number().optional(),
    max: z.number().optional(),
  })
  .refine(
    (data) => {
      if (data.min !== undefined && data.max !== undefined) {
        return data.min <= data.max;
      }
      return true;
    },
    {
      message: 'Минимальное значение не может быть больше максимального',
    }
  );

/**
 * Схема валидации фильтра атрибута
 */
export const attributeFilterSchema = z.object({
  values: z.array(z.string()).optional(),
  numericRange: z.tuple([z.number().optional(), z.number().optional()]).optional(),
});

/**
 * Схема валидации полных фильтров поиска по каталогу
 */
export const catalogSearchFiltersSchema = z.object({
  query: z.string().default(''),
  categoryIds: z.array(z.number()).default([]),
  brandIds: z.array(z.number()).default([]),
  isOemOnly: z.boolean().default(false),
  attributeFilters: z.record(z.number(), attributeFilterSchema).default({}),
  // Дополнительные опциональные поля
  sortBy: z.enum(['sku', 'description', 'name', 'createdAt', 'updatedAt']).optional(),
  sortDir: z.enum(['asc', 'desc']).optional(),
  accuracy: z.array(z.enum(['EXACT_MATCH', 'MATCH_WITH_NOTES', 'REQUIRES_MODIFICATION', 'PARTIAL_MATCH'])).optional(),
  hasImages: z.boolean().optional(),
});

/**
 * Схема валидации пресета фильтров
 */
export const filterPresetSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Название пресета не может быть пустым').max(100),
  description: z.string().max(500).optional(),
  filters: catalogSearchFiltersSchema,
  isDefault: z.boolean().default(false),
  createdAt: z.date(),
  updatedAt: z.date(),
});

/**
 * Схема валидации email
 */
export const emailSchema = z
  .string()
  .email('Неверный формат email')
  .toLowerCase()
  .trim();

/**
 * Схема валидации URL
 */
export const urlSchema = z
  .string()
  .url('Неверный формат URL')
  .trim();

// ============================================================================
// Validation Functions
// ============================================================================

/**
 * Валидирует и очищает поисковый запрос
 */
export function validateSearchQuery(query: string): {
  success: boolean;
  data?: string;
  error?: string;
} {
  const result = searchQuerySchema.safeParse(query);
  
  if (result.success) {
    return { success: true, data: result.data };
  }
  
  return {
    success: false,
    error: result.error.errors[0]?.message || 'Неверный поисковый запрос',
  };
}

/**
 * Валидирует консистентность числового диапазона
 */
export function validateNumericRange(
  min?: number,
  max?: number
): {
  success: boolean;
  error?: string;
} {
  if (min === undefined && max === undefined) {
    return { success: true };
  }

  if (min !== undefined && max !== undefined && min > max) {
    return {
      success: false,
      error: 'Минимальное значение не может быть больше максимального',
    };
  }

  if (min !== undefined && !isFinite(min)) {
    return {
      success: false,
      error: 'Минимальное значение должно быть числом',
    };
  }

  if (max !== undefined && !isFinite(max)) {
    return {
      success: false,
      error: 'Максимальное значение должно быть числом',
    };
  }

  return { success: true };
}

/**
 * Валидирует значение атрибута против шаблона
 */
export function validateAttributeValue(
  value: string,
  template: AttributeTemplate
): {
  success: boolean;
  error?: string;
} {
  // Проверка обязательности
  if (template.isRequired && (!value || value.trim() === '')) {
    return {
      success: false,
      error: `Атрибут "${template.title}" обязателен для заполнения`,
    };
  }

  // Если значение пустое и не обязательное, это валидно
  if (!value || value.trim() === '') {
    return { success: true };
  }

  // Проверка типа данных
  switch (template.dataType) {
    case 'NUMBER': {
      const numValue = parseFloat(value);
      if (isNaN(numValue) || !isFinite(numValue)) {
        return {
          success: false,
          error: `Атрибут "${template.title}" должен быть числом`,
        };
      }
      break;
    }

    case 'BOOLEAN': {
      const lowerValue = value.toLowerCase();
      if (!['true', 'false', '1', '0', 'да', 'нет'].includes(lowerValue)) {
        return {
          success: false,
          error: `Атрибут "${template.title}" должен быть булевым значением`,
        };
      }
      break;
    }

    case 'DATE': {
      const dateValue = new Date(value);
      if (isNaN(dateValue.getTime())) {
        return {
          success: false,
          error: `Атрибут "${template.title}" должен быть датой`,
        };
      }
      break;
    }

    case 'JSON': {
      try {
        JSON.parse(value);
      } catch {
        return {
          success: false,
          error: `Атрибут "${template.title}" должен быть валидным JSON`,
        };
      }
      break;
    }
  }

  // Проверка allowedValues
  if (template.allowedValues && template.allowedValues.length > 0) {
    if (!template.allowedValues.includes(value)) {
      return {
        success: false,
        error: `Значение "${value}" не входит в список допустимых значений для атрибута "${template.title}"`,
      };
    }
  }

  return { success: true };
}

/**
 * Валидирует структуру объекта фильтров
 */
export function validateFilters(filters: unknown): {
  success: boolean;
  data?: CatalogSearchFilters;
  errors?: string[];
} {
  const result = catalogSearchFiltersSchema.safeParse(filters);

  if (result.success) {
    return { success: true, data: result.data };
  }

  return {
    success: false,
    errors: result.error.errors.map((err) => err.message),
  };
}

/**
 * Очищает поисковый запрос от специальных символов
 */
export function sanitizeSearchQuery(query: string): string {
  // Удаляем потенциально опасные символы, оставляем только безопасные
  return query
    .trim()
    .replace(/[<>{}[\]\\]/g, '') // Удаляем опасные символы
    .replace(/\s+/g, ' ') // Нормализуем пробелы
    .slice(0, 200); // Ограничиваем длину
}

// ============================================================================
// Type Guards
// ============================================================================

/**
 * Type guard для проверки валидности фильтра атрибута
 */
export function isValidAttributeFilter(
  value: unknown
): value is { values?: string[]; numericRange?: [number | undefined, number | undefined] } {
  if (typeof value !== 'object' || value === null) {
    return false;
  }

  const obj = value as Record<string, unknown>;

  if ('values' in obj) {
    if (!Array.isArray(obj.values)) return false;
    if (!obj.values.every((v) => typeof v === 'string')) return false;
  }

  if ('numericRange' in obj) {
    if (!Array.isArray(obj.numericRange)) return false;
    if (obj.numericRange.length !== 2) return false;
    const [min, max] = obj.numericRange;
    if (min !== undefined && typeof min !== 'number') return false;
    if (max !== undefined && typeof max !== 'number') return false;
  }

  return true;
}

/**
 * Проверяет, является ли атрибут числовым типом
 */
export function isNumericAttribute(template: AttributeTemplate): boolean {
  return template.dataType === 'NUMBER';
}

/**
 * Проверяет, является ли атрибут строковым типом
 */
export function isStringAttribute(template: AttributeTemplate): boolean {
  return template.dataType === 'STRING';
}

// ============================================================================
// Error Formatting
// ============================================================================

/**
 * Форматирует ошибки Zod в user-friendly сообщения на русском
 */
export function formatValidationErrors(errors: z.ZodError): string[] {
  return errors.errors.map((err) => {
    const path = err.path.length > 0 ? `${err.path.join('.')}: ` : '';
    return `${path}${err.message}`;
  });
}

/**
 * Получает первое сообщение об ошибке из ZodError
 */
export function getFirstValidationError(errors: z.ZodError): string | undefined {
  return errors.errors[0]?.message;
}


