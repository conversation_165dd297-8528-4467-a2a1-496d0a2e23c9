import 'catalog_schema'
import 'user_schema'
import 'aggregate_schemas_schema'



generator client {
    provider = "prisma-client-js"
}

plugin zod {
    provider = '@core/zod'
    // Генерируем Zod-схемы прямо в workspace-пакет shared-types
    output = '../packages/shared-types/src/zod'
    compile = true
    preserveTsFiles = true
}


// Дублируем генерацию Zod-схем для совместимости с серверным кодом API
plugin zod_api {
    provider = '@core/zod'
    output = 'generated/zod'
    compile = true
    preserveTsFiles = true
}

plugin trpc {
    provider = '@zenstackhq/trpc'
    output = 'generated/trpc'
    version = 'v11'
    importCreateRouter = '../../../trpc'
    importProcedure = '../../../trpc'
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}
model DatabaseSnapshot {
    id             String         @id @default(uuid())
    name           String
    description    String?
    type           SnapshotType // FULL_DATABASE, SELECTIVE_TABLES
    status         SnapshotStatus // CREATING, READY, RESTORING, FAILED, DELETED
    filePath       String // путь к файлу снапшота
    fileSize       Int? // размер файла в байтах
    tablesIncluded String[] // список таблиц для селективных снапшотов
    metadata       Json? // дополнительные метаданные (версия схемы, статистика)
    createdBy      String // ID пользователя
    createdAt      DateTime       @default(now())
    updatedAt      DateTime       @updatedAt
    restoredAt     DateTime? // когда был восстановлен
    error          String? // описание ошибки если status = FAILED

    @@allow('all', auth().role == 'ADMIN')
    @@index([status])
    @@index([type])
    @@index([createdAt])
}

enum SnapshotType {
    FULL_DATABASE
    SELECTIVE_TABLES
}

enum SnapshotStatus {
    CREATING
    READY
    RESTORING
    FAILED
    DELETED
}