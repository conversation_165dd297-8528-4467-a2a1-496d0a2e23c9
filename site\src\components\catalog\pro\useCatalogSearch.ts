"use client"

import { useMemo } from "react"
import { trpc } from "@/lib/trpc"
import { useCatalogGlobalState } from "@/lib/catalog-state"
import { useDebounce } from "@/hooks/useDebounce"
import { useFeatureAccess } from "@/hooks/useFeatureAccess"
import { aggregateStringAttribute, aggregateNumericAttribute } from "@/lib/aggregators"
import type { FilterMetadata } from "@/types/filters"

// Хук для использования в отдельных компонентах
export function useCatalogSearch() {
  const { filters, setFilters, updateFilters, clearFilters } = useCatalogGlobalState()
  
  // Проверка подписки для условного использования API
  const { canUseAdvancedSearch, isPro, isPending } = useFeatureAccess()

  // Дебаунсинг для поискового запроса
  const debouncedQuery = useDebounce(filters.query, 300)

  // Загружаем данные через изолированный site API для PRO пользователей
  const { data: partsResult, isLoading: partsLoading, error: partsError } = trpc.site.search.parts.useQuery({
    search: debouncedQuery || undefined,
    brandIds: filters.brandIds,
    categoryIds: filters.categoryIds,
    isOemOnly: filters.isOemOnly,
    attributeFilters: Object.entries(filters.attributeFilters || {}).map(([templateId, f]) => ({
      templateId: Number(templateId),
      values: f.values?.length ? f.values : undefined,
      minValue: f.numericRange?.[0],
      maxValue: f.numericRange?.[1],
    })),
    limit: filters.limit ?? 50,
    offset: filters.offset ?? 0,
    sortBy: filters.sortBy || 'updatedAt',
    sortDir: filters.sortDir || 'desc',
  }, {
    enabled: canUseAdvancedSearch && !isPending,
    staleTime: 30_000,
    gcTime: 5 * 60_000,
    refetchOnWindowFocus: false, // Не перезапрашивать при фокусе
    refetchOnMount: false, // Использовать кеш если доступен
    keepPreviousData: true, // Показывать предыдущие данные пока загружаются новые
  })

  // Загружаем данные через catalogItems API для FREE пользователей
  const { data: catalogItemsResult, isLoading: catalogItemsLoading, error: catalogItemsError } = trpc.site.search.catalogItems.useQuery({
    search: debouncedQuery || undefined,
    brandIds: filters.brandIds,
    categoryIds: filters.categoryIds,
    isOemOnly: filters.isOemOnly,
    attributeFilters: Object.entries(filters.attributeFilters || {}).map(([templateId, f]) => ({
      templateId: Number(templateId),
      values: f.values?.length ? f.values : undefined,
      minValue: f.numericRange?.[0],
      maxValue: f.numericRange?.[1],
    })),
    limit: filters.limit ?? 50,
    offset: filters.offset ?? 0,
    sortBy: filters.sortBy || 'updatedAt',
    sortDir: filters.sortDir || 'desc',
  }, {
    enabled: !canUseAdvancedSearch && !isPending,
    staleTime: 30_000,
    gcTime: 5 * 60_000,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    keepPreviousData: true,
  })

  // Определяем активный результат на основе подписки
  const activeResult = useMemo(() => {
    if (isPro) {
      return {
        data: partsResult,
        isLoading: partsLoading,
        error: partsError,
        resultType: 'parts' as const
      }
    } else {
      return {
        data: catalogItemsResult,
        isLoading: catalogItemsLoading,
        error: catalogItemsError,
        resultType: 'catalogItems' as const
      }
    }
  }, [isPro, partsResult, partsLoading, partsError, catalogItemsResult, catalogItemsLoading, catalogItemsError])

  // Логирование для отладки
  if (typeof window !== 'undefined') {
    console.log('Search filters:', filters)
    console.log('isPro:', isPro, 'canUseAdvancedSearch:', canUseAdvancedSearch)
    console.log('resultType:', activeResult.resultType)
    console.log('Active result:', activeResult.data)
    console.log('Active error:', activeResult.error)
    console.log('Is loading:', activeResult.isLoading)
  }

  // Вычисляем метаданные фильтров и доступные значения атрибутов
  const metadata: FilterMetadata = useMemo(() => {
    if (!activeResult.data?.items) {
      return {
        totalResults: 0,
        filteredResults: 0,
        availableValues: {},
        numericRanges: {},
        availableValuesWithCounts: {},
      }
    }

    const items = activeResult.data.items
    const availableValues: Record<number, string[]> = {}
    const numericRanges: Record<number, { min: number; max: number }> = {}
    const availableValuesWithCounts: Record<number, Array<{ value: string; count: number }>> = {}

    // Извлекаем все атрибуты из элементов
    const allAttributes = items.flatMap((item) => item.attributes || [])

    // Группируем по templateId
    const grouped = new Map<number, typeof allAttributes>()
    for (const attr of allAttributes) {
      const existing = grouped.get(attr.templateId) || []
      existing.push(attr)
      grouped.set(attr.templateId, existing)
    }

    // Вычисляем распределения и диапазоны
    for (const [templateId, attrs] of grouped) {
      const firstAttr = attrs[0]
      if (!firstAttr) continue

      // Проверяем, есть ли template в атрибуте
      if ('template' in firstAttr && firstAttr.template) {
        const template = firstAttr.template

        if (template.dataType === 'STRING') {
          // Используем aggregator helper для получения распределения
          const distribution = aggregateStringAttribute(attrs, templateId)
          
          // Получаем уникальные значения для строковых атрибутов
          availableValues[templateId] = distribution.map((d) => d.value)
          
          // Сохраняем распределение с подсчетами
          availableValuesWithCounts[templateId] = distribution.map((d) => ({
            value: d.value,
            count: d.count,
          }))
        } else if (template.dataType === 'NUMBER') {
          // Вычисляем числовой диапазон
          const stats = aggregateNumericAttribute(attrs, templateId)
          if (stats) {
            numericRanges[templateId] = {
              min: stats.min,
              max: stats.max,
            }
            
            // Для числовых атрибутов также сохраняем распределение для гистограммы
            const numericValues = attrs
              .map((a) => a.value)
              .filter(Boolean)
              .map((v) => ({ value: v, count: 1 }))
            
            // Группируем одинаковые значения
            const valueCounts = new Map<string, number>()
            for (const attr of attrs) {
              if (attr.value) {
                valueCounts.set(attr.value, (valueCounts.get(attr.value) || 0) + 1)
              }
            }
            
            availableValuesWithCounts[templateId] = Array.from(valueCounts.entries()).map(
              ([value, count]) => ({ value, count })
            )
          }
        }
      }
    }

    return {
      totalResults: activeResult.data.total || 0,
      filteredResults: items.length,
      availableValues,
      numericRanges,
      availableValuesWithCounts,
    }
  }, [activeResult.data])

  // Обратная совместимость со старым форматом
  const availableAttributeValues = useMemo(() => {
    return {
      values: metadata.availableValues,
      valuesWithCounts: metadata.availableValuesWithCounts,
      numericStats: Object.fromEntries(
        Object.entries(metadata.numericRanges).map(([id, range]) => [
          id,
          {
            min: range.min,
            max: range.max,
            avg: (range.min + range.max) / 2,
          },
        ])
      ),
    }
  }, [metadata])

  return {
    filters,
    setFilters,
    updateFilters,
    clearFilters,
    results: activeResult.data?.items ?? [],
    totalCount: activeResult.data?.total || 0,
    filteredCount: activeResult.data?.items.length || 0,
    isLoading: activeResult.isLoading,
    isPro,
    resultType: activeResult.resultType,
    metadata, // Новый формат с полными метаданными
    availableAttributeValues, // Старый формат для обратной совместимости
  }
}

