import type { CatalogSearchFilters } from '@/types/catalog'

const SAVED_SEARCHES_KEY = 'search-saved-searches'
const SEARCH_HISTORY_KEY = 'search-history'
const MAX_HISTORY_ITEMS = 20
const SEARCH_VERSION = 1

export interface SavedSearch {
  id: string
  name: string
  query: string
  filters?: CatalogSearchFilters
  resultCount?: number
  createdAt: Date
  lastUsedAt: Date
}

export interface SearchHistoryItem {
  query: string
  timestamp: Date
  resultCount?: number
}

/**
 * Generate unique ID for saved search
 */
function generateId(): string {
  return `search_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
}

/**
 * Save current search with name
 */
export function saveSearch(
  name: string,
  query: string,
  filters?: CatalogSearchFilters,
  resultCount?: number
): SavedSearch {
  const searches = loadSavedSearches()
  
  const newSearch: SavedSearch = {
    id: generateId(),
    name,
    query,
    filters,
    resultCount,
    createdAt: new Date(),
    lastUsedAt: new Date()
  }

  searches.push(newSearch)
  
  if (typeof window !== 'undefined') {
    localStorage.setItem(SAVED_SEARCHES_KEY, JSON.stringify({
      version: SEARCH_VERSION,
      searches
    }))
  }

  return newSearch
}

/**
 * Load all saved searches from localStorage
 */
export function loadSavedSearches(): SavedSearch[] {
  if (typeof window === 'undefined') return []

  try {
    const data = localStorage.getItem(SAVED_SEARCHES_KEY)
    if (!data) return []

    const parsed = JSON.parse(data)
    const searches = parsed.searches || []

    return searches.map((s: SavedSearch) => ({
      ...s,
      createdAt: new Date(s.createdAt),
      lastUsedAt: new Date(s.lastUsedAt)
    }))
  } catch {
    return []
  }
}

/**
 * Delete saved search by ID
 */
export function deleteSavedSearch(id: string): void {
  const searches = loadSavedSearches().filter(s => s.id !== id)
  
  if (typeof window !== 'undefined') {
    localStorage.setItem(SAVED_SEARCHES_KEY, JSON.stringify({
      version: SEARCH_VERSION,
      searches
    }))
  }
}

/**
 * Update existing saved search
 */
export function updateSavedSearch(id: string, updates: Partial<SavedSearch>): SavedSearch | null {
  const searches = loadSavedSearches()
  const index = searches.findIndex(s => s.id === id)
  
  if (index === -1) return null

  searches[index] = {
    ...searches[index],
    ...updates
  }

  if (typeof window !== 'undefined') {
    localStorage.setItem(SAVED_SEARCHES_KEY, JSON.stringify({
      version: SEARCH_VERSION,
      searches
    }))
  }

  return searches[index]
}

/**
 * Apply saved search (updates URL params)
 */
export function applySavedSearch(search: SavedSearch): void {
  if (typeof window === 'undefined') return

  updateLastUsed(search.id)

  const params = new URLSearchParams()
  params.set('q', search.query)
  
  if (search.filters) {
    params.set('filters', JSON.stringify(search.filters))
  }

  window.history.pushState({}, '', `?${params.toString()}`)
  window.location.reload()
}

/**
 * Update lastUsedAt timestamp
 */
export function updateLastUsed(id: string): void {
  updateSavedSearch(id, { lastUsedAt: new Date() })
}

/**
 * Add search to history
 */
export function addToSearchHistory(query: string, resultCount?: number): void {
  if (typeof window === 'undefined' || !query.trim()) return

  const history = getSearchHistory()
  
  // Remove existing entry if exists
  const filtered = history.filter(item => item.query !== query)

  // Add new entry at the beginning
  const newItem: SearchHistoryItem = {
    query,
    timestamp: new Date(),
    resultCount
  }

  filtered.unshift(newItem)

  // Keep only MAX_HISTORY_ITEMS
  const limited = filtered.slice(0, MAX_HISTORY_ITEMS)

  localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify({
    version: SEARCH_VERSION,
    history: limited
  }))
}

/**
 * Get recent searches sorted by timestamp descending
 */
export function getSearchHistory(): SearchHistoryItem[] {
  if (typeof window === 'undefined') return []

  try {
    const data = localStorage.getItem(SEARCH_HISTORY_KEY)
    if (!data) return []

    const parsed = JSON.parse(data)
    const history = parsed.history || []

    return history.map((item: SearchHistoryItem) => ({
      ...item,
      timestamp: new Date(item.timestamp)
    }))
  } catch {
    return []
  }
}

/**
 * Clear all search history
 */
export function clearSearchHistory(): void {
  if (typeof window === 'undefined') return
  localStorage.removeItem(SEARCH_HISTORY_KEY)
}

/**
 * Remove specific query from history
 */
export function removeFromHistory(query: string): void {
  const history = getSearchHistory().filter(item => item.query !== query)
  
  if (typeof window !== 'undefined') {
    localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify({
      version: SEARCH_VERSION,
      history
    }))
  }
}

/**
 * Validate search name
 */
export function validateSearchName(
  name: string,
  existingSearches: SavedSearch[]
): { isValid: boolean; error?: string } {
  if (!name.trim()) {
    return { isValid: false, error: 'Имя поиска не может быть пустым' }
  }

  if (name.length > 50) {
    return { isValid: false, error: 'Имя поиска не может быть длиннее 50 символов' }
  }

  if (existingSearches.some(s => s.name === name)) {
    return { isValid: false, error: 'Поиск с таким именем уже существует' }
  }

  if (!/^[\w\s\-_а-яёА-ЯЁ]+$/.test(name)) {
    return { isValid: false, error: 'Имя содержит недопустимые символы' }
  }

  return { isValid: true }
}

/**
 * Get most frequent searches from history
 */
export function getPopularSearches(limit: number = 5): string[] {
  const history = getSearchHistory()
  
  const frequencyMap = new Map<string, number>()
  
  history.forEach(item => {
    const count = frequencyMap.get(item.query) || 0
    frequencyMap.set(item.query, count + 1)
  })

  const sorted = Array.from(frequencyMap.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, limit)
    .map(([query]) => query)

  return sorted
}

/**
 * Export saved searches as JSON string
 */
export function exportSavedSearches(): string {
  const searches = loadSavedSearches()
  return JSON.stringify({
    version: SEARCH_VERSION,
    exportDate: new Date().toISOString(),
    searches
  }, null, 2)
}

/**
 * Import saved searches from JSON
 */
export function importSavedSearches(json: string): SavedSearch[] {
  try {
    const parsed = JSON.parse(json)
    
    if (parsed.version !== SEARCH_VERSION) {
      throw new Error('Несовместимая версия экспорта')
    }

    const searches = parsed.searches || []
    const existing = loadSavedSearches()
    
    // Merge with existing, avoiding duplicates
    const merged = [...existing]
    
    searches.forEach((importedSearch: SavedSearch) => {
      if (!existing.some(s => s.name === importedSearch.name)) {
        merged.push({
          ...importedSearch,
          id: generateId(),
          createdAt: new Date(importedSearch.createdAt),
          lastUsedAt: new Date(importedSearch.lastUsedAt)
        })
      }
    })

    if (typeof window !== 'undefined') {
      localStorage.setItem(SAVED_SEARCHES_KEY, JSON.stringify({
        version: SEARCH_VERSION,
        searches: merged
      }))
    }

    return merged
  } catch (error) {
    throw new Error(`Ошибка импорта: ${error instanceof Error ? error.message : 'Неизвестная ошибка'}`)
  }
}
