---
import MainLayout from "../../../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/clients";
import PartPageIsland from "@/components/catalog/islands/PartPageIsland.tsx";
// ВАЖНО: Эта страница защищена middleware (/site/src/middleware.ts)
// Доступ разрешен только пользователям с PRO подпиской
// Дополнительная server-side проверка для defense-in-depth

import type { Part } from "@/lib/types";

const { id } = Astro.params;

// Defense-in-depth: дополнительная проверка подписки на сервере
const user = Astro.locals.user
const subscription = (user as any)?.subscription
const isPro = subscription === 'PRO'

// Если пользователь не PRO (обход middleware?), редирект на pricing
if (!isPro) {
  console.warn('[Part Page] Non-PRO user accessed Part page, redirecting to pricing')
  return Astro.redirect(`/pricing?reason=pro_required&feature=part_viewing&from=${encodeURIComponent(Astro.url.pathname)}`)
}

if (!id || isNaN(Number(id))) {
  return Astro.redirect('/catalog');
}

let part: Part | null = null;

try {
  // prettier-ignore
  part = await trpcClient.crud.part.findUnique.query({
    where: { id: Number(id) },
    include: {
      partCategory: true,
      image: true,
      mediaAssets: true,
      attributes: {
        include: {
          template: {
            include: {
              synonymGroups: {
                include: {
                  synonyms: true
                }
              }
            }
          },
        },
      },
      applicabilities: {
        include: {
          catalogItem: {
            include: {
              brand: true,
              image: true,
              mediaAssets: true,
              attributes: {
                include: {
                  template: true,
                },
              },
            },
          },
        },
      },
      equipmentApplicabilities: {
        include: {
          equipmentModel: {
            include: {
              brand: true,
              attributes: {
                include: {
                  template: true
                }
              }
            }
          }
        }
      },
    },
  }) as Part;
} catch (error) {
  console.error('Error preloading part data:', error);
}

if (import.meta.env.DEV && part) {
  console.log('[Part Page] Loaded Part data:', {
    id: part.id,
    name: part.name,
    applicabilitiesCount: part.applicabilities?.length ?? 0,
    equipmentApplicabilitiesCount: part.equipmentApplicabilities?.length ?? 0,
    attributesCount: part.attributes?.length ?? 0,
  })
}

if (!part) {
  return Astro.redirect('/catalog');
}

// Cache control для production
if (import.meta.env.PROD) {
  Astro.response.headers.set(
    'Cache-Control',
    'public, max-age=300, s-maxage=600, stale-while-revalidate=86400'
  )
  
  // Generate ETag based on part updatedAt
  const etag = `"${part.id}-${new Date(part.updatedAt).getTime()}"`
  Astro.response.headers.set('ETag', etag)

  // Check If-None-Match header
  const ifNoneMatch = Astro.request.headers.get('If-None-Match')
  if (ifNoneMatch === etag) {
    return new Response(null, { status: 304 })
  }
}
---

<MainLayout title={part.name || `Запчасть #${part.id}`} description={`Группа взаимозаменяемости`}>
  <PartPageIsland client:load part={part} />
</MainLayout>


