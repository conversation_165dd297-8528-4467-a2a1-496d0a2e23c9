import { createContext, useContext, useState, type ReactNode } from 'react'

export interface BulkSelectionContextValue {
  selectedIds: Set<number>
  selectItem: (id: number) => void
  deselectItem: (id: number) => void
  toggleItem: (id: number) => void
  selectAll: (ids: number[]) => void
  deselectAll: () => void
  isSelected: (id: number) => boolean
  selectedCount: number
  hasSelection: boolean
}

const BulkSelectionContext = createContext<BulkSelectionContextValue | undefined>(undefined)

export interface BulkSelectionProviderProps {
  children: ReactNode
}

export function BulkSelectionProvider({ children }: BulkSelectionProviderProps) {
  const [selectedIds, setSelectedIds] = useState<Set<number>>(new Set())
  
  const selectItem = (id: number) => {
    setSelectedIds(prev => {
      const newSet = new Set(prev)
      newSet.add(id)
      return newSet
    })
  }
  
  const deselectItem = (id: number) => {
    setSelectedIds(prev => {
      const newSet = new Set(prev)
      newSet.delete(id)
      return newSet
    })
  }
  
  const toggleItem = (id: number) => {
    setSelectedIds(prev => {
      const newSet = new Set(prev)
      if (newSet.has(id)) {
        newSet.delete(id)
      } else {
        newSet.add(id)
      }
      return newSet
    })
  }
  
  const selectAll = (ids: number[]) => {
    setSelectedIds(new Set(ids))
  }
  
  const deselectAll = () => {
    setSelectedIds(new Set())
  }
  
  const isSelected = (id: number): boolean => {
    return selectedIds.has(id)
  }
  
  const selectedCount = selectedIds.size
  const hasSelection = selectedCount > 0
  
  const value: BulkSelectionContextValue = {
    selectedIds,
    selectItem,
    deselectItem,
    toggleItem,
    selectAll,
    deselectAll,
    isSelected,
    selectedCount,
    hasSelection
  }
  
  return (
    <BulkSelectionContext.Provider value={value}>
      {children}
    </BulkSelectionContext.Provider>
  )
}

export function useBulkSelection(): BulkSelectionContextValue {
  const context = useContext(BulkSelectionContext)
  if (context === undefined) {
    throw new Error('useBulkSelection must be used within a BulkSelectionProvider')
  }
  return context
}

