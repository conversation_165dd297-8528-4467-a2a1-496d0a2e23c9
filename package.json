{"name": "parttec3", "version": "1.0.0", "private": true, "description": "PartTec - система управления каталогом взаимозаменяемых запчастей", "scripts": {"dev": "concurrently \"npm run dev:api\" \"npm run dev:cpanel\" \"npm run dev:site\"", "dev:api": "cd api && npm run dev", "dev:cpanel": "cd cpanel && npm run dev", "dev:site": "cd site && npm run dev", "build": "npm run build:api && npm run build:cpanel && npm run build:site", "build:api": "cd api && bunx zenstack generate", "build:cpanel": "cd cpanel && npm run build", "build:site": "cd site && npm run build", "preview": "cd site && npm run preview"}, "keywords": ["parttec", "astro", "vue", "tailwind", "primevue", "volt"], "author": "PartTec Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.54.1", "concurrently": "^9.2.0"}, "dependencies": {"@radix-ui/react-progress": "^1.1.7", "tar": "^7.5.1", "zod": "^4.1.1"}}