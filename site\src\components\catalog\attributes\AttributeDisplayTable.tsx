"use client";

import { useState } from "react";
import { BookType, ChevronDown, ChevronUp } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { formatAttributeValue, formatTolerance } from "@/lib/formatters";
import { analytics } from "@/lib/analytics";
import { cn } from "@/lib/utils";

// ============================================================================
// Types
// ============================================================================

type SynonymGroup = {
  id: number;
  name: string;
  notes: string | null;
  compatibilityLevel: string;
  canonicalValue: string | null;
  synonyms: Array<{ id: number; value: string }>;
};

type AttributeTemplate = {
  title: string;
  name: string;
  dataType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'JSON';
  unit: string | null;
  tolerance: number | null;
  synonymGroups?: SynonymGroup[];
};

type DisplayAttribute = {
  id: number;
  templateId: number;
  value: string;
  template: AttributeTemplate;
};

export interface AttributeDisplayTableProps {
  attributes: DisplayAttribute[];
  showSynonyms?: boolean;
  showTolerance?: boolean;
  onAttributeClick?: (attributeId: number) => void;
  className?: string;
  maxVisible?: number;
  collapsible?: boolean;
  // Analytics context
  contextId?: number; // partId or itemId for analytics
  contextType?: 'part' | 'catalogItem';
}

// ============================================================================
// Synonym Popover Component
// ============================================================================

interface SynonymPopoverProps {
  template: AttributeTemplate;
  contextId?: number;
}

function SynonymPopover({ template, contextId }: SynonymPopoverProps) {
  if (!template.synonymGroups || template.synonymGroups.length === 0) {
    return null;
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 text-muted-foreground hover:text-primary"
          onClick={() => {
            if (contextId) {
              analytics.synonymPopoverOpened(template.title, contextId);
            }
          }}
          aria-label={`Показать группы синонимов для ${template.title}`}
        >
          <BookType className="h-3.5 w-3.5" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="grid gap-4">
          <div className="space-y-2">
            <h4 className="font-medium leading-none">
              Группы синонимов для "{template.title}"
            </h4>
            <p className="text-sm text-muted-foreground">
              Правила эквивалентности для строковых значений.
            </p>
          </div>
          <div className="grid gap-2">
            {template.synonymGroups.map((group) => (
              <div key={group.id} className="text-xs p-2 rounded bg-accent/50 border">
                <p className="font-semibold">
                  {group.name}{' '}
                  <span className="text-muted-foreground font-normal">
                    ({group.compatibilityLevel})
                  </span>
                </p>
                {group.notes && (
                  <p className="text-muted-foreground italic mt-1 mb-2">
                    "{group.notes}"
                  </p>
                )}
                <div className="flex flex-wrap gap-1 mt-1">
                  {group.synonyms.map((s) => (
                    <Badge key={s.id} variant="secondary" className="text-xs">
                      {s.value}
                    </Badge>
                  ))}
                  {group.canonicalValue && (
                    <Badge variant="outline" title="Каноническое значение" className="text-xs">
                      {group.canonicalValue}
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

// ============================================================================
// Main Component
// ============================================================================

export default function AttributeDisplayTable({
  attributes,
  showSynonyms = false,
  showTolerance = false,
  onAttributeClick,
  className = '',
  maxVisible = 10,
  collapsible = true,
  contextId,
  contextType,
}: AttributeDisplayTableProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Handle attribute click
  const handleAttributeClick = (attrId: number) => {
    if (onAttributeClick) {
      onAttributeClick(attrId);
    }
  };

  // Determine visible attributes
  const shouldCollapse = collapsible && attributes.length > maxVisible;
  const visibleAttributes = shouldCollapse && !isExpanded 
    ? attributes.slice(0, maxVisible) 
    : attributes;
  const hiddenCount = attributes.length - maxVisible;

  if (attributes.length === 0) {
    return (
      <div className={cn("text-center py-8 text-muted-foreground", className)}>
        Нет атрибутов для отображения
      </div>
    );
  }

  const AttributeTable = ({ attrs }: { attrs: DisplayAttribute[] }) => (
    <Table>
      <TableBody>
        {attrs.map((attr) => {
          const formattedValue = formatAttributeValue(attr.value, attr.template.unit);
          const hasToleranceDisplay =
            showTolerance &&
            attr.template.dataType === 'NUMBER' &&
            attr.template.tolerance !== null &&
            attr.template.tolerance > 0;

          const displayValue = hasToleranceDisplay 
            ? formatTolerance(Number(attr.value), attr.template.tolerance!, attr.template.unit ?? undefined)
            : formattedValue;

          return (
            <TableRow 
              key={attr.id}
              className="hover:bg-muted/50 cursor-pointer transition-colors"
              onClick={() => handleAttributeClick(attr.id)}
            >
              <TableCell className="font-medium text-sm py-2 w-1/3">
                <div className="flex items-center gap-2">
                  <span className="text-foreground">{attr.template.title}</span>
                  {showSynonyms && (
                    <SynonymPopover template={attr.template} contextId={contextId} />
                  )}
                </div>
              </TableCell>
              <TableCell className="text-sm py-2">
                <div className="flex items-center gap-2">
                  <span className="font-semibold text-foreground">{displayValue}</span>
                  {attr.template.dataType === 'NUMBER' && attr.template.unit && (
                    <Badge variant="outline" className="text-xs">
                      {attr.template.unit}
                    </Badge>
                  )}
                </div>
              </TableCell>
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );

  return (
    <div className={cn("space-y-3", className)}>
      <div className="border rounded-lg overflow-hidden bg-card">
        <AttributeTable attrs={visibleAttributes} />
      </div>

      {shouldCollapse && (
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleTrigger asChild>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full gap-2"
              onClick={() => {
                if (contextId && contextType) {
                  analytics.attributeTableExpanded(contextId, contextType, !isExpanded);
                }
              }}
            >
              {isExpanded ? (
                <>
                  <ChevronUp className="h-4 w-4" />
                  Скрыть атрибуты
                </>
              ) : (
                <>
                  <ChevronDown className="h-4 w-4" />
                  Показать еще {hiddenCount} атрибутов
                </>
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3">
            <div className="border rounded-lg overflow-hidden bg-card">
              <AttributeTable attrs={attributes.slice(maxVisible)} />
            </div>
          </CollapsibleContent>
        </Collapsible>
      )}
    </div>
  );
}
