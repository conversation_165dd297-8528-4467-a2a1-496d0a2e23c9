import type { CatalogItem } from './types'
import { formatAttributeValue } from './formatters'

export type ExportOptions = {
  includeApplicabilities?: boolean
  includeAlternatives?: boolean
  includeEquipment?: boolean
  includeComparison?: boolean
  includeMedia?: boolean
}

/**
 * Экранирует значение для CSV (обрабатывает кавычки, запятые, переносы строк)
 */
function escapeCsvValue(value: string): string {
  if (value.includes('"') || value.includes(',') || value.includes('\n')) {
    return `"${value.replace(/"/g, '""')}"`
  }
  return value
}

/**
 * Генерирует CSV-строку с данными артикула
 */
export function generateCatalogItemCSV(
  item: CatalogItem,
  options: ExportOptions = {}
): string {
  const rows: string[] = []
  
  // Заголовок
  rows.push('Тип,Ключ,Значение')
  
  // Основная информация
  rows.push(`Основное,SKU,${escapeCsvValue(item.sku)}`)
  rows.push(`Основное,Бренд,${escapeCsvValue(item.brand?.name || '')}`)
  
  if (item.brand?.isOem) {
    rows.push(`Основное,OEM,Да`)
  }
  
  if (item.description) {
    rows.push(`Основное,Описание,${escapeCsvValue(item.description)}`)
  }
  
  // Атрибуты
  if (item.attributes && item.attributes.length > 0) {
    rows.push('') // Пустая строка для разделения
    item.attributes.forEach(attr => {
      const name = attr.template?.name || 'Неизвестный атрибут'
      const value = formatAttributeValue(attr.value, attr.template?.dataType || 'STRING')
      rows.push(`Атрибут,${escapeCsvValue(name)},${escapeCsvValue(value)}`)
    })
  }
  
  // Применимость к эталонным группам
  if (options.includeApplicabilities && item.applicabilities && item.applicabilities.length > 0) {
    rows.push('') // Пустая строка для разделения
    item.applicabilities.forEach((app, index) => {
      rows.push(`Применимость ${index + 1},Деталь,${escapeCsvValue(app.part?.name || '')}`)
      rows.push(`Применимость ${index + 1},Категория,${escapeCsvValue(app.part?.partCategory?.name || '')}`)
      
      if (app.accuracy) {
        rows.push(`Применимость ${index + 1},Точность,${app.accuracy}%`)
      }
      
      if (app.notes) {
        rows.push(`Применимость ${index + 1},Примечания,${escapeCsvValue(app.notes)}`)
      }
    })
  }
  
  // Альтернативные бренды
  if (options.includeAlternatives && (item as any).alternativeItems && (item as any).alternativeItems.length > 0) {
    rows.push('') // Пустая строка для разделения
    (item as any).alternativeItems.forEach((alt: any, index: number) => {
      rows.push(`Альтернатива ${index + 1},SKU,${escapeCsvValue(alt.sku || '')}`)
      rows.push(`Альтернатива ${index + 1},Бренд,${escapeCsvValue(alt.brand?.name || '')}`)
      
      if (alt.brand?.isOem) {
        rows.push(`Альтернатива ${index + 1},OEM,Да`)
      }
    })
  }

  // Применимость к технике
  if (options.includeEquipment && (item as any).equipmentApplicabilities && (item as any).equipmentApplicabilities.length > 0) {
    rows.push('') // Пустая строка для разделения
    (item as any).equipmentApplicabilities.forEach((eq: any, index: number) => {
      rows.push(`Техника ${index + 1},Модель,${escapeCsvValue(eq.equipmentModel?.name || '')}`)
      rows.push(`Техника ${index + 1},Бренд,${escapeCsvValue(eq.equipmentModel?.brand?.name || '')}`)
      
      if (eq.notes) {
        rows.push(`Техника ${index + 1},Примечания,${escapeCsvValue(eq.notes)}`)
      }
    })
  }

  // Сравнение с эталоном
  if (options.includeComparison && item.applicabilities && item.applicabilities.length > 0) {
    const firstApp = item.applicabilities[0]
    if (firstApp.part?.attributes && firstApp.part.attributes.length > 0) {
      rows.push('') // Пустая строка для разделения
      rows.push('Сравнение,Атрибут,Эталон,Артикул,Совпадение')
      
      const itemAttrsMap = new Map(
        (item.attributes || []).map(attr => [attr.templateId, attr.value])
      )
      
      firstApp.part.attributes.forEach(partAttr => {
        const attrName = partAttr.template?.name || 'Неизвестный'
        const partValue = partAttr.value
        const itemValue = itemAttrsMap.get(partAttr.templateId) || '-'
        const match = itemValue !== '-' && itemValue.toLowerCase().trim() === partValue.toLowerCase().trim() ? 'Да' : 'Нет'
        
        rows.push(`Сравнение,${escapeCsvValue(attrName)},${escapeCsvValue(partValue)},${escapeCsvValue(itemValue)},${match}`)
      })
    }
  }
  
  // Медиа-ресурсы
  if (options.includeMedia && item.mediaAssets && item.mediaAssets.length > 0) {
    rows.push('') // Пустая строка для разделения
    item.mediaAssets.forEach((media, index) => {
      rows.push(`Медиа ${index + 1},Тип,${media.type}`)
      rows.push(`Медиа ${index + 1},URL,${escapeCsvValue(media.url)}`)
      
      if (media.fileName) {
        rows.push(`Медиа ${index + 1},Файл,${escapeCsvValue(media.fileName)}`)
      }
    })
  }
  
  return rows.join('\n')
}

/**
 * Генерирует JSON-строку с данными артикула
 */
export function generateCatalogItemJSON(
  item: CatalogItem,
  options: ExportOptions = {}
): string {
  const data: Record<string, unknown> = {
    id: item.id,
    sku: item.sku,
    brand: {
      id: item.brand?.id,
      name: item.brand?.name,
      isOem: item.brand?.isOem
    },
    description: item.description,
    isPublic: item.isPublic,
    attributes: item.attributes?.map(attr => ({
      id: attr.id,
      templateId: attr.templateId,
      templateName: attr.template?.name,
      value: attr.value,
      dataType: attr.template?.dataType,
      formattedValue: formatAttributeValue(attr.value, attr.template?.dataType || 'STRING')
    }))
  }
  
  // Применимость к эталонным группам
  if (options.includeApplicabilities && item.applicabilities) {
    data.applicabilities = item.applicabilities.map(app => ({
      partId: app.partId,
      partName: app.part?.name,
      category: app.part?.partCategory?.name,
      accuracy: app.accuracy,
      notes: app.notes
    }))
  }
  
  // Медиа-ресурсы
  if (options.includeMedia) {
    data.image = item.image ? {
      id: item.image.id,
      url: item.image.url,
      fileName: item.image.fileName,
      type: item.image.type
    } : null
    
    data.mediaAssets = item.mediaAssets?.map(media => ({
      id: media.id,
      url: media.url,
      fileName: media.fileName,
      type: media.type,
      sortOrder: media.sortOrder
    }))
  }
  
  // Альтернативные бренды
  if (options.includeAlternatives && (item as any).alternativeItems) {
    data.alternativeItems = (item as any).alternativeItems.map((alt: any) => ({
      id: alt.id,
      sku: alt.sku,
      brand: {
        id: alt.brand?.id,
        name: alt.brand?.name,
        isOem: alt.brand?.isOem
      },
      description: alt.description
    }))
  }

  // Применимость к технике
  if (options.includeEquipment && (item as any).equipmentApplicabilities) {
    data.equipmentApplicabilities = (item as any).equipmentApplicabilities.map((app: any) => ({
      equipmentId: app.equipmentModelId,
      equipmentName: app.equipmentModel?.name,
      brand: app.equipmentModel?.brand?.name,
      notes: app.notes
    }))
  }

  // Сравнение с эталоном
  if (options.includeComparison && item.applicabilities && item.applicabilities.length > 0) {
    const firstApp = item.applicabilities[0]
    if (firstApp.part?.attributes && firstApp.part.attributes.length > 0) {
      const itemAttrsMap = new Map(
        (item.attributes || []).map(attr => [attr.templateId, attr])
      )
      
      const comparison = firstApp.part.attributes.map(partAttr => {
        const itemAttr = itemAttrsMap.get(partAttr.templateId)
        const partValue = partAttr.value
        const itemValue = itemAttr?.value || null
        const match = itemValue !== null && itemValue.toLowerCase().trim() === partValue.toLowerCase().trim()
        
        return {
          attributeName: partAttr.template?.name || 'Unknown',
          templateId: partAttr.templateId,
          partValue: partValue,
          itemValue: itemValue,
          match: match
        }
      })
      
      const matchCount = comparison.filter(c => c.match).length
      const totalCount = comparison.length
      const matchScore = totalCount > 0 ? (matchCount / totalCount) * 100 : 0
      
      data.comparison = {
        partId: firstApp.partId,
        partName: firstApp.part.name,
        matchScore: Math.round(matchScore * 100) / 100,
        matchingAttributes: matchCount,
        totalAttributes: totalCount,
        attributes: comparison
      }
    }
  }
  
  return JSON.stringify(data, null, 2)
}

/**
 * Генерирует имя файла для экспорта
 */
export function generateExportFilename(
  item: CatalogItem,
  format: 'csv' | 'json'
): string {
  // Очищаем SKU от специальных символов
  const sanitizedSku = item.sku.replace(/[^a-zA-Z0-9]/g, '-')
  
  // Форматируем timestamp
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]
  
  return `catalog-item-${sanitizedSku}-${timestamp}.${format}`
}

/**
 * Инициирует скачивание файла в браузере
 */
export function downloadFile(
  content: string,
  fileName: string,
  mimeType: string
): void {
  // Создаем blob с контентом
  const blob = new Blob([content], { type: mimeType })
  
  // Создаем временную ссылку
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  
  // Добавляем в DOM, кликаем и удаляем
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  // Освобождаем память
  URL.revokeObjectURL(url)
}

