"use client"

import { useState, useMemo } from "react"
import { ChevronDown, ChevronUp, Search, X, BarChart3 } from "lucide-react"
import { motion } from "motion/react"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { ModernInput } from "@/components/ui/modern-input"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import Combobox from "@/components/ui/combobox"
import type { AttributeTemplate } from "@/types/catalog"
import { fadeInUp } from "@/lib/animation-variants"
import { calculateDistribution } from "@/lib/aggregators"

interface AttributeFilterProps {
  template: AttributeTemplate
  selectedValues: string[]
  numericRange?: [number, number]
  onValuesChange: (values: string[]) => void
  onRangeChange: (range: [number, number]) => void
  availableValues: string[]
  availableValuesWithCounts?: Array<{ value: string; count: number }>
  numericStats?: { min: number; max: number; avg: number }
}

export function AttributeFilter({ 
  template, 
  selectedValues, 
  numericRange, 
  onValuesChange, 
  onRangeChange, 
  availableValues, 
  availableValuesWithCounts,
  numericStats, 
}: AttributeFilterProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [showDistribution, setShowDistribution] = useState(false)
  const [showHistogram, setShowHistogram] = useState(false)
  const [manualMin, setManualMin] = useState<string>("")
  const [manualMax, setManualMax] = useState<string>("")

  const handleValueToggle = (value: string) => {
    if (selectedValues.includes(value)) onValuesChange(selectedValues.filter((v) => v !== value))
    else onValuesChange([...selectedValues, value])
  }

  const handleClearFilter = () => {
    onValuesChange([])
    if (numericStats) {
      onRangeChange([numericStats.min, numericStats.max])
    }
  }

  const handleRemoveValue = (value: string) => {
    onValuesChange(selectedValues.filter((v) => v !== value))
  }

  const handleManualRangeChange = () => {
    const min = parseFloat(manualMin) || numericStats?.min || 0
    const max = parseFloat(manualMax) || numericStats?.max || 100
    if (min <= max) {
      onRangeChange([min, max])
    }
  }

  const isNumeric = template.dataType === "NUMBER"
  const hasActiveFilters = selectedValues.length > 0 || (numericRange && numericStats && (numericRange[0] > numericStats.min || numericRange[1] < numericStats.max))
  const filteredValues = availableValues.filter((value) => value.toLowerCase().includes(searchQuery.toLowerCase()))
  const hasManyValues = availableValues.length > 10

  // Вычисляем распределение значений для визуализации
  const distribution = useMemo(() => {
    if (!availableValuesWithCounts) return []
    return availableValuesWithCounts
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)
  }, [availableValuesWithCounts])

  // Вычисляем гистограмму для числовых атрибутов
  const histogram = useMemo(() => {
    if (!numericStats || !availableValuesWithCounts) return []
    
    const binCount = 8
    const range = numericStats.max - numericStats.min
    const binSize = range / binCount
    const bins: Array<{ min: number; max: number; count: number }> = []

    for (let i = 0; i < binCount; i++) {
      const min = numericStats.min + i * binSize
      const max = min + binSize
      bins.push({ min, max, count: 0 })
    }

    // Подсчитываем значения в каждом бине
    availableValuesWithCounts.forEach((item) => {
      const value = parseFloat(item.value)
      if (!isNaN(value)) {
        const binIndex = Math.min(
          Math.floor((value - numericStats.min) / binSize),
          binCount - 1
        )
        if (binIndex >= 0 && binIndex < bins.length) {
          bins[binIndex].count += item.count
        }
      }
    })

    return bins
  }, [numericStats, availableValuesWithCounts])

  const maxHistogramCount = histogram.length > 0 ? Math.max(...histogram.map((h) => h.count)) : 1

  return (
    <div className="border-2 border-border-strong rounded-lg bg-card animate-theme-transition">
      <Button variant="ghost" onClick={() => setIsExpanded(!isExpanded)} className="w-full justify-between p-4 text-foreground hover:bg-accent/50 transition-colors">
        <div className="flex items-center gap-2">
          <span className="font-medium">{template.title}</span>
          {template.unit && (<Badge variant="outline" className="text-xs border-border text-muted-foreground">{template.unit}</Badge>)}
          {hasActiveFilters && (<Badge variant="default" className="text-xs bg-primary text-primary-foreground">{selectedValues.length || "range"}</Badge>)}
        </div>
        <div className="flex items-center gap-1">
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                handleClearFilter()
              }}
              className="h-7 px-2 text-xs text-muted-foreground hover:text-foreground"
            >
              Очистить
            </Button>
          )}
          {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
        </div>
      </Button>

      {isExpanded && (
        <motion.div 
          className="p-4 pt-0 space-y-3 border-t border-border"
          variants={fadeInUp}
          initial="initial"
          animate="animate"
        >
          {template.description && <p className="text-xs text-muted-foreground">{template.description}</p>}

          {/* Отображение выбранных значений как badges */}
          {selectedValues.length > 0 && (
            <div className="flex flex-wrap gap-1.5">
              {selectedValues.map((value) => (
                <Badge
                  key={value}
                  variant="secondary"
                  className="gap-1 pr-1 pl-2 py-1"
                >
                  <span className="text-xs">{value}</span>
                  <button
                    type="button"
                    onClick={() => handleRemoveValue(value)}
                    className="rounded-full hover:bg-secondary-foreground/20 p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          )}

          {numericRange && numericStats && (
            <Badge variant="secondary" className="gap-1 pr-2 pl-2 py-1">
              <span className="text-xs">
                {numericRange[0].toFixed(1)}–{numericRange[1].toFixed(1)} {template.unit}
              </span>
            </Badge>
          )}

          {isNumeric && numericStats ? (
            <div className="space-y-3">
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Диапазон: {numericStats.min.toFixed(1)} - {numericStats.max.toFixed(1)}</span>
                <span>Среднее: {numericStats.avg.toFixed(1)}</span>
              </div>

              {/* Range input fields */}
              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-1">
                  <Label className="text-xs text-muted-foreground">Мин</Label>
                  <ModernInput
                    type="number"
                    value={manualMin || numericRange?.[0] || numericStats.min}
                    onChange={(e) => setManualMin(e.target.value)}
                    onBlur={handleManualRangeChange}
                    className="h-8 text-sm"
                    min={numericStats.min}
                    max={numericStats.max}
                  />
                </div>
                <div className="space-y-1">
                  <Label className="text-xs text-muted-foreground">Макс</Label>
                  <ModernInput
                    type="number"
                    value={manualMax || numericRange?.[1] || numericStats.max}
                    onChange={(e) => setManualMax(e.target.value)}
                    onBlur={handleManualRangeChange}
                    className="h-8 text-sm"
                    min={numericStats.min}
                    max={numericStats.max}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm text-foreground">Выбранный диапазон: {(numericRange?.[0] || numericStats.min).toFixed(1)} - {(numericRange?.[1] || numericStats.max).toFixed(1)}</Label>
                <Slider 
                  value={numericRange || [numericStats.min, numericStats.max]} 
                  onValueChange={(value) => {
                    onRangeChange([value[0], value[1]])
                    setManualMin(value[0].toString())
                    setManualMax(value[1].toString())
                  }} 
                  min={numericStats.min} 
                  max={numericStats.max} 
                  step={template.tolerance || 0.1} 
                  className="w-full py-2"
                />
              </div>

              {/* Histogram visualization */}
              {histogram.length > 0 && (
                <Collapsible open={showHistogram} onOpenChange={setShowHistogram}>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" size="sm" className="w-full justify-between text-xs">
                      <div className="flex items-center gap-1">
                        <BarChart3 className="h-3 w-3" />
                        <span>Показать распределение</span>
                      </div>
                      <ChevronDown className={`h-3 w-3 transition-transform ${showHistogram ? 'rotate-180' : ''}`} />
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-2 pt-2">
                    <div className="space-y-1">
                      {histogram.map((bin, index) => {
                        const isInRange = numericRange 
                          ? bin.min >= numericRange[0] && bin.max <= numericRange[1]
                          : true
                        const barHeight = (bin.count / maxHistogramCount) * 100
                        return (
                          <div key={index} className="flex items-end gap-2">
                            <div className="text-xs text-muted-foreground w-20 text-right">
                              {bin.min.toFixed(1)}–{bin.max.toFixed(1)}
                            </div>
                            <div className="flex-1 h-6 bg-accent rounded relative overflow-hidden">
                              <motion.div
                                className={`h-full rounded ${
                                  isInRange ? 'bg-primary' : 'bg-accent-foreground/20'
                                }`}
                                initial={{ width: 0 }}
                                animate={{ width: `${barHeight}%` }}
                                transition={{ duration: 0.3, delay: index * 0.05 }}
                              />
                            </div>
                            <div className="text-xs text-muted-foreground w-8">
                              {bin.count}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              )}

              {template.tolerance && (<p className="text-xs text-muted-foreground">Допуск: ±{template.tolerance} {template.unit}</p>)}
            </div>
          ) : (
            <div className="space-y-3">
              {/* Use Combobox for many values, checkboxes for few */}
              {hasManyValues ? (
                <Combobox
                  options={availableValues.map((value) => {
                    const withCount = availableValuesWithCounts?.find((v) => v.value === value)
                    return {
                      value,
                      label: value,
                      count: withCount?.count,
                    }
                  })}
                  value={selectedValues}
                  onValueChange={onValuesChange}
                  placeholder="Выберите значения..."
                  searchPlaceholder="Поиск значений..."
                  emptyMessage="Ничего не найдено"
                  showCount={true}
                  className="w-full"
                />
              ) : (
                <>
                  {availableValues.length > 5 && (
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground" />
                      <ModernInput placeholder="Поиск значений..." value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} className="pl-9 h-8 text-sm" variant="ghost" />
                    </div>
                  )}

                  <div className="space-y-3 max-h-48 overflow-y-auto">
                    {filteredValues.map((value) => {
                      const withCount = availableValuesWithCounts?.find((v) => v.value === value)
                      return (
                        <div key={value} className="flex items-center space-x-2 group p-2 rounded hover:bg-accent/50 transition-colors">
                          <Checkbox 
                            id={`${template.id}-${value}`} 
                            checked={selectedValues.includes(value)} 
                            onCheckedChange={() => handleValueToggle(value)} 
                            className="h-4 w-4 border-border data-[state=checked]:bg-primary data-[state=checked]:border-primary" 
                          />
                          <Label htmlFor={`${template.id}-${value}`} className="text-sm text-foreground cursor-pointer flex-1 group-hover:text-primary transition-colors">
                            {value}
                          </Label>
                          {withCount && (
                            <Badge variant="outline" className="text-xs h-5 px-1.5">
                              {withCount.count}
                            </Badge>
                          )}
                        </div>
                      )
                    })}
                    {filteredValues.length === 0 && searchQuery && (<p className="text-sm text-muted-foreground text-center py-2">Ничего не найдено по запросу "{searchQuery}"</p>)}
                    {availableValues.length === 0 && (<p className="text-sm text-muted-foreground text-center py-2">Нет доступных значений</p>)}
                  </div>
                </>
              )}

              {/* Distribution visualization */}
              {distribution.length > 0 && (
                <Collapsible open={showDistribution} onOpenChange={setShowDistribution}>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" size="sm" className="w-full justify-between text-xs">
                      <div className="flex items-center gap-1">
                        <BarChart3 className="h-3 w-3" />
                        <span>Показать распределение значений</span>
                      </div>
                      <ChevronDown className={`h-3 w-3 transition-transform ${showDistribution ? 'rotate-180' : ''}`} />
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-2 pt-2">
                    <p className="text-xs text-muted-foreground">Топ 5 значений:</p>
                    <div className="space-y-1.5">
                      {distribution.map((item, index) => {
                        const maxCount = distribution[0]?.count || 1
                        const percentage = (item.count / maxCount) * 100
                        return (
                          <div key={index} className="flex items-center gap-2">
                            <div className="text-xs text-muted-foreground w-24 truncate">
                              {item.value}
                            </div>
                            <div className="flex-1 h-5 bg-accent rounded relative overflow-hidden">
                              <motion.div
                                className="h-full bg-primary rounded"
                                initial={{ width: 0 }}
                                animate={{ width: `${percentage}%` }}
                                transition={{ duration: 0.3, delay: index * 0.05 }}
                              />
                            </div>
                            <div className="text-xs text-muted-foreground w-8 text-right">
                              {item.count}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              )}
            </div>
          )}
        </motion.div>
      )}
    </div>
  )
}

