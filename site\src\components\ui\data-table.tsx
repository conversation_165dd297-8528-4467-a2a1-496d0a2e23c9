"use client"

import * as React from "react"
import { useMemo, useRef } from "react"
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import type { ColumnDef, SortingState } from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { ModernButton } from "@/components/ui/modern-button"
import { useTableVirtualization } from "@/hooks/useVirtualization"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  onSortingChange?: (sorting: SortingState) => void
  onRowSelectionChange?: (rowSelection: Record<string, boolean>) => void
  enableVirtualization?: boolean // Включить виртуализацию (default: true для >50 строк)
  rowHeight?: number // Высота строки (default: 53px)
}

export function DataTable<TData, TValue>({
  columns,
  data,
  onSortingChange,
  onRowSelectionChange,
  enableVirtualization,
  rowHeight,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [rowSelection, setRowSelection] = React.useState({})

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: (updaterOrValue) => {
      setSorting(updaterOrValue)
      if (onSortingChange) {
        const newSorting = typeof updaterOrValue === 'function' ? updaterOrValue(sorting) : updaterOrValue
        onSortingChange(newSorting)
      }
    },
    getSortedRowModel: getSortedRowModel(),
    onRowSelectionChange: (updaterOrValue) => {
      setRowSelection(updaterOrValue)
      if (onRowSelectionChange) {
        const newRowSelection = typeof updaterOrValue === 'function' ? updaterOrValue(rowSelection) : updaterOrValue
        onRowSelectionChange(newRowSelection)
      }
    },
    state: {
      sorting,
      rowSelection,
    },
  })

  const shouldVirtualize = enableVirtualization !== false && data.length > 50

  const { virtualizer, virtualItems, totalSize, containerRef } = useTableVirtualization({
    items: table.getRowModel().rows,
    rowHeight: rowHeight || 53,
    enabled: shouldVirtualize,
    overscan: 10,
  })

  return (
    <div>
      <div className="border rounded-lg relative" ref={containerRef}>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {shouldVirtualize ? (
              <>
                {/* Spacer для виртуальной высоты */}
                {totalSize > 0 && <tr style={{ height: `${totalSize}px` }} />}
                
                {/* Виртуализированные строки */}
                {(virtualItems as any[]).map((virtualRow) => {
                  const row = table.getRowModel().rows[virtualRow.index]
                  if (!row) return null
                  
                  return (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      data-index={virtualRow.index}
                      ref={virtualizer?.measureElement}
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        transform: `translateY(${virtualRow.start}px)`,
                      }}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  )
                })}
              </>
            ) : (
              // Fallback для малых таблиц - существующий код
              table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    Нет данных.
                  </TableCell>
                </TableRow>
              )
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <ModernButton
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          Назад
        </ModernButton>
        <ModernButton
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          Вперед
        </ModernButton>
      </div>
    </div>
  )
}
