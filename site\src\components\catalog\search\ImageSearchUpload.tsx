import { useState, useRef } from 'react'
import { Upload, X, Image as ImageIcon, AlertCircle } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import LoadingState from '@/components/shared/LoadingState'
import ErrorState from '@/components/shared/ErrorState'
import { ModernCard } from '@/components/ui/modern-card'
import { cn } from '@/lib/utils'
import { fadeInUp } from '@/lib/animation-variants'

export interface ImageSearchUploadProps {
  onImageSearch: (file: File) => void // Упростить - передаем только File
  isLoading?: boolean
  error?: string
  className?: string
}

const MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB
const ACCEPTED_TYPES = ['image/jpeg', 'image/png', 'image/webp']
const MAX_DIMENSION = 1024

export default function ImageSearchUpload({
  onImageSearch,
  isLoading = false,
  error: externalError,
  className
}: ImageSearchUploadProps) {
  const [preview, setPreview] = useState<string | null>(null)
  const [fileName, setFileName] = useState<string>('')
  const [fileSize, setFileSize] = useState<number>(0)
  const [error, setError] = useState<string | null>(externalError ?? null)
  const [isDragging, setIsDragging] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [currentFile, setCurrentFile] = useState<File | null>(null)

  const validateFile = (file: File): string | null => {
    if (!ACCEPTED_TYPES.includes(file.type)) {
      return 'Неподдерживаемый формат изображения. Используйте JPEG, PNG или WebP.'
    }

    if (file.size > MAX_FILE_SIZE) {
      return 'Файл слишком большой. Максимальный размер: 5 МБ.'
    }

    return null
  }

  const handleFileSelect = (file: File) => {
    setError(null)
    const validationError = validateFile(file)
    if (validationError) {
      setError(validationError)
      return
    }

    const reader = new FileReader()
    reader.onloadend = () => {
      setPreview(reader.result as string)
      setFileName(file.name)
      setFileSize(file.size)
      setCurrentFile(file)
    }
    reader.onerror = () => {
      setError('Не удалось прочитать файл')
    }
    reader.readAsDataURL(file)
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    const file = e.dataTransfer.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleRemove = () => {
    setPreview(null)
    setFileName('')
    setFileSize(0)
    setCurrentFile(null)
    setError(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

// Image matching выполняется на бэкенде
// Будущее: интеграция с computer vision API или MeiliSearch image search
  const handleSearch = () => {
    if (currentFile) {
      onImageSearch(currentFile)
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} Б`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} КБ`
    return `${(bytes / (1024 * 1024)).toFixed(1)} МБ`
  }

  if (isLoading) {
    return (
      <div className={cn('w-full', className)}>
        <LoadingState message="Анализ изображения..." variant="card" />
      </div>
    )
  }

  return (
    <div className={cn('w-full space-y-4', className)}>
      <input
        ref={fileInputRef}
        type="file"
        accept={ACCEPTED_TYPES.join(',')}
        onChange={handleFileInputChange}
        className="hidden"
      />

      {!preview ? (
        <motion.div
          variants={fadeInUp}
          initial="initial"
          animate="animate"
        >
          <ModernCard
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
            className={cn(
              'cursor-pointer transition-all duration-200',
              isDragging ? 'border-primary bg-primary/5 scale-105' : 'border-dashed hover:border-primary/50'
            )}
          >
            <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
              <div className={cn(
                'w-16 h-16 rounded-full flex items-center justify-center mb-4 transition-colors',
                isDragging ? 'bg-primary text-primary-foreground' : 'bg-muted'
              )}>
                <Upload className="w-8 h-8" />
              </div>
              
              <h3 className="text-lg font-semibold mb-2">
                {isDragging ? 'Отпустите файл' : 'Загрузите изображение запчасти'}
              </h3>
              
              <p className="text-sm text-muted-foreground mb-4">
                Перетащите изображение сюда или нажмите для выбора файла
              </p>

              <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
                <span>Поддерживаемые форматы: JPEG, PNG, WebP</span>
                <span>•</span>
                <span>Максимальный размер: 5 МБ</span>
              </div>
            </div>
          </ModernCard>
        </motion.div>
      ) : (
        <motion.div
          variants={fadeInUp}
          initial="initial"
          animate="animate"
          className="space-y-4"
        >
          <ModernCard>
            <div className="relative">
              <img
                src={preview}
                alt="Превью"
                className="w-full max-h-96 object-contain rounded-lg"
              />
              <button
                onClick={handleRemove}
                disabled={isLoading}
                className="absolute top-2 right-2 p-2 bg-background/80 backdrop-blur-sm hover:bg-background rounded-full transition-colors"
                aria-label="Удалить изображение"
              >
                <X className="w-4 h-4" />
              </button>
            </div>

            <div className="mt-4 space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <ImageIcon className="w-4 h-4 text-muted-foreground" />
                <span className="font-medium">{fileName}</span>
              </div>
              <p className="text-xs text-muted-foreground">
                Размер: {formatFileSize(fileSize)}
              </p>
            </div>

            <button
              onClick={handleSearch}
              disabled={isLoading}
              className="w-full mt-4 px-4 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Найти похожие запчасти
            </button>
          </ModernCard>
        </motion.div>
      )}

      <AnimatePresence>
        {error && (
          <motion.div
            variants={fadeInUp}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            <ErrorState
              title="Ошибка загрузки"
              message={error}
              icon={<AlertCircle className="w-12 h-12" />}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {!preview && (
        <div className="p-4 bg-muted/50 rounded-lg">
          <h4 className="text-sm font-semibold mb-2">Примеры использования:</h4>
          <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
            <li>Сфотографируйте нужную запчасть</li>
            <li>Загрузите изображение из каталога</li>
            <li>Найдите аналоги по внешнему виду</li>
          </ul>
        </div>
      )}
    </div>
  )
}

