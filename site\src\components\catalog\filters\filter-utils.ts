/**
 * Утилиты для управления фильтрами каталога
 * Включает управление пресетами, валидацию и быстрые фильтры
 */

import type { CatalogSearchFilters } from '@/types/catalog';
import type { FilterPreset, QuickFilter, FilterValidationResult } from '@/types/filters';
import { catalogSearchFiltersSchema, filterPresetSchema } from '@/lib/validators';

// ============================================================================
// Constants
// ============================================================================

export const PRESETS_STORAGE_KEY = 'catalog-filter-presets';
export const PRESET_VERSION = 1;

// ============================================================================
// Preset Management
// ============================================================================

/**
 * Создает и сохраняет новый пресет фильтров в localStorage
 */
export function saveFilterPreset(
  name: string,
  description: string | undefined,
  filters: CatalogSearchFilters
): FilterPreset {
  const preset: FilterPreset = {
    id: `preset-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    name: name.trim(),
    description: description?.trim(),
    filters,
    isDefault: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Валидация пресета
  const validation = filterPresetSchema.safeParse(preset);
  if (!validation.success) {
    console.error('Invalid preset:', validation.error);
    throw new Error('Некорректные данные пресета');
  }

  // Загружаем существующие пресеты
  const presets = loadFilterPresets();

  // Проверяем уникальность имени
  if (presets.some((p) => p.name === preset.name)) {
    throw new Error('Пресет с таким именем уже существует');
  }

  // Добавляем новый пресет
  presets.push(preset);

  // Сохраняем в localStorage
  try {
    localStorage.setItem(
      PRESETS_STORAGE_KEY,
      JSON.stringify({
        version: PRESET_VERSION,
        presets,
      })
    );
  } catch (error) {
    console.error('Failed to save preset:', error);
    throw new Error('Не удалось сохранить пресет');
  }

  return preset;
}

/**
 * Загружает все сохраненные пресеты из localStorage
 */
export function loadFilterPresets(): FilterPreset[] {
  if (typeof window === 'undefined') {
    return [];
  }

  try {
    const stored = localStorage.getItem(PRESETS_STORAGE_KEY);
    if (!stored) {
      return [];
    }

    const parsed = JSON.parse(stored);

    // Проверяем версию
    if (parsed.version !== PRESET_VERSION) {
      console.warn('Preset version mismatch, clearing presets');
      localStorage.removeItem(PRESETS_STORAGE_KEY);
      return [];
    }

    // Восстанавливаем даты
    const presets = (parsed.presets || []).map((preset: FilterPreset) => ({
      ...preset,
      createdAt: new Date(preset.createdAt),
      updatedAt: new Date(preset.updatedAt),
    }));

    return presets;
  } catch (error) {
    console.error('Failed to load presets:', error);
    return [];
  }
}

/**
 * Удаляет пресет из localStorage
 */
export function deleteFilterPreset(id: string): void {
  const presets = loadFilterPresets();
  const filtered = presets.filter((p) => p.id !== id);

  try {
    localStorage.setItem(
      PRESETS_STORAGE_KEY,
      JSON.stringify({
        version: PRESET_VERSION,
        presets: filtered,
      })
    );
  } catch (error) {
    console.error('Failed to delete preset:', error);
    throw new Error('Не удалось удалить пресет');
  }
}

/**
 * Обновляет существующий пресет
 */
export function updateFilterPreset(
  id: string,
  updates: Partial<Omit<FilterPreset, 'id' | 'createdAt'>>
): FilterPreset {
  const presets = loadFilterPresets();
  const index = presets.findIndex((p) => p.id === id);

  if (index === -1) {
    throw new Error('Пресет не найден');
  }

  const updated: FilterPreset = {
    ...presets[index],
    ...updates,
    id, // Сохраняем оригинальный ID
    createdAt: presets[index].createdAt, // Сохраняем дату создания
    updatedAt: new Date(),
  };

  // Валидация
  const validation = filterPresetSchema.safeParse(updated);
  if (!validation.success) {
    throw new Error('Некорректные данные пресета');
  }

  presets[index] = updated;

  try {
    localStorage.setItem(
      PRESETS_STORAGE_KEY,
      JSON.stringify({
        version: PRESET_VERSION,
        presets,
      })
    );
  } catch (error) {
    console.error('Failed to update preset:', error);
    throw new Error('Не удалось обновить пресет');
  }

  return updated;
}

/**
 * Устанавливает пресет как пресет по умолчанию
 */
export function setDefaultPreset(id: string): void {
  const presets = loadFilterPresets();

  // Снимаем флаг default со всех пресетов
  const updated = presets.map((p) => ({
    ...p,
    isDefault: p.id === id,
    updatedAt: p.id === id ? new Date() : p.updatedAt,
  }));

  try {
    localStorage.setItem(
      PRESETS_STORAGE_KEY,
      JSON.stringify({
        version: PRESET_VERSION,
        presets: updated,
      })
    );
  } catch (error) {
    console.error('Failed to set default preset:', error);
    throw new Error('Не удалось установить пресет по умолчанию');
  }
}

/**
 * Получает пресет по умолчанию
 */
export function getDefaultPreset(): FilterPreset | null {
  const presets = loadFilterPresets();
  return presets.find((p) => p.isDefault) || null;
}

// ============================================================================
// Filter Calculations
// ============================================================================

/**
 * Подсчитывает количество активных фильтров
 */
export function calculateActiveFiltersCount(filters: CatalogSearchFilters): number {
  let count = 0;

  // Поисковый запрос
  if (filters.query && filters.query.trim() !== '') {
    count++;
  }

  // Категории
  if (filters.categoryIds && filters.categoryIds.length > 0) {
    count++;
  }

  // Бренды
  if (filters.brandIds && filters.brandIds.length > 0) {
    count++;
  }

  // OEM only
  if (filters.isOemOnly) {
    count++;
  }

  // Фильтры атрибутов
  if (filters.attributeFilters) {
    const attrCount = Object.values(filters.attributeFilters).filter((filter) => {
      // Проверяем, есть ли выбранные значения
      if (filter.values && filter.values.length > 0) return true;
      // Проверяем, установлен ли числовой диапазон
      if (filter.numericRange) {
        const [min, max] = filter.numericRange;
        if (min !== undefined || max !== undefined) return true;
      }
      return false;
    }).length;
    count += attrCount;
  }

  // Точность совпадения (accuracy)
  if (filters.accuracy && filters.accuracy.length > 0) {
    count++;
  }

  // Фильтр по наличию изображений
  if (filters.hasImages) {
    count++;
  }

  // Примечание: sortBy и sortDir не учитываются, так как это параметры сортировки, а не фильтрации

  return count;
}

/**
 * Глубокое сравнение двух объектов фильтров
 */
export function areFiltersEqual(
  a: CatalogSearchFilters,
  b: CatalogSearchFilters
): boolean {
  // Сравниваем примитивы
  if (a.query !== b.query) return false;
  if (a.isOemOnly !== b.isOemOnly) return false;
  if (a.sortBy !== b.sortBy) return false;
  if (a.sortDir !== b.sortDir) return false;
  if (a.hasImages !== b.hasImages) return false;

  // Сравниваем массивы категорий
  if (a.categoryIds.length !== b.categoryIds.length) return false;
  const sortedCategoriesA = [...a.categoryIds].sort();
  const sortedCategoriesB = [...b.categoryIds].sort();
  if (!sortedCategoriesA.every((id, i) => id === sortedCategoriesB[i])) return false;

  // Сравниваем массивы брендов
  if (a.brandIds.length !== b.brandIds.length) return false;
  const sortedBrandsA = [...a.brandIds].sort();
  const sortedBrandsB = [...b.brandIds].sort();
  if (!sortedBrandsA.every((id, i) => id === sortedBrandsB[i])) return false;

  // Сравниваем массивы accuracy
  const accuracyA = a.accuracy || [];
  const accuracyB = b.accuracy || [];
  if (accuracyA.length !== accuracyB.length) return false;
  const sortedAccuracyA = [...accuracyA].sort();
  const sortedAccuracyB = [...accuracyB].sort();
  if (!sortedAccuracyA.every((acc, i) => acc === sortedAccuracyB[i])) return false;

  // Сравниваем фильтры атрибутов
  const keysA = Object.keys(a.attributeFilters || {});
  const keysB = Object.keys(b.attributeFilters || {});
  if (keysA.length !== keysB.length) return false;

  for (const key of keysA) {
    const numKey = Number(key);
    const filterA = a.attributeFilters[numKey];
    const filterB = b.attributeFilters[numKey];

    if (!filterB) return false;

    // Сравниваем values
    if (filterA.values && filterB.values) {
      if (filterA.values.length !== filterB.values.length) return false;
      const sortedA = [...filterA.values].sort();
      const sortedB = [...filterB.values].sort();
      if (!sortedA.every((v, i) => v === sortedB[i])) return false;
    } else if (filterA.values || filterB.values) {
      return false;
    }

    // Сравниваем numericRange
    if (filterA.numericRange && filterB.numericRange) {
      if (
        filterA.numericRange[0] !== filterB.numericRange[0] ||
        filterA.numericRange[1] !== filterB.numericRange[1]
      ) {
        return false;
      }
    } else if (filterA.numericRange || filterB.numericRange) {
      return false;
    }
  }

  return true;
}

/**
 * Объединяет два объекта фильтров
 */
export function mergeFilters(
  base: CatalogSearchFilters,
  updates: Partial<CatalogSearchFilters>
): CatalogSearchFilters {
  return {
    query: updates.query !== undefined ? updates.query : base.query,
    categoryIds:
      updates.categoryIds !== undefined ? updates.categoryIds : base.categoryIds,
    brandIds: updates.brandIds !== undefined ? updates.brandIds : base.brandIds,
    isOemOnly: updates.isOemOnly !== undefined ? updates.isOemOnly : base.isOemOnly,
    attributeFilters:
      updates.attributeFilters !== undefined
        ? { ...base.attributeFilters, ...updates.attributeFilters }
        : base.attributeFilters,
    sortBy: updates.sortBy !== undefined ? updates.sortBy : base.sortBy,
    sortDir: updates.sortDir !== undefined ? updates.sortDir : base.sortDir,
    accuracy: updates.accuracy !== undefined ? updates.accuracy : base.accuracy,
    hasImages: updates.hasImages !== undefined ? updates.hasImages : base.hasImages,
  };
}

/**
 * Сравнивает фильтры только по тем полям, которые определены в partial объекте
 * Используется для проверки активности quick filters
 */
export function areFiltersPartiallyEqual(
  filters: CatalogSearchFilters,
  partial: Partial<CatalogSearchFilters>
): boolean {
  // Для каждого ключа в partial сравниваем значения
  for (const key of Object.keys(partial) as Array<keyof CatalogSearchFilters>) {
    const partialValue = partial[key];
    const filterValue = filters[key];

    // Пропускаем undefined значения в partial
    if (partialValue === undefined) continue;

    // Сравниваем примитивы
    if (key === 'query' || key === 'isOemOnly' || key === 'sortBy' || key === 'sortDir' || key === 'hasImages') {
      if (filterValue !== partialValue) return false;
    }

    // Сравниваем массивы categoryIds, brandIds, accuracy
    if (key === 'categoryIds' || key === 'brandIds' || key === 'accuracy') {
      const arr1 = filterValue as number[] | string[] | undefined;
      const arr2 = partialValue as number[] | string[] | undefined;
      
      if (!arr1 && !arr2) continue;
      if (!arr1 || !arr2) return false;
      if (arr1.length !== arr2.length) return false;
      
      const sorted1 = [...arr1].sort();
      const sorted2 = [...arr2].sort();
      if (!sorted1.every((v, i) => v === sorted2[i])) return false;
    }

    // Сравниваем attributeFilters
    if (key === 'attributeFilters') {
      const obj1 = filterValue as CatalogSearchFilters['attributeFilters'];
      const obj2 = partialValue as Partial<CatalogSearchFilters>['attributeFilters'];
      
      if (!obj2) continue;
      
      // Проверяем только те ключи, которые определены в obj2
      for (const attrKey of Object.keys(obj2)) {
        const numKey = Number(attrKey);
        const filter1 = obj1[numKey];
        const filter2 = obj2[numKey];
        
        if (!filter1 && !filter2) continue;
        if (!filter1 || !filter2) return false;

        // Сравниваем values
        if (filter2.values) {
          if (!filter1.values) return false;
          if (filter1.values.length !== filter2.values.length) return false;
          const sorted1 = [...filter1.values].sort();
          const sorted2 = [...filter2.values].sort();
          if (!sorted1.every((v, i) => v === sorted2[i])) return false;
        }

        // Сравниваем numericRange
        if (filter2.numericRange) {
          if (!filter1.numericRange) return false;
          if (
            filter1.numericRange[0] !== filter2.numericRange[0] ||
            filter1.numericRange[1] !== filter2.numericRange[1]
          ) {
            return false;
          }
        }
      }
    }
  }

  return true;
}

/**
 * Проверяет, отличаются ли текущие фильтры от пресета
 */
export function isFilterDirty(
  current: CatalogSearchFilters,
  preset: FilterPreset
): boolean {
  return !areFiltersEqual(current, preset.filters);
}

// ============================================================================
// Quick Filters
// ============================================================================

/**
 * Возвращает массив предустановленных быстрых фильтров
 * Каждый фильтр применяет значимые изменения к CatalogSearchFilters
 */
export function getQuickFilters(): QuickFilter[] {
  return [
    {
      id: 'oem-only',
      label: 'Только OEM',
      icon: 'Shield',
      badge: 'OEM',
      filters: {
        isOemOnly: true,
      },
    },
    {
      id: 'recent',
      label: 'Недавние',
      icon: 'Clock',
      badge: 'Новое',
      filters: {
        sortBy: 'updatedAt',
        sortDir: 'desc',
      },
    },
    {
      id: 'popular-brands',
      label: 'Популярные бренды',
      icon: 'TrendingUp',
      badge: 'Топ',
      filters: {
        // brandIds будут заполнены динамически на основе популярности
        // Здесь можно предзаполнить известные OEM бренды или топ бренды
        // Например: brandIds: [1, 2, 3, 4, 5] для топ-5 популярных брендов
        isOemOnly: true, // Используем OEM как прокси для популярных брендов
      },
    },
    {
      id: 'with-images',
      label: 'С изображениями',
      icon: 'Image',
      filters: {
        hasImages: true,
      },
    },
    {
      id: 'exact-match',
      label: 'Точное совпадение',
      icon: 'Target',
      filters: {
        accuracy: ['EXACT_MATCH'],
      },
    },
  ];
}

// ============================================================================
// Validation
// ============================================================================

/**
 * Валидирует структуру фильтров
 */
export function validateFilters(
  filters: CatalogSearchFilters
): FilterValidationResult {
  const result = catalogSearchFiltersSchema.safeParse(filters);

  if (result.success) {
    return {
      isValid: true,
      errors: {},
      warnings: {},
    };
  }

  // Группируем ошибки по полям
  const errors: Record<string, string[]> = {};
  for (const error of result.error.errors) {
    const field = error.path.join('.');
    if (!errors[field]) {
      errors[field] = [];
    }
    errors[field].push(error.message);
  }

  return {
    isValid: false,
    errors,
    warnings: {},
  };
}

/**
 * Очищает фильтры от невалидных значений
 */
export function sanitizeFilters(
  filters: CatalogSearchFilters
): CatalogSearchFilters {
  const sanitized: CatalogSearchFilters = {
    query: filters.query || '',
    categoryIds: Array.isArray(filters.categoryIds) ? filters.categoryIds : [],
    brandIds: Array.isArray(filters.brandIds) ? filters.brandIds : [],
    isOemOnly: Boolean(filters.isOemOnly),
    attributeFilters: {},
  };

  // Очищаем фильтры атрибутов
  if (filters.attributeFilters && typeof filters.attributeFilters === 'object') {
    for (const [key, value] of Object.entries(filters.attributeFilters)) {
      const numKey = Number(key);
      if (isNaN(numKey)) continue;

      const sanitizedFilter: {
        values?: string[];
        numericRange?: [number | undefined, number | undefined];
      } = {};

      // Очищаем values
      if (value.values && Array.isArray(value.values)) {
        const validValues = value.values.filter((v) => typeof v === 'string');
        if (validValues.length > 0) {
          sanitizedFilter.values = validValues;
        }
      }

      // Очищаем numericRange
      if (value.numericRange && Array.isArray(value.numericRange)) {
        const [min, max] = value.numericRange;
        const validMin = typeof min === 'number' ? min : undefined;
        const validMax = typeof max === 'number' ? max : undefined;

        // Проверяем, что min <= max
        if (
          validMin !== undefined &&
          validMax !== undefined &&
          validMin > validMax
        ) {
          // Меняем местами
          sanitizedFilter.numericRange = [validMax, validMin];
        } else {
          sanitizedFilter.numericRange = [validMin, validMax];
        }
      }

      // Добавляем только если есть данные
      if (sanitizedFilter.values || sanitizedFilter.numericRange) {
        sanitized.attributeFilters[numKey] = sanitizedFilter;
      }
    }
  }

  return sanitized;
}

