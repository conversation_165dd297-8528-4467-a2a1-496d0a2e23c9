"use client";

import { useState } from "react";
import { BookType } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { formatAttributeValue, formatTolerance } from "@/lib/formatters";
import { analytics } from "@/lib/analytics";

// ============================================================================
// Types
// ============================================================================

type SynonymGroup = {
  id: number;
  name: string;
  notes: string | null;
  compatibilityLevel: string;
  canonicalValue: string | null;
  synonyms: Array<{ id: number; value: string }>;
};

type AttributeTemplate = {
  title: string;
  name: string;
  dataType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'JSON';
  unit: string | null;
  tolerance: number | null;
  synonymGroups?: SynonymGroup[];
};

type DisplayAttribute = {
  id: number;
  templateId: number;
  value: string;
  template: AttributeTemplate;
};

export interface AttributeDisplayProps {
  attributes: DisplayAttribute[];
  layout?: 'list' | 'grid' | 'compact';
  maxVisible?: number;
  showSynonyms?: boolean;
  showTolerance?: boolean;
  onAttributeClick?: (attributeId: number) => void;
  className?: string;
  // Analytics context
  contextId?: number; // partId or itemId for analytics
  contextType?: 'part' | 'catalogItem';
}

// ============================================================================
// Synonym Popover Component
// ============================================================================

interface SynonymPopoverProps {
  template: AttributeTemplate;
  contextId?: number;
}

function SynonymPopover({ template, contextId }: SynonymPopoverProps) {
  if (!template.synonymGroups || template.synonymGroups.length === 0) {
    return null;
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <button
          className="text-muted-foreground hover:text-primary transition-colors"
          onClick={() => {
            if (contextId) {
              analytics.synonymPopoverOpened(template.title, contextId);
            }
          }}
          aria-label={`Показать группы синонимов для ${template.title}`}
        >
          <BookType className="h-3.5 w-3.5" />
        </button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="grid gap-4">
          <div className="space-y-2">
            <h4 className="font-medium leading-none">
              Группы синонимов для "{template.title}"
            </h4>
            <p className="text-sm text-muted-foreground">
              Правила эквивалентности для строковых значений.
            </p>
          </div>
          <div className="grid gap-2">
            {template.synonymGroups.map((group) => (
              <div key={group.id} className="text-xs p-2 rounded bg-accent/50 border">
                <p className="font-semibold">
                  {group.name}{' '}
                  <span className="text-muted-foreground font-normal">
                    ({group.compatibilityLevel})
                  </span>
                </p>
                {group.notes && (
                  <p className="text-muted-foreground italic mt-1 mb-2">
                    "{group.notes}"
                  </p>
                )}
                <div className="flex flex-wrap gap-1 mt-1">
                  {group.synonyms.map((s) => (
                    <Badge key={s.id} variant="secondary">
                      {s.value}
                    </Badge>
                  ))}
                  {group.canonicalValue && (
                    <Badge variant="outline" title="Каноническое значение">
                      {group.canonicalValue}
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

// ============================================================================
// Attribute Display Layouts
// ============================================================================

interface AttributeItemProps {
  attr: DisplayAttribute;
  showSynonyms: boolean;
  showTolerance: boolean;
  contextId?: number;
  onClick?: () => void;
}

function ListAttributeItem({
  attr,
  showSynonyms,
  showTolerance,
  contextId,
  onClick,
}: AttributeItemProps) {
  const formattedValue = formatAttributeValue(attr.value, attr.template.unit);
  const hasToleranceDisplay =
    showTolerance &&
    attr.template.dataType === 'NUMBER' &&
    attr.template.tolerance !== null &&
    attr.template.tolerance > 0;

  return (
    <div
      className="flex items-center justify-between text-sm p-2 rounded bg-muted/50"
      onClick={onClick}
    >
      <div className="flex items-center gap-2">
        <span className="text-muted-foreground">{attr.template.title}</span>
        {showSynonyms && <SynonymPopover template={attr.template} contextId={contextId} />}
      </div>
      <div className="font-semibold text-right">
        {hasToleranceDisplay 
          ? formatTolerance(Number(attr.value), attr.template.tolerance!, attr.template.unit ?? undefined)
          : formattedValue
        }
      </div>
    </div>
  );
}

function GridAttributeItem({ attr, onClick }: { attr: DisplayAttribute; onClick?: () => void }) {
  const formattedValue = formatAttributeValue(attr.value, attr.template.unit);

  return (
    <div
      className="p-2 rounded bg-muted/50 border border-border"
      onClick={onClick}
    >
      <div className="text-xs text-muted-foreground mb-1">{attr.template.title}</div>
      <div className="text-sm font-semibold truncate">{formattedValue}</div>
    </div>
  );
}

// ============================================================================
// Main Component
// ============================================================================

export default function AttributeDisplay({
  attributes,
  layout = 'list',
  maxVisible = 3,
  showSynonyms = false,
  showTolerance = false,
  onAttributeClick,
  className = '',
  contextId,
  contextType,
}: AttributeDisplayProps) {
  // Responsive layout: force list on mobile
  const [isMobile] = useState(() => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth < 640;
  });

  const effectiveLayout = isMobile ? 'list' : layout;

  // Compact layout: show only first N attributes
  const visibleAttributes =
    effectiveLayout === 'compact' ? attributes.slice(0, maxVisible) : attributes;
  const remainingCount = attributes.length - maxVisible;

  // Handle attribute click
  const handleAttributeClick = (attrId: number) => {
    if (onAttributeClick) {
      onAttributeClick(attrId);
    }
  };

  // List layout
  if (effectiveLayout === 'list') {
    return (
      <div className={`space-y-2 ${className}`}>
        {attributes.map((attr) => (
          <ListAttributeItem
            key={attr.id}
            attr={attr}
            showSynonyms={showSynonyms}
            showTolerance={showTolerance}
            contextId={contextId}
            onClick={() => handleAttributeClick(attr.id)}
          />
        ))}
      </div>
    );
  }

  // Grid layout
  if (effectiveLayout === 'grid') {
    return (
      <div className={`grid grid-cols-2 md:grid-cols-3 gap-2 ${className}`}>
        {attributes.map((attr) => (
          <GridAttributeItem
            key={attr.id}
            attr={attr}
            onClick={() => handleAttributeClick(attr.id)}
          />
        ))}
      </div>
    );
  }

  // Compact layout
  if (effectiveLayout === 'compact') {
    return (
      <div className={`flex flex-wrap gap-2 ${className}`}>
        {visibleAttributes.map((attr) => {
          const formattedValue = formatAttributeValue(attr.value, attr.template.unit);
          return (
            <Badge
              key={attr.id}
              variant="outline"
              className="cursor-pointer hover:bg-accent"
              onClick={() => handleAttributeClick(attr.id)}
            >
              {attr.template.title}: {formattedValue}
            </Badge>
          );
        })}
        {remainingCount > 0 && (
          <Badge variant="outline" className="text-muted-foreground">
            +{remainingCount} еще
          </Badge>
        )}
      </div>
    );
  }

  return null;
}

