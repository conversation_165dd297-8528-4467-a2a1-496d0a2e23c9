---
import MainLayout from "../../../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/clients";
import CatalogItemIsland from "@/components/catalog/islands/CatalogItemIsland";

const { id } = Astro.params;
if (!id) return Astro.redirect('/catalog');

const itemId = Number(id);
if (isNaN(itemId)) return Astro.redirect('/catalog');

const user = Astro.locals.user
const subscription = (user as any)?.subscription
const isPro = subscription === 'PRO'

let item: any = null; // Загрузим полные данные
try {
  item = await trpcClient.crud.catalogItem.findUnique.query({
    where: { id: itemId },
    include: {
      brand: true,
      attributes: {
        include: {
          template: {
            include: {
              synonymGroups: {
                include: {
                  synonyms: true
                }
              }
            }
          },
        },
      },
      // Загружаем applicabilities: все для PRO, первую для FREE (для preview сравнения)
      applicabilities: {
        ...(isPro ? {} : { take: 1 }), // FREE: только первая для preview
        include: {
          part: {
            include: {
              partCategory: true,
              attributes: {
                include: {
                  template: {
                    include: {
                      synonymGroups: {
                        include: {
                          synonyms: true
                        }
                      }
                    }
                  }
                }
              },
              image: true,
              mediaAssets: true
            }
          }
        }
      },
      image: true,
      mediaAssets: true,
      ...(isPro && {
        equipmentApplicabilities: {
          include: {
            equipmentModel: {
              include: {
                brand: true,
                attributes: {
                  include: {
                    template: true
                  }
                }
              }
            }
          }
        }
      })
    }
  });
} catch (error) {
  console.error("Failed to load CatalogItem:", error);
}

// Для FREE пользователей получаем только количество аналогов для preview
let applicabilitiesCount = 0
if (!isPro && item) {
  try {
    applicabilitiesCount = await trpcClient.crud.partApplicability.count.query({
      where: { catalogItemId: itemId }
    })
  } catch (error) {
    console.error("Failed to count applicabilities:", error)
  }
}

// Fetch alternative brands (PRO only)
let alternativeItems: any[] = []
if (isPro && item?.applicabilities?.length > 0) {
  const partIds = item.applicabilities.map((app: any) => app.partId)
  try {
    alternativeItems = await trpcClient.crud.catalogItem.findMany.query({
      where: {
        applicabilities: {
          some: {
            partId: { in: partIds }
          }
        },
        id: { not: itemId },
        isPublic: true
      },
      include: {
        brand: true,
        image: true,
        attributes: {
          include: {
            template: true
          }
        },
        applicabilities: {
          where: {
            partId: { in: partIds }
          },
          include: {
            part: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      },
      take: 20
    })
  } catch (error) {
    console.error('Failed to fetch alternative items:', error)
  }
}

// Fetch similar items (PRO only)
let similarItems: any[] = []
if (isPro && item) {
  try {
    similarItems = await trpcClient.crud.catalogItem.findMany.query({
      where: {
        brandId: item.brandId,
        id: { not: itemId },
        isPublic: true
      },
      include: {
        brand: true,
        image: true,
        attributes: {
          include: {
            template: true
          }
        },
        applicabilities: {
          select: {
            partId: true
          }
        }
      },
      take: 50 // Will be filtered client-side by similarity
    })
  } catch (error) {
    console.error('Failed to fetch similar items:', error)
  }
}

if (!item) {
  // Можно показать страницу 404
  return Astro.redirect('/catalog');
}

// Cache control для production
if (import.meta.env.PROD) {
  Astro.response.headers.set(
    'Cache-Control',
    'public, max-age=300, s-maxage=600, stale-while-revalidate=86400'
  )
  
  // Generate ETag based on item updatedAt
  if (item) {
    const etag = `"${item.id}-${new Date(item.updatedAt).getTime()}"`
    Astro.response.headers.set('ETag', etag)
    
    const ifNoneMatch = Astro.request.headers.get('If-None-Match')
    if (ifNoneMatch === etag) {
      return new Response(null, { status: 304 })
    }
  }
}
---

<MainLayout title={item?.sku ? `Артикул ${item.sku}` : 'Товар'} description={`Каталожная позиция от ${item?.brand?.name}`}>
  <CatalogItemIsland
    client:load
    item={item}
    isPro={isPro}
    applicabilitiesCount={isPro ? (item?.applicabilities?.length ?? 0) : applicabilitiesCount}
    alternativeItems={isPro ? alternativeItems : []}
    similarItems={isPro ? similarItems : []}
  />
</MainLayout>


