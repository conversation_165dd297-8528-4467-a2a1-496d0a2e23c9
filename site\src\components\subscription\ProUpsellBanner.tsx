"use client"

import { motion } from "motion/react"
import { Crown, ArrowRight, Check } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ModernCard, ModernCardHeader, ModernCardTitle, ModernCardDescription, ModernCardContent, ModernCardFooter } from "@/components/ui/modern-card"
import { cn } from "@/lib/utils"

export interface ProUpsellBannerProps {
  title?: string
  description?: string
  features?: string[]
  ctaText?: string
  ctaHref?: string
  variant?: "default" | "compact" | "inline"
  className?: string
  showBadge?: boolean
}

const defaultFeatures = [
  "Полный доступ к базе аналогов",
  "Сравнение характеристик деталей",
  "Информация о поставщиках",
  "Экспорт данных в Excel",
  "AI-ассистент для поиска"
]

export function ProUpsellBanner({
  title = "Откройте полный доступ к базе аналогов",
  description = "Оформите PRO подписку и получите доступ к группам взаимозаменяемости, сравнению характеристик и информации о поставщиках",
  features = defaultFeatures,
  ctaText = "Оформить PRO подписку",
  ctaHref = "/pricing",
  variant = "default",
  className,
  showBadge = true
}: ProUpsellBannerProps) {
  if (variant === "inline") {
    return (
      <div className={cn("inline-flex items-center gap-2", className)}>
        {showBadge && (
          <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0">
            PRO
          </Badge>
        )}
        <p className="text-sm text-muted-foreground">Нужен PRO для полного доступа</p>
        <Button asChild variant="outline" size="sm">
          <a href={ctaHref}>{ctaText}</a>
        </Button>
      </div>
    )
  }

  if (variant === "compact") {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className={cn("flex items-center justify-between p-4 border rounded-lg bg-background", className)}
      >
        <div className="flex items-center gap-3 flex-1">
          <div className="p-2 rounded-full bg-gradient-to-r from-amber-500 to-orange-500">
            <Crown className="w-5 h-5 text-white" />
          </div>
          <div>
            <h4 className="font-semibold text-base">{title}</h4>
            <p className="text-sm text-muted-foreground line-clamp-2">{description}</p>
          </div>
        </div>
        <Button asChild variant="default" size="sm">
          <a href={ctaHref} className="flex items-center gap-1">
            {ctaText}
            <ArrowRight className="w-4 h-4" />
          </a>
        </Button>
      </motion.div>
    )
  }

  // default variant
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <ModernCard variant="elevated" className={cn("max-w-md mx-auto", className)}>
        <ModernCardHeader className="text-center">
          {showBadge && (
            <Badge className="mx-auto mb-2 bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0">
              PRO
            </Badge>
          )}
          <ModernCardTitle className="flex items-center justify-center gap-2">
            <Crown className="w-6 h-6 text-amber-500" />
            {title}
          </ModernCardTitle>
          <ModernCardDescription>{description}</ModernCardDescription>
        </ModernCardHeader>
        <ModernCardContent>
          <ul className="space-y-3">
            {features.map((feature, i) => (
              <li key={i} className="flex items-start gap-3">
                <Check className="w-5 h-5 text-chart-2 mt-0.5 flex-shrink-0" />
                <span className="text-sm">{feature}</span>
              </li>
            ))}
          </ul>
        </ModernCardContent>
        <ModernCardFooter className="pt-0">
          <Button asChild className="w-full">
            <a href={ctaHref} className="flex items-center justify-center gap-2">
              {ctaText}
              <ArrowRight className="w-4 h-4" />
            </a>
          </Button>
        </ModernCardFooter>
      </ModernCard>
    </motion.div>
  )
}