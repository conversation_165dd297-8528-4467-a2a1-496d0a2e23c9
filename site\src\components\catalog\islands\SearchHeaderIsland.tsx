"use client"

import { useMemo } from "react"
import { Package } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { ProfessionalHeader } from "../pro/ProfessionalHeader"
import { useCatalogSearch } from "../pro/useCatalogSearch"
import { useCatalogGlobalState } from "@/lib/catalog-state"
import { useFeatureAccess } from "@/hooks/useFeatureAccess"
import { analytics } from "@/lib/analytics"
import type { CatalogSearchFilters } from "@/types/catalog"

import { TrpcBoundary } from "@/components/providers/TrpcBoundary"

export default function SearchHeaderIsland() {
  return (
    <TrpcBoundary>
      <SearchHeaderIslandInner />
    </TrpcBoundary>
  )
}

function SearchHeaderIslandInner() {
  const { totalCount, filteredCount } = useCatalogSearch()
  const { filters, setFilters, updateFilters } = useCatalogGlobalState()
  
  // Проверка подписки для условного messaging
  const { isPro, canUseAdvancedSearch } = useFeatureAccess()

  // Subscription-based текст для статистики
  const statsText = useMemo(() => {
    if (isPro) {
      return {
        filtered: `${filteredCount} групп`,
        total: `из ${totalCount} общих групп`
      }
    } else {
      return {
        filtered: `${filteredCount} артикулов`,
        total: `из ${totalCount} общих артикулов`
      }
    }
  }, [isPro, filteredCount, totalCount])

  const handleSearchChange = (query: string) => {
    updateFilters({ query })
  }

  const handleOpenAI = () => {
    const event = new CustomEvent('openAIAssistant')
    window.dispatchEvent(event)
  }

  const handleSavedSearchApply = (query: string, filters: CatalogSearchFilters) => {
    setFilters(filters)
    // Track analytics
    analytics.savedSearchApplied(query)
  }

  return (
    <div className="bg-background border-b border-border/40">
      <ProfessionalHeader
        totalCount={totalCount}
        filteredCount={filteredCount}
        searchQuery={filters.query}
        onSearchChange={handleSearchChange}
        onOpenAI={handleOpenAI}
        isPro={isPro}
        canUseAI={canUseAdvancedSearch}
        onSavedSearchApply={handleSavedSearchApply}
      />

      {/* Статистика результатов */}
      <div className="container max-w-none px-4 py-2">
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="px-2 py-1 font-medium text-xs">
            <Package className="h-3 w-3 mr-1" />
            {statsText.filtered}
          </Badge>
          {filteredCount !== totalCount && (
            <Badge variant="outline" className="px-2 py-1 text-xs">
              {statsText.total}
            </Badge>
          )}
        </div>
      </div>

      {/* Информационный banner для FREE пользователей */}
      {!isPro && (
        <div className="px-4 py-2 bg-muted/30 border-t border-border/40">
          <p className="text-xs text-muted-foreground text-center">
            Вы используете базовый поиск по артикулам. 
            <a href="/pricing" className="text-primary hover:underline ml-1">
              Оформите PRO для поиска по группам взаимозаменяемости
            </a>
          </p>
        </div>
      )}
    </div>
  )
}
