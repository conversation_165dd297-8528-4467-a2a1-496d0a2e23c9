"use client"

import { type ReactNode } from "react"
import { useFeatureAccess, Feature } from "@/hooks/useFeatureAccess"
import { ProUpsellBanner } from "./ProUpsellBanner"
import { Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"

export interface SubscriptionGateProps {
  children: ReactNode
  feature?: Feature
  fallback?: ReactNode
  loadingFallback?: ReactNode
  requirePro?: boolean
  requireAuth?: boolean
  upsellTitle?: string
  upsellDescription?: string
  upsellFeatures?: string[]
}

export function SubscriptionGate({
  children,
  feature,
  fallback,
  loadingFallback,
  requirePro = true,
  requireAuth = false,
  upsellTitle,
  upsellDescription,
  upsellFeatures,
}: SubscriptionGateProps) {
  const { isPro, isAuthenticated, isPending, hasAccess } = useFeatureAccess()
  
  // Показываем loading state
  if (isPending) {
    return loadingFallback || (
      <div className={cn("flex items-center justify-center p-8")}>
        <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />
      </div>
    )
  }
  
  // Проверка авторизации
  if (requireAuth && !isAuthenticated) {
    return fallback || (
      <ProUpsellBanner 
        title="Требуется авторизация"
        description="Войдите в систему для доступа к этому контенту"
        ctaText="Войти"
        ctaHref="/login"
      />
    )
  }
  
  // Проверка доступа к feature (всегда выполняется, если feature указана)
  if (feature && !hasAccess(feature)) {
    return fallback || (
      <ProUpsellBanner
        title={upsellTitle}
        description={upsellDescription}
        features={upsellFeatures}
      />
    )
  }
  
  // Проверка подписки Pro
  if (requirePro && !isPro) {
    return fallback || (
      <ProUpsellBanner
        title={upsellTitle}
        description={upsellDescription}
        features={upsellFeatures}
      />
    )
  }
  
  // Доступ разрешен
  return <>{children}</>
}