import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Database, 
  Building2, 
  DollarSign, 
  Layers, 
  CheckCircle2, 
  Shield, 
  Calendar,
  Target,
  ChevronDown
} from 'lucide-react'
import type { Part } from '@/lib/types'
import type { FilterMetadata } from '@/types/filters'
import { aggregateBrands } from '@/lib/aggregators'
import { formatRelativeTime } from '@/lib/formatters'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Badge } from '@/components/ui/badge'
import { staggerContainer, fadeInUp } from '@/lib/animation-variants'
import { analytics } from '@/lib/analytics'

type CatalogItem = {
  id: number
  sku: string
  description: string | null
  brand?: {
    id: number
    name: string
    isOem: boolean
  } | null
  attributes?: Array<{
    value: string
    template?: {
      name: string
      dataType: string
    } | null
  }>
  createdAt?: Date
  updatedAt?: Date
  applicabilities?: Array<{
    accuracy: number | null
  }>
}

export interface SearchInsightsProps {
  results: Array<Part | CatalogItem>
  metadata?: FilterMetadata
  isPro: boolean
  className?: string
}

interface InsightCard {
  icon: typeof Database
  label: string
  value: string | number
  badge?: string
  badgeVariant?: 'default' | 'secondary' | 'destructive' | 'outline'
}

export default function SearchInsights({ 
  results, 
  metadata, 
  isPro, 
  className = '' 
}: SearchInsightsProps) {
  const [isCollapsed, setIsCollapsed] = useState<boolean>(() => {
    if (typeof window === 'undefined') return false
    try {
      const stored = localStorage.getItem('catalog-insights-collapsed')
      return stored === 'true'
    } catch {
      return false
    }
  })
  
  // Сохранять состояние сворачивания
  useEffect(() => {
    try {
      localStorage.setItem('catalog-insights-collapsed', String(isCollapsed))
    } catch (error) {
      console.warn('Failed to save insights collapsed state:', error)
    }
  }, [isCollapsed])
  
  // Трекинг просмотра insights
  useEffect(() => {
    if (results.length > 0 && !isCollapsed) {
      analytics.searchInsightsViewed(results.length, isPro)
    }
  }, [results.length, isPro, isCollapsed])
  
  if (results.length === 0) {
    return null
  }
  
  // Вычисляем insights
  const insights: InsightCard[] = []
  
  // 1. Всего результатов
  insights.push({
    icon: Database,
    label: 'Всего результатов',
    value: results.length
  })
  
  // 2. Популярный бренд (только для CatalogItem)
  const isCatalogItems = results.length > 0 && 'sku' in results[0]
  if (isCatalogItems) {
    const catalogItems = results as CatalogItem[]
    const brands = aggregateBrands(catalogItems)
    
    if (brands.length > 0) {
      const topBrand = brands[0]
      insights.push({
        icon: Building2,
        label: 'Популярный бренд',
        value: `${topBrand.brandName} (${topBrand.percentage.toFixed(0)}%)`,
        badge: topBrand.isOem ? 'OEM' : undefined,
        badgeVariant: topBrand.isOem ? 'default' : undefined
      })
    }
  }
  
  // 3. Категории (только для Part)
  const isParts = results.length > 0 && 'partCategory' in results[0]
  if (isParts) {
    const parts = results as Part[]
    const categories = new Set(
      parts
        .filter(p => p.partCategory)
        .map(p => p.partCategory!.name)
    )
    
    insights.push({
      icon: Layers,
      label: 'Категории',
      value: categories.size
    })
  }
  
  // 4. Полнота данных - процент элементов с атрибутами
  const itemsWithAttributes = results.filter(
    item => item.attributes && item.attributes.length > 0
  ).length
  const attributeCoverage = results.length > 0 
    ? Math.round((itemsWithAttributes / results.length) * 100)
    : 0
  
  insights.push({
    icon: CheckCircle2,
    label: 'Полнота данных',
    value: `${attributeCoverage}%`
  })
  
  // 5. OEM процент (для CatalogItem)
  if (isCatalogItems) {
    const catalogItems = results as CatalogItem[]
    const oemCount = catalogItems.filter(
      item => item.brand?.isOem
    ).length
    
    if (oemCount > 0) {
      const oemPercentage = Math.round((oemCount / catalogItems.length) * 100)
      insights.push({
        icon: Shield,
        label: 'OEM запчасти',
        value: `${oemPercentage}%`
      })
    }
  }
  
  // 6. Диапазон дат обновлений
  const datesAvailable = results.some(
    item => 'updatedAt' in item && item.updatedAt
  )
  
  if (datesAvailable) {
    const dates = results
      .filter(item => 'updatedAt' in item && item.updatedAt)
      .map(item => new Date((item as CatalogItem).updatedAt!))
      .sort((a, b) => a.getTime() - b.getTime())
    
    if (dates.length > 0) {
      const oldest = dates[0]
      const newest = dates[dates.length - 1]
      
      insights.push({
        icon: Calendar,
        label: 'Обновления',
        value: `от ${formatRelativeTime(oldest)} до ${formatRelativeTime(newest)}`
      })
    }
  }
  
  // 7. Средняя точность (PRO, только для Part с applicabilities)
  if (isPro && isParts) {
    const parts = results as Part[]
    const allAccuracies: number[] = []
    
    for (const part of parts) {
      if (part.applicabilities) {
        for (const app of part.applicabilities) {
          if (app.accuracy !== null) {
            allAccuracies.push(app.accuracy)
          }
        }
      }
    }
    
    if (allAccuracies.length > 0) {
      const avgAccuracy = Math.round(
        allAccuracies.reduce((sum, acc) => sum + acc, 0) / allAccuracies.length
      )
      
      insights.push({
        icon: Target,
        label: 'Средняя точность',
        value: `${avgAccuracy}%`
      })
    }
  }
  
  return (
    <Collapsible
      open={!isCollapsed}
      onOpenChange={(open) => setIsCollapsed(!open)}
      className={className}
    >
      <div className="rounded-xl border border-border bg-card">
        <CollapsibleTrigger className="flex w-full items-center justify-between p-4 hover:bg-accent/50 transition-colors">
          <h3 className="text-base font-semibold">Аналитика результатов</h3>
          <motion.div
            animate={{ rotate: isCollapsed ? 0 : 180 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="h-5 w-5 text-muted-foreground" />
          </motion.div>
        </CollapsibleTrigger>
        
        <CollapsibleContent>
          <motion.div
            variants={staggerContainer}
            initial="initial"
            animate="animate"
            className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 p-4 pt-0"
          >
            {insights.map((insight, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="flex flex-col gap-2 p-3 rounded-lg border border-border bg-background hover:bg-accent/30 transition-colors"
              >
                <div className="flex items-center gap-2">
                  <insight.icon className="h-4 w-4 text-primary" />
                  <span className="text-xs text-muted-foreground">
                    {insight.label}
                  </span>
                </div>
                
                <div className="flex items-baseline gap-2">
                  <span className="text-lg font-bold tracking-tight">
                    {insight.value}
                  </span>
                  
                  {insight.badge && (
                    <Badge 
                      variant={insight.badgeVariant || 'secondary'}
                      className="text-[10px] px-1.5 py-0"
                    >
                      {insight.badge}
                    </Badge>
                  )}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </CollapsibleContent>
      </div>
    </Collapsible>
  )
}

