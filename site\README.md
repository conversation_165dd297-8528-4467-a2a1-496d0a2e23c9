# PartTec3 - Клиентская часть

Клиентская часть каталога взаимозаменяемых запчастей PartTec3, построенная на Astro + React + shadcn/ui + Tailwind CSS 4.

## 🚀 Технологический стек

- **Astro 5** - основной фреймворк
- **React 19** - для интерактивных компонентов (islands)
- **shadcn/ui** - библиотека UI компонентов
- **Tailwind CSS 4** - стилизация
- **tRPC** - типобезопасное API
- **React Query** - кеширование данных
- **Lucide React** - иконки

## 📁 Структура проекта

```text
site/
├── src/
│   ├── components/
│   │   ├── catalog/          # Компоненты каталога
│   │   │   ├── PartCard.tsx
│   │   │   ├── CategoryCard.tsx
│   │   │   ├── BrandCard.tsx
│   │   │   └── SearchForm.tsx
│   │   ├── providers/        # React провайдеры
│   │   │   └── TrpcProvider.tsx
│   │   └── ui/              # shadcn/ui компоненты
│   │       ├── button.tsx
│   │       ├── card.tsx
│   │       ├── input.tsx
│   │       └── badge.tsx
│   ├── layouts/
│   │   └── MainLayout.astro  # Основной layout
│   ├── lib/
│   │   ├── trpc.ts          # tRPC клиент
│   │   └── utils.ts         # Утилиты
│   ├── pages/               # Страницы приложения
│   │   ├── index.astro      # Главная страница
│   │   ├── catalog.astro    # Каталог запчастей
│   │   ├── categories.astro # Список категорий
│   │   ├── brands.astro     # Список брендов
│   │   ├── search.astro     # Поиск
│   │   └── catalog/
│   │       ├── parts/[id].astro        # Детальная страница запчасти
│   │       ├── categories/[slug].astro # Страница категории
│   │       └── brands/[slug].astro     # Страница бренда
│   ├── styles/
│   │   └── global.css       # Глобальные стили
│   └── types/
│       └── catalog.ts       # TypeScript типы
└── package.json
```

## 🎯 Реализованный функционал

### Основные страницы
- **Главная** (`/`) - обзор каталога, статистика, популярные категории
- **Каталог** (`/catalog`) - список запчастей с фильтрами и поиском
- **Категории** (`/categories`) - иерархический каталог категорий
- **Бренды** (`/brands`) - список производителей (OEM и Aftermarket)
- **Поиск** (`/search`) - расширенный поиск по всем сущностям

### Детальные страницы
- **Группа запчастей** (`/catalog/parts/[id]`) - подробная информация о группе взаимозаменяемости
- **Категория** (`/catalog/categories/[slug]`) - запчасти и подкатегории
- **Бренд** (`/catalog/brands/[slug]`) - каталог запчастей и модели техники

### Компоненты
- **PartCard** - карточка группы запчастей с атрибутами
- **CategoryCard** - карточка категории с иерархией
- **BrandCard** - карточка бренда с типом (OEM/Aftermarket)
- **SearchForm** - форма поиска с автодополнением

### Особенности
- **Типобезопасность** - полная типизация через tRPC и TypeScript
- **Responsive дизайн** - адаптивная верстка для всех устройств
- **Навигация** - хлебные крошки и интуитивная навигация
- **Фильтрация** - по категориям, брендам, атрибутам
- **Пагинация** - для больших списков данных
- **Кеширование** - React Query для оптимизации запросов

## 🧞 Команды

```bash
# Установка зависимостей
npm install

# Запуск dev сервера
npm run dev

# Сборка для продакшена
npm run build

# Предварительный просмотр сборки
npm run preview
```

## 🔗 Интеграция с API

Клиентская часть подключается к API серверу через tRPC:
- **URL API**: `http://localhost:3000/trpc`
- **Автогенерированные роуты**: используются CRUD операции из ZenStack
- **Кастомные роуты**: поиск, фильтрация, статистика

## 📋 Схема данных

Основные сущности каталога:
- **Part** - группы взаимозаменяемости
- **PartCategory** - иерархические категории
- **CatalogItem** - конкретные артикулы от производителей
- **Brand** - производители (OEM и Aftermarket)
- **EquipmentModel** - модели техники
- **Attributes** - динамические характеристики

## 📝 Примечания

- Следует принципу DRY - нет дублирования компонентов
- Максимально простой и прозрачный код
- Использует автогенерированные Zod схемы и tRPC роуты
- Реализован только реальный функционал без демо данных


## Search Architecture

### Current Implementation: PostgreSQL Full-Text Search

Поиск в настоящее время использует PostgreSQL full-text search с `ts_rank` для ранжирования результатов.

**Компоненты:**
- `pages/search.astro` - SSR страница поиска для SEO
- `components/catalog/search/SearchIsland.tsx` - главный React island для интерактивности
- `api/services/site.service.ts` - unified search endpoint
- `lib/search-provider.ts` - абстракция для легкой замены поискового движка

**Возможности:**
- Full-text search по названиям, описаниям, SKU
- Ранжирование результатов (relevanceScore)
- Поиск по категориям и брендам
- Faceted search с фильтрами
- Search history и saved searches
- Image search (UI готов, backend TODO)
- Equipment search

### Future: MeiliSearch Integration

Планируется миграция на MeiliSearch для улучшенного поиска:

**Преимущества MeiliSearch:**
- Instant search (результаты за <50ms)
- Встроенная typo tolerance
- Faceted search из коробки
- Synonym support
- Highlight matches автоматически
- Geo-search (если понадобится)

**Миграция:**
1. Установить MeiliSearch
2. Создать индексы для parts, catalogItems, categories, brands
3. Реализовать `MeiliSearchProvider` в `lib/search-provider.ts`
4. Изменить `SEARCH_PROVIDER=meilisearch` в `.env`
5. UI остается без изменений (работает через единый интерфейс)

**Конфигурация:**
См. `.env.example` для настройки SEARCH_PROVIDER и MeiliSearch параметров.

## ⚡ Performance Optimizations

### Виртуализация списков

Для работы с большими списками используется `@tanstack/react-virtual`:

**Компоненты с виртуализацией:**
- `ResultsIsland.tsx` - виртуализация списка результатов (>20 элементов)
- `DataTable.tsx` - виртуализация строк таблицы (>50 строк)
- `ApplicabilitiesCardView.tsx` - виртуализация мобильных карточек (>30 элементов)

**Использование:**
```typescript
import { useListVirtualization } from '@/hooks/useVirtualization'

const { virtualizer, virtualItems, totalSize, containerRef } = useListVirtualization({
  items: myItems,
  itemHeight: 100,
  enabled: myItems.length > 20,
})
```

### Code Splitting

Тяжелые компоненты загружаются асинхронно через `React.lazy()`:

**Lazy-loaded компоненты:**
- `StatisticsOverview` - графики и статистика (~50KB)
- `MediaGallery` / `MediaZoom` / `MediaCarousel` - медиа компоненты (~30KB)
- `FilterSystem` - система фильтров (~40KB)
- `AttributeComparison` - сравнение атрибутов (~20KB)

**Использование:**
```typescript
const StatisticsOverview = lazy(() => import('../statistics/StatisticsOverview'))

<Suspense fallback={<LoadingState variant="card" />}>
  <StatisticsOverview part={part} />
</Suspense>
```

### Prefetching

Часто используемые данные prefetch'атся в фоне:

**Prefetch hooks:**
- `usePrefetchCategories()` - prefetch категорий при загрузке каталога
- `usePrefetchBrands()` - prefetch брендов
- `usePrefetchOnHover(id, type)` - prefetch при hover на карточке
- `usePrefetchNextPage(page, hasNext)` - prefetch следующей страницы

**Использование:**
```typescript
import { usePrefetchOnHover } from '@/hooks/usePrefetch'

const handleMouseEnter = usePrefetchOnHover(part.id, 'part')
<PartCard onMouseEnter={handleMouseEnter} ... />
```

### Image Optimization

**Progressive Loading:**
- Blur placeholder → Low-res → High-res
- Использует IntersectionObserver для lazy loading
- Responsive images с srcset для разных плотностей экрана

**Responsive Images:**
```html
<img 
  srcset="image-400w.jpg 400w, image-800w.jpg 800w, image-1200w.jpg 1200w"
  sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
/>
```

### Caching Strategy

**React Query Cache:**
- Categories: 30 минут stale, 2 часа gc
- Brands: 30 минут stale, 2 часа gc
- Attribute Templates: 1 час stale, 4 часа gc
- Search Results: 30 секунд stale, 5 минут gc

**HTTP Cache:**
- Part/CatalogItem pages: 5 минут browser, 10 минут CDN
- ETag support для 304 Not Modified responses
- Stale-while-revalidate для instant loading

### Performance Monitoring

**Hooks для мониторинга:**
- `useRenderTime(componentName)` - измерение времени рендера
- `useDataLoadTime(queryKey)` - измерение времени загрузки данных
- `usePageLoadMetrics()` - метрики загрузки страницы (TTFB, FCP, LCP)

**Использование:**
```typescript
import { useRenderTime } from '@/hooks/usePerformance'

const { renderTime } = useRenderTime('MyComponent', import.meta.env.DEV)
// Метрики логируются в console и отправляются в analytics
```

### Build Optimizations

**Vite Configuration:**
- Manual chunks для vendor библиотек
- Terser минификация с удалением console.log
- optimizeDeps для faster dev server

**Bundle Size:**
- Initial bundle: ~150KB (gzipped)
- Vendor chunks: ~200KB (кешируются отдельно)
- Lazy chunks: загружаются по требованию

### Performance Targets

**Метрики:**
- Time to Interactive (TTI): < 3s на 3G
- First Contentful Paint (FCP): < 1.5s
- Largest Contentful Paint (LCP): < 2.5s
- Total Blocking Time (TBT): < 300ms

**Мониторинг:**
Используйте Chrome DevTools Lighthouse для аудита производительности.