// @ts-check
import { defineConfig } from "astro/config";

import tailwindcss from "@tailwindcss/vite";
import react from "@astrojs/react";
import node from "@astrojs/node";
import path from "node:path";

// https://astro.build/config
export default defineConfig({
  server: {
    port: 4323,
  },
  output: 'server',
  vite: {
      plugins: [tailwindcss()],
      resolve: {
          alias: [
              // alias на shared types из packages
              { find: "@api-types", replacement: new URL('../packages/shared-types/src/zod', import.meta.url).pathname }
          ]
      },
      build: {
          rollupOptions: {
              output: {
                  manualChunks: {
                      // Разделить vendor библиотеки на отдельные chunks
                      'react-vendor': ['react', 'react-dom'],
                      'query-vendor': ['@tanstack/react-query', '@tanstack/react-table', '@tanstack/react-virtual'],
                      'ui-vendor': ['motion', 'lucide-react', 'vaul'],
                      'trpc-vendor': ['@trpc/client', '@trpc/react-query'],
                      // Тяжелые компоненты в отдельные chunks
                      'statistics': ['./src/components/catalog/statistics/StatisticsOverview.tsx'],
                      'media': ['./src/components/catalog/media/MediaGallery.tsx', './src/components/catalog/media/MediaZoom.tsx'],
                      'filters': ['./src/components/catalog/filters/FilterSystem.tsx'],
                  },
              },
          },
          // Увеличить chunk size warning limit
          chunkSizeWarningLimit: 1000,
          // Минификация
          minify: 'terser',
          terserOptions: {
              compress: {
                  drop_console: true, // Удалить console.log в production
                  drop_debugger: true,
              },
          },
      },
      // Оптимизация dev сервера
      optimizeDeps: {
          include: [
              'react',
              'react-dom',
              '@tanstack/react-query',
              '@tanstack/react-table',
              '@tanstack/react-virtual',
              'motion',
          ],
      },
  },

  integrations: [react()],

  adapter: node({
    mode: "standalone",
  }),
});
