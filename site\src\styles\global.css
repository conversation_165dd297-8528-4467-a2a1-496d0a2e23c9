@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.205 0 0);
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.577 0.245 27.325);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.708 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --radius: 0.625rem;
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0);
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.145 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.145 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.985 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.396 0.141 25.723);
    --destructive-foreground: oklch(0.637 0.237 25.331);
    --border: oklch(0.269 0 0);
    --input: oklch(0.269 0 0);
    --ring: oklch(0.439 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(0.269 0 0);
    --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }

    body {
        @apply bg-background text-foreground;
    }
}

/* Дополнительные цвета для каталога */
:root {
    --info: oklch(0.94 0.05 240);
    --info-foreground: oklch(0.48 0.15 240);
    --border-strong: oklch(0.85 0 0);
    --surface: oklch(0.98 0 0);
}

.dark {
    --info: oklch(0.175 0.05 240);
    --info-foreground: oklch(0.48 0.15 240);
    --border-strong: oklch(0.25 0 0);
    --surface: oklch(0.15 0 0);
}

@theme inline {
    --color-info: var(--info);
    --color-info-foreground: var(--info-foreground);
    --color-border-strong: var(--border-strong);
    --color-surface: var(--surface);
}

/* Утилиты для обрезки текста */
.line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}

/* Анимации для каталога */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
    animation: slideUp 0.3s ease-out;
}

.animate-theme-transition {
    transition: all 0.2s ease-in-out;
}

/* Ripple animation used by UI ripple component */
@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(0.9);
    opacity: 0.1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.15);
    opacity: 0.05;
  }
}

.animate-ripple {
  animation: ripple 4s ease-in-out infinite;
}

/* Advanced Animation Keyframes */
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideOutToLeft {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-20px);
  }
}

@keyframes slideOutToRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(20px);
  }
}

/* Animation Utility Classes - with prefers-reduced-motion support */
@media (prefers-reduced-motion: no-preference) {
  .animate-slide-in-left {
    animation: slideInFromLeft 0.3s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInFromRight 0.3s ease-out;
  }

  .animate-slide-in-top {
    animation: slideInFromTop 0.3s ease-out;
  }

  .animate-slide-in-bottom {
    animation: slideInFromBottom 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .animate-scale-out {
    animation: scaleOut 0.2s ease-in;
  }

  .animate-shimmer {
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 100%
    );
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.4s ease-out;
  }

  .animate-fade-in-down {
    animation: fadeInDown 0.4s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.5s ease-out;
  }
}

/* For reduced motion users - instant transitions */
@media (prefers-reduced-motion: reduce) {
  .animate-slide-in-left,
  .animate-slide-in-right,
  .animate-slide-in-top,
  .animate-slide-in-bottom,
  .animate-scale-in,
  .animate-scale-out,
  .animate-shimmer,
  .animate-fade-in-up,
  .animate-fade-in-down,
  .animate-bounce-in {
    animation: none;
    opacity: 1;
    transform: none;
  }
}

/* Transition Utilities */
.transition-smooth {
  transition: all 0.2s ease-in-out;
}

.transition-fast {
  transition: all 0.15s ease-in-out;
}

.transition-slow {
  transition: all 0.3s ease-in-out;
}

.transition-transform {
  transition: transform 0.2s ease-in-out;
}

.transition-opacity {
  transition: opacity 0.2s ease-in-out;
}

/* Animation Delay Utilities */
.animation-delay-100 {
  animation-delay: 100ms;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

.animation-delay-500 {
  animation-delay: 500ms;
}

/* Stagger Animation Support */
.stagger-item {
  animation-delay: calc(var(--stagger-delay, 0ms) * var(--stagger-index, 0));
}

/* Mobile Typography Utilities */
@layer utilities {
  /* Mobile-optimized text sizes */
  .text-mobile-xs { font-size: 0.75rem; line-height: 1.25rem; }
  .text-mobile-sm { font-size: 0.875rem; line-height: 1.5rem; }
  .text-mobile-base { font-size: 1rem; line-height: 1.75rem; }
  .text-mobile-lg { font-size: 1.125rem; line-height: 2rem; }
  .text-mobile-xl { font-size: 1.25rem; line-height: 2.25rem; }
  
  /* Mobile-optimized spacing */
  .mobile-spacing-tight { padding: 0.75rem; }
  .mobile-spacing-normal { padding: 1rem; }
  .mobile-spacing-relaxed { padding: 1.5rem; }
  
  /* Touch-friendly targets */
  .touch-target { min-height: 44px; min-width: 44px; }
  .touch-target-lg { min-height: 56px; min-width: 56px; }
  
  /* Mobile-safe margins */
  .mobile-safe-top { padding-top: env(safe-area-inset-top); }
  .mobile-safe-bottom { padding-bottom: env(safe-area-inset-bottom); }
  .mobile-safe-left { padding-left: env(safe-area-inset-left); }
  .mobile-safe-right { padding-right: env(safe-area-inset-right); }
}

/* Mobile swipe animations */
@keyframes swipeLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes swipeRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes pullDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@media (prefers-reduced-motion: no-preference) {
  .animate-swipe-left {
    animation: swipeLeft 0.3s ease-out;
  }
  
  .animate-swipe-right {
    animation: swipeRight 0.3s ease-out;
  }
  
  .animate-pull-down {
    animation: pullDown 0.3s ease-out;
  }
}

/* Mobile-specific utilities */
@layer utilities {
  /* Hide on mobile */
  .mobile-hidden {
    @media (max-width: 639px) {
      display: none;
    }
  }
  
  /* Show only on mobile */
  .mobile-only {
    @media (min-width: 640px) {
      display: none;
    }
  }
  
  /* Tablet hidden */
  .tablet-hidden {
    @media (min-width: 640px) and (max-width: 1023px) {
      display: none;
    }
  }
  
  /* Desktop hidden */
  .desktop-hidden {
    @media (min-width: 1024px) {
      display: none;
    }
  }
}
/* ===== Доп. утилиты и стили для прототипа каталога (v0) ===== */
/* Цвета и утилиты AI/Glass/Gradients */
@layer utilities {
  .bg-surface { background-color: var(--surface); }
  .bg-surface-hover { background-color: var(--surface); }
  .border-border-strong { border-color: var(--border-strong); }
  .text-ai-primary { color: oklch(0.488 0.243 264.376); }
}

@layer components {
  .glass-effect {
    background-color: var(--card);
    backdrop-filter: blur(4px);
    border: 1px solid var(--border-strong);
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05), 0 1px 3px 0 rgb(0 0 0 / 0.1);
  }
  .gradient-primary { background: linear-gradient(135deg, var(--primary) 0%, var(--primary) 100%); }
  .gradient-ai { background: linear-gradient(135deg, oklch(0.488 0.243 264.376) 0%, oklch(0.696 0.17 162.48) 100%); }
  .shadow-professional { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05), 0 1px 3px 0 rgb(0 0 0 / 0.1); }
  .shadow-elevated { box-shadow: 0 2px 4px 0 rgb(0 0 0 / 0.06), 0 2px 8px 0 rgb(0 0 0 / 0.12); }
  .shadow-strong { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }
  .ai-message-user {
    background-color: var(--primary);
    color: var(--primary-foreground);
  }
  .ai-message-assistant {
    background-color: var(--surface);
    border: 1px solid var(--border);
    color: var(--foreground);
  }
}