# code-style.md

Code style.

## Рекомендации

- Пиши максимально прозрачный код.
- Используй чистую архитектуру.
- Не пытайся решить задачу костылем.
- Категорически запрещено изменять код, который может быть завязан в других местах. (Например: API эндпоинт)
- Когда контекст сесси уже большой, сообщай об этом что бы продолжить в средующей сессии/итерации.
- Код должен иметь максимальное покрытие типами, контрактами.
- Запрещено игнорировать критические ошибки линтера.
- Код должен быть весь покрыт типами и контрактами.
- КРИТИЧЕСКИ, Категорически ЗАПРЕШЕНО нарушать прицнипы SOLID, DRY, KISS, YAGNI, TDA. Строго им следуем, всегда, без исключений.
- КРИТИЧЕСКИ ЗАПРЕЩЕНО ДЕЛАТЬ ЗАГЛУШКИ, МОКИ, ДЕМО КОД, ТЕСТОВЫЙ КОД.