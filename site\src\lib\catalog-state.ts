import type { CatalogSearchFilters } from "@/types/catalog"
import type { FilterPreset } from "@/types/filters"

// Глобальное состояние каталога через localStorage и события
export class CatalogStateManager {
  private static readonly STORAGE_KEY = 'catalog-search-filters'
  private static readonly EVENT_NAME = 'catalog-filters-changed'

  static getFilters(): CatalogSearchFilters {
    if (typeof window === 'undefined') {
      return this.getDefaultFilters()
    }

    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        return { ...this.getDefaultFilters(), ...JSON.parse(stored) }
      }
    } catch (error) {
      console.warn('Failed to parse stored catalog filters:', error)
    }

    return this.getDefaultFilters()
  }

  static setFilters(filters: CatalogSearchFilters): void {
    if (typeof window === 'undefined') return

    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filters))
      window.dispatchEvent(new CustomEvent(this.EVENT_NAME, { detail: filters }))
    } catch (error) {
      console.warn('Failed to store catalog filters:', error)
    }
  }

  static updateFilters(updates: Partial<CatalogSearchFilters>): void {
    const current = this.getFilters()
    this.setFilters({ ...current, ...updates })
  }

  static clearFilters(): void {
    this.setFilters(this.getDefaultFilters())
  }

  static getActivePresetId(): string | null {
    if (typeof window === 'undefined') return null
    try {
      return localStorage.getItem('catalog-active-preset-id')
    } catch (error) {
      console.warn('Failed to get active preset ID:', error)
      return null
    }
  }

  static setActivePresetId(presetId: string | null): void {
    if (typeof window === 'undefined') return
    try {
      if (presetId) {
        localStorage.setItem('catalog-active-preset-id', presetId)
      } else {
        localStorage.removeItem('catalog-active-preset-id')
      }
      window.dispatchEvent(new CustomEvent('catalog-preset-changed', { detail: presetId }))
    } catch (error) {
      console.warn('Failed to set active preset ID:', error)
    }
  }

  static subscribe(callback: (filters: CatalogSearchFilters) => void): () => void {
    if (typeof window === 'undefined') return () => {}

    const handler = (event: CustomEvent<CatalogSearchFilters>) => {
      callback(event.detail)
    }

    window.addEventListener(this.EVENT_NAME, handler as EventListener)
    
    return () => {
      window.removeEventListener(this.EVENT_NAME, handler as EventListener)
    }
  }

  static subscribeToPresetChanges(callback: (presetId: string | null) => void): () => void {
    if (typeof window === 'undefined') return () => {}

    const handler = (event: CustomEvent<string | null>) => {
      callback(event.detail)
    }

    window.addEventListener('catalog-preset-changed', handler as EventListener)
    
    return () => {
      window.removeEventListener('catalog-preset-changed', handler as EventListener)
    }
  }

  static getViewMode(): 'detailed' | 'grid' | 'table' {
    if (typeof window === 'undefined') return 'detailed'
    try {
      const stored = localStorage.getItem('catalog-view-mode')
      if (stored && ['detailed', 'grid', 'table'].includes(stored)) {
        return stored as 'detailed' | 'grid' | 'table'
      }
    } catch (error) {
      console.warn('Failed to get view mode:', error)
    }
    return 'detailed'
  }

  static setViewMode(mode: 'detailed' | 'grid' | 'table'): void {
    if (typeof window === 'undefined') return
    try {
      localStorage.setItem('catalog-view-mode', mode)
      window.dispatchEvent(new CustomEvent('catalog-view-mode-changed', { detail: mode }))
    } catch (error) {
      console.warn('Failed to set view mode:', error)
    }
  }

  static subscribeToViewModeChanges(callback: (mode: 'detailed' | 'grid' | 'table') => void): () => void {
    if (typeof window === 'undefined') return () => {}

    const handler = (event: CustomEvent<'detailed' | 'grid' | 'table'>) => {
      callback(event.detail)
    }

    window.addEventListener('catalog-view-mode-changed', handler as EventListener)
    
    return () => {
      window.removeEventListener('catalog-view-mode-changed', handler as EventListener)
    }
  }

  private static notifyListeners(): void {
    window.dispatchEvent(new CustomEvent('catalogFiltersChanged', { detail: this.getFilters() }))
  }

  private static getDefaultFilters(): CatalogSearchFilters {
    return {
      query: "",
      categoryIds: [],
      brandIds: [],
      isOemOnly: false,
      attributeFilters: {},
    }
  }
}

// Хук для использования в React компонентах
import { useState, useEffect } from "react"

export function useCatalogGlobalState() {
  const [filters, setFiltersState] = useState<CatalogSearchFilters>(() => 
    CatalogStateManager.getFilters()
  )
  
  // Добавляем состояние пресета
  const [activePresetId, setActivePresetIdState] = useState<string | null>(() =>
    CatalogStateManager.getActivePresetId()
  )

  // Добавляем состояние режима просмотра
  const [viewMode, setViewModeState] = useState<'detailed' | 'grid' | 'table'>(() =>
    CatalogStateManager.getViewMode()
  )

  useEffect(() => {
    const unsubscribeFilters = CatalogStateManager.subscribe(setFiltersState)
    const unsubscribePreset = CatalogStateManager.subscribeToPresetChanges(setActivePresetIdState)
    const unsubscribeViewMode = CatalogStateManager.subscribeToViewModeChanges(setViewModeState)
    return () => {
      unsubscribeFilters()
      unsubscribePreset()
      unsubscribeViewMode()
    }
  }, [])

  const setFilters = (newFilters: CatalogSearchFilters) => {
    CatalogStateManager.setFilters(newFilters)
  }

  const updateFilters = (updates: Partial<CatalogSearchFilters>) => {
    CatalogStateManager.updateFilters(updates)
  }

  const clearFilters = () => {
    CatalogStateManager.clearFilters()
  }
  
  // Добавляем методы для работы с пресетами
  const setActivePresetId = (presetId: string | null) => {
    CatalogStateManager.setActivePresetId(presetId)
  }

  const setViewMode = (mode: 'detailed' | 'grid' | 'table') => {
    CatalogStateManager.setViewMode(mode)
  }

  return {
    filters,
    setFilters,
    updateFilters,
    clearFilters,
    activePresetId,
    setActivePresetId,
    viewMode,
    setViewMode,
  }
}
