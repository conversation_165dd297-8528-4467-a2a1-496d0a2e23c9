/**
 * FilterSystem - главный оркестратор системы фильтров
 * Предоставляет контекст для управления фильтрами, пресетами и метаданными
 */

import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import type { CatalogSearchFilters, AttributeTemplate } from '@/types/catalog';
import type { FilterPreset, QuickFilter, FilterMetadata } from '@/types/filters';
import {
  calculateActiveFiltersCount,
  isFilterDirty,
  mergeFilters,
  saveFilterPreset,
  deleteFilterPreset as deletePresetUtil,
  loadFilterPresets,
  areFiltersEqual,
} from './filter-utils';

// ============================================================================
// Types
// ============================================================================

export interface FilterSystemProps {
  filters: CatalogSearchFilters;
  setFilters: (filters: CatalogSearchFilters) => void;
  updateFilters: (updates: Partial<CatalogSearchFilters>) => void;
  clearFilters: () => void;
  metadata: FilterMetadata;
  categories: Array<{ id: number; name: string; slug: string; level: number; path: string }>;
  brands: Array<{ id: number; name: string; slug: string; isOem: boolean }>;
  attributeTemplates: AttributeTemplate[];
  children: React.ReactNode;
}

interface FilterSystemContextValue {
  // Состояние фильтров
  filters: CatalogSearchFilters;
  setFilters: (filters: CatalogSearchFilters) => void;
  updateFilters: (updates: Partial<CatalogSearchFilters>) => void;
  clearFilters: () => void;

  // Метаданные
  activeFiltersCount: number;
  metadata: FilterMetadata;

  // Справочные данные
  categories: FilterSystemProps['categories'];
  brands: FilterSystemProps['brands'];
  attributeTemplates: AttributeTemplate[];

  // Пресеты
  activePreset: FilterPreset | null;
  isDirty: boolean;
  presets: FilterPreset[];
  applyPreset: (preset: FilterPreset) => void;
  saveCurrentAsPreset: (name: string, description?: string) => void;
  deletePreset: (id: string) => void;
  setAsDefaultPreset: (id: string) => void;

  // Быстрые фильтры
  applyQuickFilter: (quickFilter: QuickFilter) => void;
}

// ============================================================================
// Context
// ============================================================================

const FilterSystemContext = createContext<FilterSystemContextValue | null>(null);

// ============================================================================
// Provider Component
// ============================================================================

export function FilterSystem({
  filters,
  setFilters,
  updateFilters,
  clearFilters,
  metadata,
  categories,
  brands,
  attributeTemplates,
  children,
}: FilterSystemProps): React.ReactElement {
  // Состояние пресетов
  const [presets, setPresets] = useState<FilterPreset[]>(() => loadFilterPresets());
  const [activePreset, setActivePreset] = useState<FilterPreset | null>(null);

  // Загружаем пресеты при монтировании
  useEffect(() => {
    const loadedPresets = loadFilterPresets();
    setPresets(loadedPresets);

    // Проверяем, есть ли пресет по умолчанию
    const defaultPreset = loadedPresets.find((p) => p.isDefault);
    if (defaultPreset) {
      setActivePreset(defaultPreset);
    }
  }, []);

  // Подсчитываем количество активных фильтров
  const activeFiltersCount = useMemo(
    () => calculateActiveFiltersCount(filters),
    [filters]
  );

  // Проверяем, изменились ли фильтры с момента применения пресета
  const isDirty = useMemo(() => {
    if (!activePreset) return false;
    return isFilterDirty(filters, activePreset);
  }, [filters, activePreset]);

  // ============================================================================
  // Preset Management Functions
  // ============================================================================

  /**
   * Применяет пресет фильтров
   */
  const applyPreset = (preset: FilterPreset): void => {
    setFilters(preset.filters);
    setActivePreset(preset);
  };

  /**
   * Сохраняет текущие фильтры как новый пресет
   */
  const saveCurrentAsPreset = (name: string, description?: string): void => {
    try {
      const newPreset = saveFilterPreset(name, description, filters);
      const updatedPresets = loadFilterPresets();
      setPresets(updatedPresets);
      setActivePreset(newPreset);
    } catch (error) {
      console.error('Failed to save preset:', error);
      throw error;
    }
  };

  /**
   * Удаляет пресет
   */
  const deletePreset = (id: string): void => {
    try {
      deletePresetUtil(id);
      const updatedPresets = loadFilterPresets();
      setPresets(updatedPresets);

      // Если удаленный пресет был активным, сбрасываем
      if (activePreset?.id === id) {
        setActivePreset(null);
      }
    } catch (error) {
      console.error('Failed to delete preset:', error);
      throw error;
    }
  };

  /**
   * Устанавливает пресет как пресет по умолчанию
   */
  const setAsDefaultPreset = (id: string): void => {
    try {
      const { setDefaultPreset } = require('./filter-utils');
      setDefaultPreset(id);
      const updatedPresets = loadFilterPresets();
      setPresets(updatedPresets);
    } catch (error) {
      console.error('Failed to set default preset:', error);
      throw error;
    }
  };

  // ============================================================================
  // Quick Filter Functions
  // ============================================================================

  /**
   * Применяет быстрый фильтр
   */
  const applyQuickFilter = (quickFilter: QuickFilter): void => {
    const merged = mergeFilters(filters, quickFilter.filters);
    setFilters(merged);

    // Сбрасываем активный пресет, так как применили быстрый фильтр
    setActivePreset(null);
  };

  // ============================================================================
  // Context Value
  // ============================================================================

  const contextValue: FilterSystemContextValue = {
    // Состояние фильтров
    filters,
    setFilters,
    updateFilters,
    clearFilters,

    // Метаданные
    activeFiltersCount,
    metadata,

    // Справочные данные
    categories,
    brands,
    attributeTemplates,

    // Пресеты
    activePreset,
    isDirty,
    presets,
    applyPreset,
    saveCurrentAsPreset,
    deletePreset,
    setAsDefaultPreset,

    // Быстрые фильтры
    applyQuickFilter,
  };

  return (
    <FilterSystemContext.Provider value={contextValue}>
      {children}
    </FilterSystemContext.Provider>
  );
}

// ============================================================================
// Custom Hook
// ============================================================================

/**
 * Хук для доступа к контексту фильтров
 * Должен использоваться внутри FilterSystem провайдера
 */
export function useFilterSystem(): FilterSystemContextValue {
  const context = useContext(FilterSystemContext);

  if (!context) {
    throw new Error('useFilterSystem must be used within FilterSystem provider');
  }

  return context;
}

// ============================================================================
// Exports
// ============================================================================

export default FilterSystem;
export { FilterSystemContext };

