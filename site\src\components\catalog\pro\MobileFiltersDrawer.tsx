"use client"

import { useState } from "react"
import { ChevronDown, ChevronUp, X, Filter, Sliders, Search } from "lucide-react"
import { ModernButton } from "@/components/ui/modern-button"
import { ModernCard, ModernCardContent, ModernCardHeader, ModernCardTitle } from "@/components/ui/modern-card"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ModernInput } from "@/components/ui/modern-input"
import { AttributeFilter } from "./parts/AttributeFilter"
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
  DrawerFooter,
} from "@/components/ui/drawer"
import { useFilterSystem } from "@/components/catalog/filters/FilterSystem"
import FilterPresets from "@/components/catalog/filters/FilterPresets"
import QuickFilters from "@/components/catalog/filters/QuickFilters"

interface MobileFiltersDrawerProps {
  className?: string
}

export function MobileFiltersDrawer({ className = '' }: MobileFiltersDrawerProps) {
  // Используем FilterSystem - обязательно
  const {
    filters,
    updateFilters,
    activeFiltersCount,
    clearFilters,
    categories,
    brands,
    attributeTemplates,
    metadata,
  } = useFilterSystem()

  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    categories: true,
    brands: true
  })
  const [categorySearch, setCategorySearch] = useState("")
  const [brandSearch, setBrandSearch] = useState("")
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)

  const triggerHaptic = (duration: number = 10) => {
    if ('vibrate' in navigator) {
      navigator.vibrate(duration);
    }
  };

  const toggleSection = (section: string) => setExpandedSections((prev) => ({ ...prev, [section]: !prev[section] }))
  
  // Универсальный обработчик для мультивыбора
  const handleToggle = (key: 'categoryIds' | 'brandIds', value: number) => {
    triggerHaptic(10);
    const current = filters[key];
    const updated = current.includes(value)
      ? current.filter((v) => v !== value)
      : [...current, value];
    updateFilters({ [key]: updated });
  }

  const filteredCategories = categories.filter((category) => category.name.toLowerCase().includes(categorySearch.toLowerCase()))
  const filteredBrands = brands.filter((brand) => brand.name.toLowerCase().includes(brandSearch.toLowerCase()))

  return (
    <Drawer>
      <DrawerTrigger asChild>
        <ModernButton
          variant="outline"
          className={`gap-2 md:hidden h-10 px-4 ${className}`}
        >
          <Filter className="h-5 w-5" />
          Фильтры
          {activeFiltersCount > 0 && (
            <Badge variant="default" className="h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center">
              {activeFiltersCount}
            </Badge>
          )}
        </ModernButton>
      </DrawerTrigger>
      <DrawerContent>
        <DrawerHeader className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5 text-primary" />
              <DrawerTitle>Фильтры</DrawerTitle>
              {activeFiltersCount > 0 && (
                <Badge variant="default" className="h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center">
                  {activeFiltersCount}
                </Badge>
              )}
            </div>
          </div>

          {/* Filter Presets Dropdown */}
          <div className="flex items-center gap-2">
            <FilterPresets variant="dropdown" className="flex-1" />
          </div>

          {/* Quick Filters */}
          <QuickFilters layout="horizontal" className="w-full" />
        </DrawerHeader>

        <div className="relative flex-1 overflow-y-auto">
          <div className="absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-background to-transparent pointer-events-none z-10" />
          <div className="px-4 space-y-3 pb-4">
            {/* Active Filters */}
            {activeFiltersCount > 0 && (
              <ModernCard variant="glass" className="border-primary/20 bg-primary/5">
                <ModernCardContent className="p-3">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm font-medium">Активные фильтры</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {filters.categoryIds.length > 0 && (<Badge variant="secondary" className="text-sm h-5">Категории: {filters.categoryIds.length}</Badge>)}
                    {filters.brandIds.length > 0 && (<Badge variant="secondary" className="text-sm h-5">Бренды: {filters.brandIds.length}</Badge>)}
                    {filters.attributeFilters && Object.keys(filters.attributeFilters).length > 0 && (<Badge variant="secondary" className="text-sm h-5">Параметры: {Object.keys(filters.attributeFilters).length}</Badge>)}
                    {filters.isOemOnly && (<Badge variant="secondary" className="text-sm h-5">Только OEM</Badge>)}
                  </div>
                </ModernCardContent>
              </ModernCard>
            )}

            {/* Categories */}
            <ModernCard variant="default">
              <Collapsible open={expandedSections.categories} onOpenChange={() => toggleSection("categories")}>
                <CollapsibleTrigger asChild>
                  <ModernCardHeader className="cursor-pointer p-4 hover:bg-accent/5 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <ModernCardTitle className="text-sm">Категории</ModernCardTitle>
                        {filters.categoryIds.length > 0 && (<Badge variant="default" className="h-4 w-4 rounded-full p-0 text-xs flex items-center justify-center">{filters.categoryIds.length}</Badge>)}
                      </div>
                      {expandedSections.categories ? (<ChevronUp className="h-4 w-4 text-muted-foreground" />) : (<ChevronDown className="h-4 w-4 text-muted-foreground" />)}
                    </div>
                  </ModernCardHeader>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <ModernCardContent className="p-4 pt-0 space-y-2">
                    {categories.length > 5 && (
                      <div className="relative">
                        <Search className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <ModernInput placeholder="Поиск категорий..." value={categorySearch} onChange={(e) => setCategorySearch(e.target.value)} className="pl-8 h-10 text-sm" variant="ghost" />
                      </div>
                    )}

                    {filteredCategories.map((category) => (
                      <div key={category.id} className="flex items-center space-x-2 group p-2 -m-2">
                        <Checkbox id={`category-${category.id}`} checked={filters.categoryIds.includes(category.id)} onCheckedChange={() => handleToggle('categoryIds', category.id)} className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-4 w-4" />
                        <Label htmlFor={`category-${category.id}`} className="text-sm cursor-pointer flex-1 group-hover:text-primary transition-colors">{category.name}</Label>
                      </div>
                    ))}
                  </ModernCardContent>
                </CollapsibleContent>
              </Collapsible>
            </ModernCard>

            {/* Brands */}
            <ModernCard variant="default">
              <Collapsible open={expandedSections.brands} onOpenChange={() => toggleSection("brands")}>
                <CollapsibleTrigger asChild>
                  <ModernCardHeader className="cursor-pointer p-4 hover:bg-accent/5 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <ModernCardTitle className="text-sm">Производители</ModernCardTitle>
                        {(filters.brandIds.length > 0 || filters.isOemOnly) && (<Badge variant="default" className="h-4 w-4 rounded-full p-0 text-xs flex items-center justify-center">{filters.brandIds.length + (filters.isOemOnly ? 1 : 0)}</Badge>)}
                      </div>
                      {expandedSections.brands ? (<ChevronUp className="h-4 w-4 text-muted-foreground" />) : (<ChevronDown className="h-4 w-4 text-muted-foreground" />)}
                    </div>
                  </ModernCardHeader>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <ModernCardContent className="p-4 pt-0 space-y-2">
                    <div className="flex items-center space-x-2 p-2 rounded bg-accent/5 border border-border/30">
                      <Checkbox id="oem-only" checked={filters.isOemOnly} onCheckedChange={(checked) => { triggerHaptic(10); updateFilters({ isOemOnly: !!checked }); }} className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-4 w-4" />
                      <Label htmlFor="oem-only" className="text-sm cursor-pointer font-medium">Только OEM производители</Label>
                    </div>

                    <Separator className="bg-border/30" />

                    {brands.length > 5 && (
                      <div className="relative">
                        <Search className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <ModernInput placeholder="Поиск брендов..." value={brandSearch} onChange={(e) => setBrandSearch(e.target.value)} className="pl-8 h-10 text-sm" variant="ghost" />
                      </div>
                    )}

                    {filteredBrands.map((brand) => (
                      <div key={brand.id} className="flex items-center space-x-2 group p-2 -m-2">
                        <Checkbox id={`brand-${brand.id}`} checked={filters.brandIds.includes(brand.id)} onCheckedChange={() => handleToggle('brandIds', brand.id)} className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-4 w-4" />
                        <Label htmlFor={`brand-${brand.id}`} className="text-sm cursor-pointer flex-1 group-hover:text-primary transition-colors flex items-center gap-1">
                          {brand.name}
                          {brand.isOem && (<Badge variant="outline" className="text-xs h-4">OEM</Badge>)}
                        </Label>
                      </div>
                    ))}
                  </ModernCardContent>
                </CollapsibleContent>
              </Collapsible>
            </ModernCard>

            {/* Technical Parameters */}
            <ModernCard variant="default">
              <Collapsible open={showAdvancedFilters} onOpenChange={setShowAdvancedFilters}>
                <CollapsibleTrigger asChild>
                  <ModernCardHeader className="cursor-pointer p-4 hover:bg-accent/5 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Sliders className="h-4 w-4 text-primary" />
                        <ModernCardTitle className="text-sm">Технические параметры</ModernCardTitle>
                        {filters.attributeFilters && Object.keys(filters.attributeFilters).length > 0 && (
                          <Badge variant="default" className="h-4 w-4 rounded-full p-0 text-xs flex items-center justify-center">{Object.keys(filters.attributeFilters).length}</Badge>
                        )}
                      </div>
                      {showAdvancedFilters ? (<ChevronUp className="h-4 w-4 text-muted-foreground" />) : (<ChevronDown className="h-4 w-4 text-muted-foreground" />)}
                    </div>
                  </ModernCardHeader>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <ModernCardContent className="p-4 pt-0 space-y-3">
                    {attributeTemplates.map((template) => {
                      const numericRange = metadata.numericRanges[template.id]
                      return (
                        <AttributeFilter
                          key={template.id}
                          template={template}
                          selectedValues={filters.attributeFilters?.[template.id]?.values || []}
                          numericRange={filters.attributeFilters?.[template.id]?.numericRange as any}
                          onValuesChange={(values) => {
                            updateFilters({ attributeFilters: { ...(filters.attributeFilters || {}), [template.id]: { ...(filters.attributeFilters?.[template.id] || {}), values } } })
                          }}
                          onRangeChange={(range) => {
                            updateFilters({ attributeFilters: { ...(filters.attributeFilters || {}), [template.id]: { ...(filters.attributeFilters?.[template.id] || {}), numericRange: range as [number, number] } } })
                          }}
                          availableValues={metadata.availableValues[template.id] || []}
                          availableValuesWithCounts={metadata.availableValuesWithCounts?.[template.id]}
                          numericStats={numericRange ? { min: numericRange.min, max: numericRange.max, avg: (numericRange.min + numericRange.max) / 2 } : undefined}
                        />
                      )
                    })}
                  </ModernCardContent>
                </CollapsibleContent>
              </Collapsible>
            </ModernCard>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-background to-transparent pointer-events-none z-10" />
        </div>

        {/* Sticky Footer with Action Buttons */}
        <DrawerFooter className="border-t sticky bottom-0 bg-background">
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between text-sm text-muted-foreground px-2">
              <span>Результатов: {metadata.filteredResults}</span>
              {activeFiltersCount > 0 && (
                <span>{activeFiltersCount} активных фильтров</span>
              )}
            </div>
            <div className="flex items-center gap-2">
              {activeFiltersCount > 0 && (
                <ModernButton
                  variant="outline"
                  onClick={() => {
                    clearFilters();
                    triggerHaptic(20);
                  }}
                  className="flex-1 h-11"
                >
                  <X className="h-4 w-4 mr-2" />
                  Сбросить все
                </ModernButton>
              )}
              <ModernButton
                variant="default"
                onClick={() => {
                  triggerHaptic(15);
                  // Drawer auto-closes
                }}
                className="flex-1 h-11"
              >
                Применить
              </ModernButton>
            </div>
          </div>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}
