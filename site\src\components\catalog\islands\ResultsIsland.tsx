"use client"

import { useState, useEffect, useMemo } from "react"
import { BarChart3, Grid3X3, List, Package, Database, Layers, TrendingUp, ArrowUp, Filter as FilterIcon, Download } from "lucide-react"
import { ModernButton } from "@/components/ui/modern-button"
import { ModernCard, ModernCardContent } from "@/components/ui/modern-card"
import { useCatalogSearch } from "../pro/useCatalogSearch"
import { useCatalogMetadata } from "@/hooks/useCatalogData"
import { MobileFiltersDrawer } from "../pro/MobileFiltersDrawer"
import { TrpcBoundary } from "@/components/providers/TrpcBoundary"
import { ProUpsellBanner } from "@/components/subscription/ProUpsellBanner"
import { PartCard } from '../PartCard'
import CatalogItemCard from '../cards/CatalogItemCard'
import { LoadingState } from '@/components/shared/LoadingState'
import { FilterSystem } from "../filters/FilterSystem"
import { useIsMobile } from '@/hooks/useMediaQuery'
import { usePullToRefresh } from '@/hooks/usePullToRefresh'
import { PullToRefreshIndicator, FloatingActionButton, BottomSheet } from '@/components/mobile'
import { useCatalogGlobalState } from '@/lib/catalog-state'
import { analytics } from "@/lib/analytics"
import { useListVirtualization } from '@/hooks/useVirtualization'
import { useRenderTime } from '@/hooks/usePerformance'

export default function ResultsIsland() {
  return (
    <TrpcBoundary>
      <ResultsIslandInner />
    </TrpcBoundary>
  )
}

function ResultsIslandInner() {
  const { results, isLoading, totalCount, clearFilters, filters, setFilters, updateFilters, availableAttributeValues, isPro, resultType, metadata } = useCatalogSearch()
  const { categories, brands, templates } = useCatalogMetadata()
  const isMobile = useIsMobile()
  const { viewMode: globalViewMode, setViewMode: setGlobalViewMode } = useCatalogGlobalState()
  const [viewMode, setViewModeLocal] = useState(globalViewMode)
  const [showScrollTop, setShowScrollTop] = useState(false)
  const [showQuickActions, setShowQuickActions] = useState(false)

  const { renderTime } = useRenderTime('ResultsIsland', import.meta.env.DEV)

  // Sync local view mode with global

  // Sync local view mode with global
  useEffect(() => {
    setViewModeLocal(globalViewMode)
  }, [globalViewMode])

  const handleViewModeChange = (mode: 'detailed' | 'grid' | 'table') => {
    setViewModeLocal(mode)
    setGlobalViewMode(mode)
    analytics.viewModeChanged(mode)
  }

  // Pull to refresh
  const { containerRef, isPulling, pullDistance, isRefreshing } = usePullToRefresh({
    onRefresh: async () => {
      // Refetch results - would need to expose refetch from useCatalogSearch
      // For now, trigger re-fetch by updating filters timestamp
      updateFilters({ ...filters })
      await new Promise(resolve => setTimeout(resolve, 1000))
    },
    enabled: isMobile,
    threshold: 80,
  })

  // Track scroll position for FAB
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const activeFiltersCount = filters.categoryIds.length +
    filters.brandIds.length +
    Object.keys(filters.attributeFilters || {}).length +
    (filters.isOemOnly ? 1 : 0)

  const itemHeight = useMemo(() => {
    // Высота зависит от viewMode и типа результата
    if (viewMode === 'grid') return 280 // Компактные карточки в grid
    if (viewMode === 'detailed') return isPro ? 180 : 220 // Detailed карточки (Part меньше, CatalogItem больше)
    return 150 // Table view (не используется в ResultsIsland, но на будущее)
  }, [viewMode, isPro])

  const { virtualizer, virtualItems, totalSize, containerRef: virtualizerRef } = useListVirtualization({
    items: results,
    itemHeight,
    enabled: results.length > 20, // Виртуализация только для >20 элементов
    overscan: 5,
  })

  return (
    <FilterSystem
      filters={filters}
      setFilters={setFilters}
      updateFilters={updateFilters}
      clearFilters={clearFilters}
      metadata={metadata}
      categories={categories}
      brands={brands}
      attributeTemplates={templates}
    >
      <div ref={(el) => { (containerRef as any).current = el; (virtualizerRef as any).current = el; }} className="flex-1 overflow-y-auto">
        {isMobile && (
          <PullToRefreshIndicator
            pullDistance={pullDistance}
            threshold={80}
            isRefreshing={isRefreshing}
          />
        )}
        <div className="container max-w-none p-3 md:p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <h2 className="text-lg md:text-xl font-bold tracking-tight">
                Всего {isPro ? 'групп' : 'артикулов'}: {totalCount}
              </h2>
            </div>

            <div className="flex items-center gap-2">
              {/* Мобильный фильтр drawer - использует FilterSystem context */}
              <MobileFiltersDrawer />

              <div className="flex items-center gap-1 p-1 bg-muted/50 rounded border border-border/40">
                <ModernButton
                  variant={viewMode === "detailed" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => handleViewModeChange("detailed")}
                  className="h-8 w-8 md:h-7 md:w-7 p-0"
                >
                  <List className="h-4 w-4 md:h-3 md:w-3" />
                </ModernButton>
                <ModernButton
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => handleViewModeChange("grid")}
                  className="h-8 w-8 md:h-7 md:w-7 p-0"
                >
                  <Grid3X3 className="h-4 w-4 md:h-3 md:w-3" />
                </ModernButton>
                {!isMobile && (
                  <ModernButton
                    variant={viewMode === "table" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => handleViewModeChange("table")}
                    className="h-7 w-7 p-0"
                  >
                    <BarChart3 className="h-3 w-3" />
                  </ModernButton>
                )}
              </div>
            </div>
          </div>

          {isLoading && <LoadingState variant="card" count={5} />}

          {!isLoading && results.length === 0 ? (
            <ModernCard variant="elevated" className="text-center py-12 border-2 border-dashed border-border-strong">
              <ModernCardContent>
                <div className="flex flex-col items-center gap-3">
                  <div className="h-12 w-12 rounded-full bg-muted/50 flex items-center justify-center">
                    <Database className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-1">
                      {isPro ? 'Группы не найдены' : 'Артикулы не найдены'}
                    </h3>
                    <p className="text-muted-foreground text-sm max-w-md">Попробуйте изменить критерии поиска или сбросить фильтры.</p>
                  </div>
                  <ModernButton variant="outline" onClick={clearFilters} size="sm">
                    Очистить фильтры
                  </ModernButton>
                </div>
              </ModernCardContent>
            </ModernCard>
          ) : (
            <div
              className="relative"
              style={{ height: `${totalSize}px` }}
            >
              <div
                className="absolute top-0 left-0 w-full"
                style={{
                  transform: `translateY(${(virtualItems[0] as any)?.start ?? 0}px)`,
                }}
              >
                <div className="space-y-2 md:space-y-3">
                  {(virtualItems as any[]).map((virtualItem) => {
                    const item = results[virtualItem.index]
                    const index = virtualItem.index
                    
                    return (
                      <div
                        key={virtualItem.key}
                        data-index={index}
                        ref={virtualizer?.measureElement}
                        style={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          transform: `translateY(${virtualItem.start}px)`,
                        }}
                      >
                        {isPro ? (
                          <PartCard
                            part={item}
                            animationDelay={0} // Отключить stagger для виртуализации
                          />
                        ) : (
                          <div>
                            <CatalogItemCard
                              item={item}
                              layout={viewMode === 'grid' ? 'compact' : 'detailed'}
                              animationDelay={0}
                              showProUpsell={true}
                            />
                            {(index + 1) % 4 === 0 && index !== results.length - 1 && (
                              <ProUpsellBanner
                                variant="compact"
                                title="Хотите увидеть группы взаимозаменяемости?"
                                description="Оформите PRO подписку для доступа к эталонным группам Part и расширенному поиску"
                                features={[
                                  "Поиск по группам взаимозаменяемости",
                                  "Сравнение аналогов по эталонным атрибутам",
                                  "AI-ассистент для умного поиска",
                                  "Экспорт данных в Excel/CSV"
                                ]}
                                ctaText="Узнать больше о PRO"
                              />
                            )}
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          )}
        </div>
        {isMobile && (
          <>
            <FloatingActionButton
              actions={[
                {
                  icon: ArrowUp,
                  label: 'Наверх',
                  onClick: () => window.scrollTo({ top: 0, behavior: 'smooth' }),
                  variant: 'secondary',
                },
                {
                  icon: FilterIcon,
                  label: 'Фильтры',
                  onClick: () => setShowQuickActions(true),
                  badge: activeFiltersCount,
                  variant: 'primary',
                },
              ]}
              position="bottom-right"
              expandDirection="up"
            />
            
            <BottomSheet
              isOpen={showQuickActions}
              onClose={() => setShowQuickActions(false)}
              title="Быстрые действия"
              snapPoints={[0.4, 0.7]}
            >
              <div className="space-y-4">
                <ModernButton
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    clearFilters();
                    setShowQuickActions(false);
                  }}
                >
                  Сбросить фильтры
                </ModernButton>
              </div>
            </BottomSheet>
          </>
        )}
      </div>
    </FilterSystem>
  )
}
