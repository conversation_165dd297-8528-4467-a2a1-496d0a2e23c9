import { test, expect } from '@playwright/test'
import { getSystemDB } from '../../api/db'
import { compareAttributes, type AttributeWithTemplate, type SynonymEntry, type SynonymGroupInfo } from '../../api/lib/matching/attributeComparator'

const uniqKey = (t: number, v: string) => `${t}__${v.trim().toLowerCase()}`

test('catalogItem #53 vs #54 seal_type TC vs TCV should match via synonyms (including hierarchy, multi-mapping)', async () => {
  const db = getSystemDB()
  const items = await db.catalogItem.findMany({ where: { id: { in: [53, 54] } }, include: { attributes: { include: { template: true } } } })
  expect(items.length).toBeGreaterThanOrEqual(2)
  const it53 = items.find(x => x.id === 53)!
  const it54 = items.find(x => x.id === 54)!

  // Find seal_type template for these items
  const findSeal = (it: typeof items[number]) => it.attributes.find(a => a.template.name === 'seal_type' || a.template.name.toLowerCase().includes('seal') || a.template.title?.toLowerCase().includes('seal'))
  const a53 = findSeal(it53)!
  const a54 = findSeal(it54)!
  expect(a53?.templateId).toBe(a54?.templateId)

  const tId = a53.templateId

  // Load synonyms and groups for this template
  const synonyms = await db.attributeSynonym.findMany({ where: { group: { templateId: tId } }, include: { group: true } })
  const groups = await db.attributeSynonymGroup.findMany({ where: { templateId: tId } })
  const groupsById = new Map<number, SynonymGroupInfo>(groups.map(g => [g.id, { parentId: g.parentId ?? null, level: g.compatibilityLevel as any, notes: g.notes ?? null, canonical: g.canonicalValue ?? null }]))

  // Build multi-mapping index value -> SynonymEntry[]
  const synonymMap = new Map<string, SynonymEntry[]>()
  for (const s of synonyms) {
    const lvl = (s as any).compatibilityLevel ?? s.group.compatibilityLevel
    const key = uniqKey(s.group.templateId, s.value)
    const arr = synonymMap.get(key) || []
    arr.push({ templateId: s.group.templateId, groupId: s.groupId, level: lvl as any, notes: (s as any).notes ?? null, canonical: (s.group as any).canonicalValue ?? null })
    synonymMap.set(key, arr)
  }

  const itemAttrByTemplateId = new Map<number, AttributeWithTemplate>([[tId, { templateId: tId, value: a53.value, numericValue: null, template: { name: a53.template.name, title: a53.template.title, dataType: 'STRING' as const, tolerance: null } }]])
  const partAttrByTemplateId = new Map<number, AttributeWithTemplate>([[tId, { templateId: tId, value: a54.value, numericValue: null, template: { name: a54.template.name, title: a54.template.title, dataType: 'STRING' as const, tolerance: null } }]])

  const res = compareAttributes({ itemAttrByTemplateId, partAttrByTemplateId, synonymMap, groupsById })
  expect(res.ok).toBeTruthy()
  expect(res.details?.[0]?.kind).toMatch(/STRING_SYNONYM_|STRING_EXACT/)
})

