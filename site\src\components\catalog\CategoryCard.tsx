import { Badge } from '@/components/ui/badge';
import type { PartCategoryItem } from '@/types/catalog';
import BaseCard from './cards/BaseCard';
import { createCardClickHandler, createCategoryUrl } from './cards/card-utils';

interface CategoryCardProps {
  category: PartCategoryItem;
  onClick?: () => void;
}

export function CategoryCard({ category, onClick }: CategoryCardProps) {
  const handleClick = createCardClickHandler(createCategoryUrl(category.slug), onClick);

  // Prepare badges
  const badges = [
    { label: `Уровень ${category.level}`, variant: 'secondary' as const },
  ];

  if (category._count) {
    badges.push({ label: `${category._count.parts} запчастей`, variant: 'outline' as const });
  }

  // Prepare title with icon
  const title = (
    <span className="flex items-center gap-2">
      {category.icon && <span className="text-xl">{category.icon}</span>}
      {category.name}
    </span>
  );

  return (
    <BaseCard
      variant="category"
      layout="detailed"
      onClick={handleClick}
    >
      <div className="flex items-start gap-4">
        {/* Content */}
        <div className="flex-1 min-w-0 space-y-3">
          {/* Header */}
          <BaseCard.Header 
            title={title}
            description={category.description}
          />

          {/* Badges */}
          <BaseCard.Badges badges={badges} />

          {/* Subcategories */}
          {category.children && category.children.length > 0 && (
            <BaseCard.Body>
              <div className="text-xs text-muted-foreground mb-2">Подкатегории:</div>
              <div className="flex flex-wrap gap-1">
                {category.children.slice(0, 3).map((child) => (
                  <Badge key={child.id} variant="outline" className="text-xs">
                    {child.name}
                  </Badge>
                ))}
                {category.children.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{category.children.length - 3}
                  </Badge>
                )}
              </div>
            </BaseCard.Body>
          )}
        </div>

        {/* Media on the right */}
        {category.image && (
          <BaseCard.Media mediaAsset={category.image} size="md" />
        )}
      </div>
    </BaseCard>
  );
}
