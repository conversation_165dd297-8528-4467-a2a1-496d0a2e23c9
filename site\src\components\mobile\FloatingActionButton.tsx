import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Menu } from 'lucide-react';
import { ModernButton } from '@/components/ui/modern-button';
import { useIsMobile } from '@/hooks/useMediaQuery';
import { staggerContainer, scaleIn } from '@/lib/animation-variants';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export interface FloatingActionButtonProps {
  actions: Array<{
    icon: React.ComponentType<{ className?: string }>;
    label: string;
    onClick: () => void;
    badge?: number;
    variant?: 'primary' | 'secondary';
  }>;
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
  expandDirection?: 'up' | 'left' | 'right';
  className?: string;
}

export function FloatingActionButton({
  actions,
  position = 'bottom-right',
  expandDirection = 'up',
  className,
}: FloatingActionButtonProps) {
  const isMobile = useIsMobile();
  const [isOpen, setIsOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  if (!isMobile) {
    return null;
  }

  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-center': 'bottom-4 left-1/2 -translate-x-1/2',
  };

  const expandClasses = {
    up: 'flex-col-reverse items-center',
    left: 'flex-row-reverse items-center',
    right: 'flex-row items-center',
  };

  const toggleOpen = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  return (
    <>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/30 z-40"
            onClick={() => setIsOpen(false)}
          />
        )}
      </AnimatePresence>
      <div
        ref={ref}
        className={cn(
          'fixed z-50 flex gap-4',
          positionClasses[position],
          expandClasses[expandDirection],
          className
        )}
      >
        <AnimatePresence>
          {isOpen && (
            <motion.div
              variants={staggerContainer(0.1, 0.1)}
              initial="hidden"
              animate="show"
              exit="hidden"
              className={cn('flex gap-3', expandClasses[expandDirection])}
            >
              {actions.map((action, index) => (
                <motion.div variants={scaleIn} key={index}>
                  <TooltipProvider>
                    <Tooltip delayDuration={0}>
                      <TooltipTrigger asChild>
                        <div className="relative">
                          <ModernButton
                            size="icon"
                            variant={action.variant === 'primary' ? 'default' : 'secondary'}
                            onClick={() => {
                              action.onClick();
                              setIsOpen(false);
                            }}
                            className="w-12 h-12 rounded-full shadow-lg"
                          >
                            <action.icon className="h-6 w-6" />
                          </ModernButton>
                          {action.badge !== undefined && action.badge > 0 && (
                            <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                              {action.badge}
                            </div>
                          )}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side={expandDirection === 'up' ? 'left' : 'top'}>
                        <p>{action.label}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </motion.div>
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        <ModernButton
          size="icon"
          onClick={toggleOpen}
          className="w-14 h-14 rounded-full shadow-xl"
        >
          <AnimatePresence initial={false} mode="wait">
            <motion.div
              key={isOpen ? 'x' : 'menu'}
              initial={{ rotate: -45, opacity: 0, scale: 0.5 }}
              animate={{ rotate: 0, opacity: 1, scale: 1 }}
              exit={{ rotate: 45, opacity: 0, scale: 0.5 }}
              transition={{ duration: 0.2 }}
            >
              {isOpen ? <X className="h-7 w-7" /> : <Menu className="h-7 w-7" />}
            </motion.div>
          </AnimatePresence>
        </ModernButton>
      </div>
    </>
  );
}

export default FloatingActionButton;