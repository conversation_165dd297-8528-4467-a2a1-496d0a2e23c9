import { navigate } from 'astro:transitions/client'
import { formatDate, formatRelativeTime, formatAttributeValue } from '@/lib/formatters'
import type { BadgeProps } from '@/components/ui/badge'

/**
 * Type definitions for card utilities
 */
export interface CardBadge {
  label: string
  variant: BadgeProps['variant']
  icon?: React.ComponentType<{ className?: string }>
  className?: string
}

export interface CardAttribute {
  label: string
  value: string
  unit?: string
}

export interface CardMetadata {
  icon?: React.ComponentType<{ className?: string }>
  label: string
  value: string
}

/**
 * Date Formatting
 */

/**
 * Format date for card display using short format
 */
export function formatCardDate(date: Date | string | null): string {
  if (!date) return ''
  return formatDate(date, 'short')
}

/**
 * Format date as relative time (e.g., "2 часа назад")
 */
export function formatRelativeCardDate(date: Date | string): string {
  return formatRelativeTime(date)
}

/**
 * Badge Helpers
 */

/**
 * Create badge props for OEM/Aftermarket
 */
export function createOemBadge(isOem: boolean): CardBadge {
  return {
    label: isOem ? 'OEM' : 'Aftermarket',
    variant: isOem ? 'default' : 'secondary',
  }
}

/**
 * Create badge props for counts (e.g., "5 аналогов")
 */
export function createCountBadge(
  count: number, 
  label: string, 
  icon?: React.ComponentType<{ className?: string }>
): CardBadge {
  return {
    label: `${count} ${label}`,
    variant: 'outline',
    icon,
  }
}

/**
 * Create badge props for category
 */
export function createCategoryBadge(categoryName: string): CardBadge {
  return {
    label: categoryName,
    variant: 'secondary',
  }
}

/**
 * Create badge props for brand
 */
export function createBrandBadge(brandName: string): CardBadge {
  return {
    label: brandName,
    variant: 'default',
  }
}

/**
 * Create badge props for SKU with monospace font
 */
export function createSkuBadge(sku: string): CardBadge {
  return {
    label: sku,
    variant: 'outline',
    className: 'font-mono',
  }
}

/**
 * Attribute Helpers
 */

/**
 * Format attribute for card display
 */
export function formatAttributeForCard(attr: {
  template: { title: string; unit?: string | null }
  value: string
}): CardAttribute {
  return {
    label: attr.template.title,
    value: formatAttributeValue(attr.value, attr.template.unit || undefined),
    unit: attr.template.unit || undefined,
  }
}

/**
 * Get first N attributes for display
 */
export function getTopAttributes<T>(attributes: T[], maxCount: number): T[] {
  return attributes.slice(0, maxCount)
}

/**
 * Calculate remaining attributes count
 */
export function getRemainingAttributesCount<T>(attributes: T[], maxCount: number): number {
  return Math.max(0, attributes.length - maxCount)
}

/**
 * Navigation Helpers
 */

/**
 * Generate Part detail URL
 */
export function createPartUrl(partId: number): string {
  return `/catalog/parts/${partId}`
}

/**
 * Generate CatalogItem detail URL
 */
export function createCatalogItemUrl(itemId: number): string {
  return `/catalog/items/${itemId}`
}

/**
 * Generate Category URL
 */
export function createCategoryUrl(categorySlug: string): string {
  return `/catalog/categories/${categorySlug}`
}

/**
 * Generate Brand URL
 */
export function createBrandUrl(brandSlug: string): string {
  return `/catalog/brands/${brandSlug}`
}

/**
 * Handle navigation with optional custom onClick
 */
export function handleCardNavigation(url: string, onClick?: () => void): void {
  if (onClick) {
    onClick()
  }
  navigate(url)
}

/**
 * Click Handler Factory
 */

/**
 * Create click handler that calls customOnClick first, then navigates
 */
export function createCardClickHandler(
  url: string,
  customOnClick?: () => void
): () => void {
  return () => {
    if (customOnClick) {
      customOnClick()
    }
    navigate(url)
  }
}

/**
 * Metadata Helpers
 */

/**
 * Create metadata object for "Обновлено" display
 */
export function createUpdateMetadata(
  updatedAt: Date | string,
  icon?: React.ComponentType<{ className?: string }>
): CardMetadata {
  return {
    icon,
    label: 'Обновлено',
    value: formatCardDate(updatedAt),
  }
}

/**
 * Create metadata object for count display
 */
export function createCountMetadata(
  count: number,
  label: string,
  icon?: React.ComponentType<{ className?: string }>
): CardMetadata {
  return {
    icon,
    label,
    value: count.toString(),
  }
}

