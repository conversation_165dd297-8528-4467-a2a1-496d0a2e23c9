import React, { forwardRef } from 'react'
import { motion } from 'motion/react'
import { navigate } from 'astro:transitions/client'
import { ImageIcon } from 'lucide-react'
import { ModernCard, ModernCardHeader, ModernCardTitle, ModernCardDescription, ModernCardContent } from '@/components/ui/modern-card'
import { ModernButton } from '@/components/ui/modern-button'
import { Badge, type BadgeProps } from '@/components/ui/badge'
import { MediaThumbnail } from '@/components/catalog/media/MediaThumbnail'
import { LoadingState } from '@/components/shared/LoadingState'
import { useHoverAnimation } from '@/hooks/useAnimations'
import type { MediaAsset } from '@/lib/types'

export interface BaseCardProps {
  variant?: 'part' | 'catalogItem' | 'category' | 'brand' | 'custom'
  layout?: 'compact' | 'detailed' | 'grid'
  onClick?: () => void
  onMouseEnter?: () => void // Новый prop для prefetching
  href?: string
  isLoading?: boolean
  animationDelay?: number
  className?: string
  children: React.ReactNode
}

interface MediaProps {
  mediaAsset?: MediaAsset | null
  size?: 'sm' | 'md' | 'lg'
  fallbackIcon?: React.ComponentType<{ className?: string }>
}

interface HeaderProps {
  title: string | React.ReactNode
  description?: string
  icon?: React.ReactNode
  titleClassName?: string
}

interface BodyProps {
  children: React.ReactNode
  className?: string
}

interface BadgeItem {
  label: string
  variant?: BadgeProps['variant']
  icon?: React.ComponentType<{ className?: string }>
  className?: string
}

interface BadgesProps {
  badges: BadgeItem[]
  maxVisible?: number
}

interface AttributeItem {
  label: string
  value: string
  unit?: string
}

interface AttributesProps {
  attributes: AttributeItem[]
  maxVisible?: number
  layout?: 'list' | 'grid'
}

interface FooterProps {
  children: React.ReactNode
  className?: string
}

interface ActionItem {
  label: string
  onClick: () => void
  variant?: React.ComponentProps<typeof ModernButton>['variant']
  icon?: React.ComponentType<{ className?: string }>
}

interface ActionsProps {
  actions: ActionItem[]
  layout?: 'horizontal' | 'vertical'
}

const BaseCard = forwardRef<HTMLDivElement, BaseCardProps>(
  ({ variant = 'custom', layout = 'detailed', onClick, onMouseEnter, href, isLoading, animationDelay, className, children }, ref) => {
    const isTouchDevice = typeof window !== 'undefined' && 'ontouchstart' in window;
    const hoverProps = !isTouchDevice ? useHoverAnimation() : {};

    const handleClick = () => {
      if (onClick) {
        onClick()
      } else if (href) {
        navigate(href)
      }
    }

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault()
        handleClick()
      }
    }

    const isClickable = !!(onClick || href)

    const layoutClasses = {
      compact: 'p-3 md:p-3',
      detailed: 'p-4 md:p-4',
      grid: 'p-4 flex-col items-center text-center',
    }

    if (isLoading) {
      return <LoadingState variant="card" count={1} />
    }

    return (
      <motion.div
        ref={ref}
        {...hoverProps}
        transition={{
          delay: animationDelay ? animationDelay / 1000 : 0,
          ...(hoverProps.transition || {})
        }}
        onClick={isClickable ? handleClick : undefined}
        onMouseEnter={onMouseEnter}
        onKeyDown={isClickable ? handleKeyDown : undefined}
        tabIndex={isClickable ? 0 : undefined}
        role="article"
        aria-label={typeof children === 'string' ? children : undefined}
        className={`${className} active:scale-[0.98] transition-transform duration-100`}
      >
        <ModernCard
          variant="elevated"
          className={`
            min-h-[80px] md:min-h-0
            hover:shadow-strong transition-all duration-200 hover:border-primary/20
            ${isClickable ? 'cursor-pointer' : ''}
            ${layoutClasses[layout]}
          `}
        >
          {children}
        </ModernCard>
      </motion.div>
    )
  }
)

BaseCard.displayName = 'BaseCard'

const Media: React.FC<MediaProps> = ({ 
  mediaAsset, 
  size = 'md', 
  fallbackIcon: FallbackIcon = ImageIcon 
}) => {
  const sizeClasses = {
    sm: 'w-14 h-14 md:w-12 md:h-12',
    md: 'w-20 h-20 md:w-16 md:h-16',
    lg: 'w-28 h-28 md:w-24 md:h-24',
  }

  return (
    <div className={`flex-shrink-0 ${sizeClasses[size]}`}>
      {mediaAsset ? (
        <MediaThumbnail
          mediaAsset={mediaAsset}
          size={size}
        />
      ) : (
        <div className={`${sizeClasses[size]} flex items-center justify-center bg-muted rounded-lg`}>
          <FallbackIcon className="w-1/2 h-1/2 text-muted-foreground" />
        </div>
      )}
    </div>
  )
}

Media.displayName = 'BaseCard.Media'

const Header: React.FC<HeaderProps> = ({ title, description, icon, titleClassName }) => {
  return (
    <ModernCardHeader className="px-0 pt-0 md:px-0">
      <div className="flex items-start gap-2">
        {icon && <div className="flex-shrink-0">{icon}</div>}
        <div className="flex-1 min-w-0">
          <ModernCardTitle className={`line-clamp-3 md:line-clamp-2 text-base md:text-sm ${titleClassName || ''}`}>
            {title}
          </ModernCardTitle>
          {description && (
            <ModernCardDescription className="line-clamp-2 mt-1 text-sm md:text-xs">
              {description}
            </ModernCardDescription>
          )}
        </div>
      </div>
    </ModernCardHeader>
  )
}

Header.displayName = 'BaseCard.Header'

const Body: React.FC<BodyProps> = ({ children, className }) => {
  return (
    <ModernCardContent className={`space-y-2 px-0 ${className || ''}`}>
      {children}
    </ModernCardContent>
  )
}

Body.displayName = 'BaseCard.Body'

const Badges: React.FC<BadgesProps> = ({ badges, maxVisible }) => {
  const visibleBadges = maxVisible ? badges.slice(0, maxVisible) : badges
  const remainingCount = maxVisible && badges.length > maxVisible ? badges.length - maxVisible : 0

  return (
    <div className="flex flex-wrap gap-2 md:gap-1.5">
      {visibleBadges.map((badge, index) => {
        const Icon = badge.icon
        return (
          <Badge
            key={index}
            variant={badge.variant || 'default'}
            className={`text-xs md:text-[10px] ${badge.className}`}
          >
            {Icon && <Icon className="w-3 h-3 mr-1" />}
            {badge.label}
          </Badge>
        )
      })}
      {remainingCount > 0 && (
        <Badge variant="outline">+{remainingCount} ещё</Badge>
      )}
    </div>
  )
}

Badges.displayName = 'BaseCard.Badges'

const Attributes: React.FC<AttributesProps> = ({ attributes, maxVisible, layout = 'list' }) => {
  const visibleAttributes = maxVisible ? attributes.slice(0, maxVisible) : attributes
  const remainingCount = maxVisible && attributes.length > maxVisible ? attributes.length - maxVisible : 0

  const layoutClasses = {
    list: 'space-y-1',
    grid: 'grid grid-cols-2 gap-2',
  }

  return (
    <div>
      <div className={layoutClasses[layout]}>
        {visibleAttributes.map((attr, index) => (
          <div key={index} className="flex justify-between text-sm">
            <span className="text-muted-foreground">{attr.label}:</span>
            <span className="font-medium">
              {attr.value}
              {attr.unit && ` ${attr.unit}`}
            </span>
          </div>
        ))}
      </div>
      {remainingCount > 0 && (
        <div className="text-xs text-muted-foreground mt-2">
          +{remainingCount} характеристик
        </div>
      )}
    </div>
  )
}

Attributes.displayName = 'BaseCard.Attributes'

const Footer: React.FC<FooterProps> = ({ children, className }) => {
  return (
    <div className={`border-t pt-3 mt-3 flex items-center gap-4 text-xs text-muted-foreground ${className || ''}`}>
      {children}
    </div>
  )
}

Footer.displayName = 'BaseCard.Footer'

const Actions: React.FC<ActionsProps> = ({ actions, layout = 'horizontal' }) => {
  const layoutClasses = {
    horizontal: 'flex flex-col sm:flex-row gap-2',
    vertical: 'flex flex-col gap-2',
  }

  return (
    <div className={`${layoutClasses[layout]} flex-col sm:flex-row`}>
      {actions.map((action, index) => {
        const Icon = action.icon
        return (
          <ModernButton
            key={index}
            variant={action.variant || 'default'}
            size="sm"
            onClick={(e) => {
              e.stopPropagation()
              action.onClick()
            }}
            className="w-full sm:w-auto h-10 md:h-8"
          >
            {Icon && <Icon className="w-4 h-4 mr-2" />}
            {action.label}
          </ModernButton>
        )
      })}
    </div>
  )
}

Actions.displayName = 'BaseCard.Actions'

export default Object.assign(BaseCard, {
  Media,
  Header,
  Body,
  Badges,
  Attributes,
  Footer,
  Actions,
})

