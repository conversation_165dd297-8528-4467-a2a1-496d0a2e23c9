"use client";

import { TrendingDown, <PERSON>ren<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { ModernCard, ModernCardContent, ModernCardHeader, ModernCardTitle } from "@/components/ui/modern-card";
import {
  aggregateNumericAttribute,
  aggregateStringAttribute,
  getTopValues,
  type NumericStats,
  type AttributeDistribution,
} from "@/lib/aggregators";
import { formatNumber, formatPercentage } from "@/lib/formatters";

// ============================================================================
// Types
// ============================================================================

type AttributeTemplate = {
  id: number;
  name: string;
  title: string;
  dataType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'JSON';
  unit: string | null;
  tolerance: number | null;
};

type BaseAttribute = {
  id: number;
  templateId: number;
  value: string;
  template: AttributeTemplate;
};

export interface AttributeStatsProps {
  attributes: BaseAttribute[];
  templateId: number;
  templateTitle: string;
  dataType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'JSON';
  unit?: string | null;
  layout?: 'compact' | 'detailed';
  showDistribution?: boolean;
  maxDistributionItems?: number;
  className?: string;
}

// ============================================================================
// Numeric Stats Component
// ============================================================================

interface NumericStatsDisplayProps {
  stats: NumericStats;
  layout: 'compact' | 'detailed';
}

function NumericStatsDisplay({ stats, layout }: NumericStatsDisplayProps) {
  if (layout === 'compact') {
    const minStr = formatNumber(stats.min);
    const maxStr = formatNumber(stats.max);
    const avgStr = formatNumber(stats.avg);
    const unit = stats.unit || '';

    return (
      <div className="flex items-center gap-2 text-sm">
        <span className="text-muted-foreground">
          {minStr}–{maxStr} {unit} (средн. {avgStr})
        </span>
        <Badge variant="secondary" className="text-xs">
          {stats.count} значений
        </Badge>
      </div>
    );
  }

  // Detailed layout
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div className="flex flex-col items-center p-3 rounded bg-muted/50 border">
        <TrendingDown className="h-5 w-5 text-blue-500 mb-2" />
        <div className="text-xs text-muted-foreground mb-1">Минимум</div>
        <div className="text-lg font-bold">
          {formatNumber(stats.min)} {stats.unit}
        </div>
      </div>

      <div className="flex flex-col items-center p-3 rounded bg-muted/50 border">
        <TrendingUp className="h-5 w-5 text-green-500 mb-2" />
        <div className="text-xs text-muted-foreground mb-1">Максимум</div>
        <div className="text-lg font-bold">
          {formatNumber(stats.max)} {stats.unit}
        </div>
      </div>

      <div className="flex flex-col items-center p-3 rounded bg-muted/50 border">
        <Minus className="h-5 w-5 text-purple-500 mb-2" />
        <div className="text-xs text-muted-foreground mb-1">Среднее</div>
        <div className="text-lg font-bold">
          {formatNumber(stats.avg)} {stats.unit}
        </div>
      </div>

      <div className="flex flex-col items-center p-3 rounded bg-muted/50 border">
        <Equal className="h-5 w-5 text-orange-500 mb-2" />
        <div className="text-xs text-muted-foreground mb-1">Медиана</div>
        <div className="text-lg font-bold">
          {formatNumber(stats.median)} {stats.unit}
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// String Distribution Component
// ============================================================================

interface StringDistributionDisplayProps {
  distribution: AttributeDistribution[];
  maxItems: number;
  layout: 'compact' | 'detailed';
}

function StringDistributionDisplay({
  distribution,
  maxItems,
  layout,
}: StringDistributionDisplayProps) {
  const topValues = getTopValues(distribution, maxItems);
  const hasMore = distribution.length > maxItems;
  const remainingCount = distribution.length - maxItems;

  if (layout === 'compact') {
    return (
      <div className="flex flex-wrap items-center gap-2">
        <span className="text-sm text-muted-foreground">
          {distribution.length} уникальных значений:
        </span>
        {topValues.slice(0, 3).map((item) => (
          <Badge key={item.value} variant="outline" className="text-xs">
            {item.value}
          </Badge>
        ))}
        {distribution.length > 3 && (
          <Badge variant="secondary" className="text-xs">
            +{distribution.length - 3} еще
          </Badge>
        )}
      </div>
    );
  }

  // Detailed layout
  return (
    <div className="space-y-2">
      {topValues.map((item) => (
        <div key={item.value} className="space-y-1">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">{item.value}</span>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-xs">
                {item.count}
              </Badge>
              <span className="text-xs text-muted-foreground">
                {formatPercentage(item.percentage)}
              </span>
            </div>
          </div>
          <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
            <div
              className="h-full bg-primary"
              style={{ width: `${item.percentage}%` }}
            />
          </div>
        </div>
      ))}
      {hasMore && (
        <div className="text-xs text-muted-foreground text-center pt-2">
          +{remainingCount} значений не показано
        </div>
      )}
    </div>
  );
}

// ============================================================================
// Main Component
// ============================================================================

export default function AttributeStats({
  attributes,
  templateId,
  templateTitle,
  dataType,
  unit,
  layout = 'detailed',
  showDistribution = true,
  maxDistributionItems = 5,
  className = '',
}: AttributeStatsProps) {
  // Filter attributes by templateId
  const relevantAttributes = attributes.filter((attr) => attr.templateId === templateId);

  // Calculate missing count
  const missingCount = relevantAttributes.filter(
    (attr) => !attr.value || attr.value.trim() === ''
  ).length;

  // Empty state
  if (relevantAttributes.length === 0 || relevantAttributes.length === missingCount) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <p className="text-sm text-muted-foreground">Недостаточно данных для статистики</p>
      </div>
    );
  }

  // Numeric statistics
  if (dataType === 'NUMBER') {
    const stats = aggregateNumericAttribute(relevantAttributes, templateId);

    if (!stats) {
      return (
        <div className={`text-center py-8 ${className}`}>
          <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">Нет числовых данных</p>
        </div>
      );
    }

    if (layout === 'compact') {
      return (
        <div className={className}>
          <NumericStatsDisplay stats={stats} layout="compact" />
          {missingCount > 0 && (
            <Badge variant="secondary" className="mt-2 text-xs flex items-center gap-1 w-fit">
              <AlertCircle className="h-3 w-3" />
              {missingCount} пропущено
            </Badge>
          )}
        </div>
      );
    }

    return (
      <ModernCard className={className}>
        <ModernCardHeader>
          <ModernCardTitle className="text-lg flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Статистика: {templateTitle}
          </ModernCardTitle>
        </ModernCardHeader>
        <ModernCardContent className="space-y-4">
          <NumericStatsDisplay stats={stats} layout="detailed" />

          <div className="flex items-center justify-between text-sm pt-2 border-t">
            <span className="text-muted-foreground">Всего значений:</span>
            <Badge variant="secondary">{stats.count}</Badge>
          </div>

          {missingCount > 0 && (
            <Badge variant="secondary" className="text-xs flex items-center gap-1 w-fit">
              <AlertCircle className="h-3 w-3" />
              {missingCount} пропущено
            </Badge>
          )}
        </ModernCardContent>
      </ModernCard>
    );
  }

  // String distribution
  if (dataType === 'STRING' && showDistribution) {
    const distribution = aggregateStringAttribute(relevantAttributes, templateId);

    if (distribution.length === 0) {
      return (
        <div className={`text-center py-8 ${className}`}>
          <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">Нет данных для распределения</p>
        </div>
      );
    }

    if (layout === 'compact') {
      return (
        <div className={className}>
          <StringDistributionDisplay
            distribution={distribution}
            maxItems={maxDistributionItems}
            layout="compact"
          />
          {missingCount > 0 && (
            <Badge variant="secondary" className="mt-2 text-xs flex items-center gap-1 w-fit">
              <AlertCircle className="h-3 w-3" />
              {missingCount} пропущено
            </Badge>
          )}
        </div>
      );
    }

    return (
      <ModernCard className={className}>
        <ModernCardHeader>
          <ModernCardTitle className="text-lg flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Распределение: {templateTitle}
          </ModernCardTitle>
        </ModernCardHeader>
        <ModernCardContent className="space-y-4">
          <StringDistributionDisplay
            distribution={distribution}
            maxItems={maxDistributionItems}
            layout="detailed"
          />

          <div className="flex items-center justify-between text-sm pt-2 border-t">
            <span className="text-muted-foreground">Уникальных значений:</span>
            <Badge variant="secondary">{distribution.length}</Badge>
          </div>

          {missingCount > 0 && (
            <Badge variant="secondary" className="text-xs flex items-center gap-1 w-fit">
              <AlertCircle className="h-3 w-3" />
              {missingCount} пропущено
            </Badge>
          )}
        </ModernCardContent>
      </ModernCard>
    );
  }

  // Other data types
  return (
    <div className={`text-center py-4 ${className}`}>
      <p className="text-sm text-muted-foreground">
        Статистика для типа {dataType} не поддерживается
      </p>
    </div>
  );
}

