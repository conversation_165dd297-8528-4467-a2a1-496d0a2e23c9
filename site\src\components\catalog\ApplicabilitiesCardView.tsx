import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronsUpDown, Filter, X } from 'lucide-react';
import AttributeComparison from '@/components/catalog/attributes/AttributeComparison';
import { ModernCard } from '@/components/ui/modern-card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { staggerContainer, fadeIn } from '@/lib/animation-variants';
import type { Part } from '@/lib/types';
import { ModernButton } from '../ui/modern-button';
import { MediaThumbnail } from '@/components/catalog/media/MediaThumbnail';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EmptyState } from '@/components/shared/EmptyState';
import { LoadingState } from '@/components/shared/LoadingState';
import { useListVirtualization } from '@/hooks/useVirtualization';

type Applicability = Part['applicabilities'][0];

interface ApplicabilitiesCardViewProps {
  applicabilities: Applicability[];
  partAttributes: Part['attributes'];
  onRowSelectionChange?: (selection: Record<string, boolean>) => void;
  className?: string;
}

type SortKey = 'accuracy' | 'brand' | 'sku';
type FilterKey = 'exactMatch' | 'withMedia' | 'oem';

export function ApplicabilitiesCardView({
  applicabilities,
  partAttributes,
  onRowSelectionChange,
  className,
}: ApplicabilitiesCardViewProps) {
  const [openCards, setOpenCards] = useState<Record<string, boolean>>({});
  const [selection, setSelection] = useState<Record<string, boolean>>({});
  const [sortKey, setSortKey] = useState<SortKey>('accuracy');
  const [activeFilters, setActiveFilters] = useState<Set<FilterKey>>(new Set());

  const toggleCard = (id: string) => {
    setOpenCards((prev) => ({ ...prev, [id]: !prev[id] }));
  };

  const handleSelectionChange = (id: string, checked: boolean) => {
    const newSelection = { ...selection, [id]: checked };
    setSelection(newSelection);
    onRowSelectionChange?.(newSelection);
  };

  const toggleFilter = (filter: FilterKey) => {
    const newFilters = new Set(activeFilters);
    if (newFilters.has(filter)) {
      newFilters.delete(filter);
    } else {
      newFilters.add(filter);
    }
    setActiveFilters(newFilters);
  };

  const sortedAndFilteredApplicabilities = useMemo(() => {
    let result = [...applicabilities];

    // Filtering
    if (activeFilters.size > 0) {
      result = result.filter((app) => {
        if (activeFilters.has('exactMatch') && app.matchScore?.percentage !== 100) return false;
        if (activeFilters.has('withMedia') && (!app.catalogItem.mediaAssets || app.catalogItem.mediaAssets.length === 0)) return false;
        if (activeFilters.has('oem') && app.catalogItem.brand?.isOem === false) return false;
        return true;
      });
    }

    // Sorting
    result.sort((a, b) => {
      switch (sortKey) {
        case 'accuracy':
          return (b.matchScore?.percentage ?? 0) - (a.matchScore?.percentage ?? 0);
        case 'brand':
          return (a.catalogItem.brand?.name ?? '').localeCompare(b.catalogItem.brand?.name ?? '');
        case 'sku':
          return a.catalogItem.sku.localeCompare(b.catalogItem.sku);
        default:
          return 0;
      }
    });

    return result;
  }, [applicabilities, sortKey, activeFilters]);

  const cardHeight = useMemo(() => {
    // Collapsed card: ~80px
    // Expanded card: ~300px (зависит от количества атрибутов)
    // Используем среднее значение для estimate
    return 120
  }, [])
  
  const { virtualizer, virtualItems, totalSize, containerRef } = useListVirtualization({
    items: sortedAndFilteredApplicabilities,
    itemHeight: cardHeight,
    enabled: sortedAndFilteredApplicabilities.length > 30,
    overscan: 3,
  })

  const shouldVirtualize = sortedAndFilteredApplicabilities.length > 30

  if (!applicabilities) {
    return <LoadingState variant="card" count={5} />;
  }

  if (sortedAndFilteredApplicabilities.length === 0) {
      return <EmptyState title="Нет каталожных позиций" description="Попробуйте изменить фильтры или сортировку." />;
  }

  return (
    <div className={className}>
      <div className="flex flex-col sm:flex-row justify-between items-center mb-4 gap-2">
        <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-muted-foreground" />
            <ModernButton variant={activeFilters.has('exactMatch') ? 'default' : 'outline'} size="sm" onClick={() => toggleFilter('exactMatch')}>Точное совпадение</ModernButton>
            <ModernButton variant={activeFilters.has('withMedia') ? 'default' : 'outline'} size="sm" onClick={() => toggleFilter('withMedia')}>С фото</ModernButton>
            <ModernButton variant={activeFilters.has('oem') ? 'default' : 'outline'} size="sm" onClick={() => toggleFilter('oem')}>OEM</ModernButton>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <ModernButton variant="outline" size="sm" className="ml-auto">
              <ChevronsUpDown className="w-3 h-3 mr-2" />
              Сортировка: {sortKey === 'accuracy' ? 'Точность' : sortKey === 'brand' ? 'Бренд' : 'Артикул'}
            </ModernButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => setSortKey('accuracy')}>Точность</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setSortKey('brand')}>Бренд</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setSortKey('sku')}>Артикул</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {shouldVirtualize ? (
        <div
          ref={containerRef}
          className="relative"
          style={{ height: `${totalSize}px` }}
        >
          {(virtualItems as any[]).map((virtualItem) => {
            const app = sortedAndFilteredApplicabilities[virtualItem.index]
            
            return (
              <div
                key={virtualItem.key}
                data-index={virtualItem.index}
                ref={virtualizer?.measureElement}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  transform: `translateY(${virtualItem.start}px)`,
                }}
              >
                <ModernCard
                  className={`p-0 overflow-hidden transition-all duration-300 mb-2 ${
                    selection[app.catalogItem.id] ? 'border-primary' : ''
                  }`}
                >
                  <div
                    className="flex items-center p-4 cursor-pointer"
                    onClick={() => toggleCard(app.catalogItem.id)}
                  >
                    <Checkbox
                      className="h-5 w-5 mr-4"
                      checked={selection[app.catalogItem.id] || false}
                      onCheckedChange={(checked) => handleSelectionChange(app.catalogItem.id, !!checked)}
                      onClick={(e) => e.stopPropagation()}
                    />
                    <MediaThumbnail mediaAsset={app.catalogItem.mediaAssets?.[0] ?? null} size="sm" className="mr-4" />
                    <div className="flex-grow">
                      <p className="font-mono font-bold text-sm">{app.catalogItem.sku}</p>
                      <p className="text-xs text-muted-foreground">{app.catalogItem.brand?.name}</p>
                    </div>
                    {app.matchScore && (
                      <Badge variant={app.matchScore.percentage === 100 ? 'success' : 'warning'}>
                        {app.matchScore.percentage}%
                      </Badge>
                    )}
                    <ChevronDown
                      className={`ml-4 h-5 w-5 text-muted-foreground transition-transform ${
                        openCards[app.catalogItem.id] ? 'rotate-180' : ''
                      }`}
                    />
                  </div>
    
                  <AnimatePresence>
                    {openCards[app.catalogItem.id] && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        className="overflow-hidden"
                      >
                        <div className="p-4 border-t">
                          <AttributeComparison
                            partAttributes={partAttributes}
                            catalogItemAttributes={app.catalogItem.attributes}
                            layout="cards"
                          />
                           {app.catalogItem.notes && (
                            <div className="mt-4">
                                <h4 className="font-semibold text-sm mb-2">Примечания:</h4>
                                <p className="text-sm text-muted-foreground">{app.catalogItem.notes}</p>
                            </div>
                           )}
                           <div className="mt-4 flex justify-end">
                                <ModernButton size="sm" asChild>
                                    <a href={`/site/catalog/${app.catalogItem.id}`}>Перейти к товару</a>
                                </ModernButton>
                           </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </ModernCard>
              </div>
            )
          })}
        </div>
      ) : (
        <motion.div
            variants={staggerContainer()}
            initial="hidden"
            animate="show"
            className="space-y-3"
        >
        {sortedAndFilteredApplicabilities.map((app, index) => (
          <motion.div variants={fadeIn} key={app.catalogItem.id}>
            <ModernCard
              className={`p-0 overflow-hidden transition-all duration-300 ${
                selection[app.catalogItem.id] ? 'border-primary' : ''
              }`}
            >
              <div
                className="flex items-center p-4 cursor-pointer"
                onClick={() => toggleCard(app.catalogItem.id)}
              >
                <Checkbox
                  className="h-5 w-5 mr-4"
                  checked={selection[app.catalogItem.id] || false}
                  onCheckedChange={(checked) => handleSelectionChange(app.catalogItem.id, !!checked)}
                  onClick={(e) => e.stopPropagation()}
                />
                <MediaThumbnail mediaAsset={app.catalogItem.mediaAssets?.[0] ?? null} size="sm" className="mr-4" />
                <div className="flex-grow">
                  <p className="font-mono font-bold text-sm">{app.catalogItem.sku}</p>
                  <p className="text-xs text-muted-foreground">{app.catalogItem.brand?.name}</p>
                </div>
                {app.matchScore && (
                  <Badge variant={app.matchScore.percentage === 100 ? 'success' : 'warning'}>
                    {app.matchScore.percentage}%
                  </Badge>
                )}
                <ChevronDown
                  className={`ml-4 h-5 w-5 text-muted-foreground transition-transform ${
                    openCards[app.catalogItem.id] ? 'rotate-180' : ''
                  }`}
                />
              </div>

              <AnimatePresence>
                {openCards[app.catalogItem.id] && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="overflow-hidden"
                  >
                    <div className="p-4 border-t">
                      <AttributeComparison
                        partAttributes={partAttributes}
                        catalogItemAttributes={app.catalogItem.attributes}
                        layout="cards"
                      />
                       {app.catalogItem.notes && (
                        <div className="mt-4">
                            <h4 className="font-semibold text-sm mb-2">Примечания:</h4>
                            <p className="text-sm text-muted-foreground">{app.catalogItem.notes}</p>
                        </div>
                       )}
                       <div className="mt-4 flex justify-end">
                            <ModernButton size="sm" asChild>
                                <a href={`/site/catalog/${app.catalogItem.id}`}>Перейти к товару</a>
                            </ModernButton>
                       </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </ModernCard>
          </motion.div>
        ))}
      </motion.div>
      )}
    </div>
  );
}

export default ApplicabilitiesCardView;