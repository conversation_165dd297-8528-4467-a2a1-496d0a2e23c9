import { memo } from 'react'
import { motion } from 'framer-motion'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { fadeIn } from '@/lib/animation-variants'
import { MediaThumbnail } from '@/components/catalog/media/MediaThumbnail'
import { BaseCard } from './BaseCard'
import { usePrefetchOnHover } from '@/hooks/usePrefetch'
import { analytics } from '@/lib/analytics'
import type { Part } from '@/lib/types'
import { navigate } from 'astro:transitions/client'

interface PartCardProps {
  part: Part
  onSelectionChange?: (id: number, selected: boolean) => void
  isSelected?: boolean
  animationDelay?: number
  className?: string
}

export const PartCard = memo(
  ({
    part,
    onSelectionChange,
    isSelected,
    animationDelay = 0,
    className,
  }: PartCardProps) => {
    const handleCardClick = () => {
      analytics.partCardClicked(part.id, 'card')
      navigate(`/catalog/parts/${part.id}`)
    }

    const handleCheckboxClick = (e: React.MouseEvent) => {
      e.stopPropagation()
      onSelectionChange?.(part.id, !isSelected)
    }

    const handleMouseEnter = usePrefetchOnHover(part.id, 'part')

    return (
      <BaseCard
        variant="part"
        layout="detailed"
        onClick={handleCardClick}
        onMouseEnter={handleMouseEnter}
        animationDelay={animationDelay}
        className={className}
      >
        <BaseCard.Header>
          {onSelectionChange && (
            <Checkbox
              checked={isSelected}
              onClick={handleCheckboxClick}
              aria-label={`Select ${part.name}`}
              className="h-5 w-5"
            />
          )}
          <MediaThumbnail mediaAsset={part.image} size="md" />
        </BaseCard.Header>
        <BaseCard.Content>
          <BaseCard.Title>{part.name}</BaseCard.Title>
          <BaseCard.Description>
            {part.partCategory?.name}
          </Base-Card.Description>
          <div className="flex items-center gap-2 mt-2">
            <Badge variant="secondary">{part.applicabilities.length} аналогов</Badge>
            <Badge variant="outline">{part.attributes.length} атрибутов</Badge>
          </div>
        </BaseCard.Content>
        <BaseCard.Footer>
          <motion.div variants={fadeIn}>
            <p className="text-xs text-muted-foreground">
              Обновлено: {new Date(part.updatedAt).toLocaleDateString()}
            </p>
          </motion.div>
        </BaseCard.Footer>
      </BaseCard>
    )
  }
)