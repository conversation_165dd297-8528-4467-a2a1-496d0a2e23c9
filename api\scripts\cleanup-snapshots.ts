import { getSystemDB } from '../db';
import { getSnapshotStoragePath } from '../lib/snapshot-utils';
import { promises as fs } from 'fs';
import path from 'path';

const RETENTION_DAYS = 30; // Keep snapshots for 30 days
const DRY_RUN = process.argv.includes('--dry-run'); // Run in dry-run mode

async function cleanupSnapshots() {
  console.log(`Starting snapshot cleanup (Dry Run: ${DRY_RUN})...`);
  const db = getSystemDB();
  const storagePath = await getSnapshotStoragePath();

  // 1. Cleanup based on retention policy
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - RETENTION_DAYS);

  const oldSnapshots = await db.databaseSnapshot.findMany({
    where: {
      createdAt: { lt: cutoffDate },
      status: { in: ['READY', 'FAILED'] },
    },
  });

  console.log(`Found ${oldSnapshots.length} snapshots older than ${RETENTION_DAYS} days.`);

  for (const snapshot of oldSnapshots) {
    console.log(`Processing old snapshot: ${snapshot.name} (ID: ${snapshot.id})`);
    if (snapshot.filePath) {
      const fullPath = path.join(storagePath, snapshot.filePath);
      try {
        if (DRY_RUN) {
          console.log(`[Dry Run] Would delete file: ${fullPath}`);
        } else {
          await fs.unlink(fullPath);
          console.log(`Deleted snapshot file: ${fullPath}`);
        }
      } catch (error: any) {
        if (error.code === 'ENOENT') {
            console.warn(`Snapshot file not found, but proceeding to delete DB record: ${fullPath}`);
        } else {
            console.error(`Error deleting snapshot file ${fullPath}:`, error);
            // Optionally, update status to FAILED and skip DB deletion
            continue;
        }
      }
    }

    if (DRY_RUN) {
      console.log(`[Dry Run] Would delete DB record for snapshot: ${snapshot.id}`);
    } else {
      await db.databaseSnapshot.delete({ where: { id: snapshot.id } });
      console.log(`Deleted DB record for snapshot: ${snapshot.id}`);
    }
  }

  // 2. Cleanup orphaned DB records (records without a file)
  const allSnapshots = await db.databaseSnapshot.findMany({
    where: { status: 'READY' },
  });

  console.log(`\nChecking for ${allSnapshots.length} READY snapshots for orphaned files...`);

  for (const snapshot of allSnapshots) {
    if (!snapshot.filePath) {
      console.warn(`Snapshot ${snapshot.id} is READY but has no filePath. Deleting record.`);
      if (!DRY_RUN) {
        await db.databaseSnapshot.delete({ where: { id: snapshot.id } });
      }
      continue;
    }

    const fullPath = path.join(storagePath, snapshot.filePath);
    try {
      await fs.access(fullPath);
    } catch {
      console.warn(`Orphaned DB record found: file ${fullPath} is missing for snapshot ${snapshot.id}.`);
      if (DRY_RUN) {
        console.log(`[Dry Run] Would delete orphaned DB record: ${snapshot.id}`);
      } else {
        await db.databaseSnapshot.delete({ where: { id: snapshot.id } });
        console.log(`Deleted orphaned DB record: ${snapshot.id}`);
      }
    }
  }

  console.log('\nSnapshot cleanup finished.');
}

cleanupSnapshots().catch((error) => {
  console.error('An error occurred during snapshot cleanup:', error);
  process.exit(1);
});