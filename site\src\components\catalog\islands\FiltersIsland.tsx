"use client"

import { ModernFiltersPanel } from "../pro/ModernFiltersPanel"
import { useCatalogSearch } from "../pro/useCatalogSearch"
import { useCatalogMetadata } from "@/hooks/useCatalogData"
import { FilterSystem } from "../filters/FilterSystem"
import { TrpcBoundary } from "@/components/providers/TrpcBoundary"

export default function FiltersIsland() {
  return (
    <TrpcBoundary>
      <FiltersIslandInner />
    </TrpcBoundary>
  )
}

function FiltersIslandInner() {
  const { filters, setFilters, updateFilters, clearFilters, metadata } = useCatalogSearch()
  const { categories, brands, templates } = useCatalogMetadata()

  return (
    <FilterSystem
      filters={filters}
      setFilters={setFilters}
      updateFilters={updateFilters}
      clearFilters={clearFilters}
      metadata={metadata}
      categories={categories}
      brands={brands}
      attributeTemplates={templates}
    >
      <ModernFiltersPanel />
    </FilterSystem>
  )
}
