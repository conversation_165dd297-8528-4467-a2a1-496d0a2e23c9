// This is a workaround for a bug in Astro's type generation
// TODO: remove this when the bug is fixed
import type { Part as PrismaPart, PartCategory, MediaAsset, PartAttribute, AttributeTemplate, PartApplicability, CatalogItem as PrismaCatalogItem, Brand, CatalogItemAttribute, AttributeSynonymGroup, AttributeSynonym, EquipmentModelAttribute } from "packages/shared-types/src/index";

export type CatalogItem = PrismaCatalogItem & {
  brand: Brand;
  image: MediaAsset | null;
  mediaAssets: MediaAsset[];
  attributes: FullCatalogItemAttribute[];
  description?: string;
};

type FullAttributeTemplate = AttributeTemplate & {
  synonymGroups: (AttributeSynonymGroup & { synonyms: AttributeSynonym[] })[]
};
export type FullPartAttribute = PartAttribute & { template: FullAttributeTemplate };
export type FullCatalogItemAttribute = CatalogItemAttribute & { template: FullAttributeTemplate };

type FullCatalogItem = CatalogItem & {
  // Already included in the new CatalogItem type
};

export type FullPartApplicability = PartApplicability & {
  catalogItem: FullCatalogItem;
};

export type FullEquipmentModel = {
  id: string
  name: string
  brand: Brand | null
  attributes: (EquipmentModelAttribute & { template: AttributeTemplate })[]
}

export type FullEquipmentApplicability = {
  id: number
  equipmentModelId: string
  notes: string | null
  equipmentModel: FullEquipmentModel
}

export type Part = PrismaPart & {
  partCategory: PartCategory | null;
  image: MediaAsset | null;
  mediaAssets: MediaAsset[];
  attributes: FullPartAttribute[];
  applicabilities: FullPartApplicability[];
  equipmentApplicabilities: FullEquipmentApplicability[]; // Применимость к технике
};
