---
import MainLayout from "../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/trpc";
import { TrpcProvider } from "@/components/providers/TrpcProvider";
import { SearchIsland } from "@/components/catalog/search";

// Получаем параметры поиска из URL
const searchParams = Astro.url.searchParams;
const searchQuery = searchParams.get('q') || '';

// Проверяем подписку пользователя (по умолчанию FREE)
const isPro = false; // TODO: Implement user subscription check

// Загружаем начальные результаты поиска для SSR
type SearchResults = {
  parts: any[]
  catalogItems: any[]
  categories: any[]
  brands: any[]
}

let searchResults: SearchResults = {
  parts: [],
  catalogItems: [],
  categories: [],
  brands: []
};

// SSR search для SEO и первой загрузки
// Полный поиск с ранжированием выполняется в SearchIsland (client-side)
// Бэкенд: PostgreSQL → MeiliSearch (будущее)
if (searchQuery.trim()) {
  try {
    // Простой поиск для SSR (первая страница)
    const [parts, categories, brands] = await Promise.all([
      // Parts (только для PRO)
      isPro ? trpcClient.crud.part.findMany.query({
        where: {
          OR: [
            { name: { contains: searchQuery, mode: 'insensitive' } },
            { partCategory: { name: { contains: searchQuery, mode: 'insensitive' } } },
          ],
        },
        include: { partCategory: true, image: true, attributes: { include: { template: true }, take: 3 } },
        take: 10,
      }) : Promise.resolve([]),
      
      // Categories
      trpcClient.crud.partCategory.findMany.query({
        where: { name: { contains: searchQuery, mode: 'insensitive' } },
        include: { image: true, _count: { select: { parts: true } } },
        take: 5,
      }),
      
      // Brands
      trpcClient.crud.brand.findMany.query({
        where: { name: { contains: searchQuery, mode: 'insensitive' } },
        include: { _count: { select: { catalogItems: true } } },
        take: 5,
      }),
    ])
    
    searchResults = { parts, categories, brands, catalogItems: [] }
  } catch (error) {
    console.error('Search error:', error)
  }
}
---

<MainLayout 
  title={searchQuery ? `Поиск: ${searchQuery}` : "Поиск по каталогу запчастей"}
  description="Расширенный поиск запчастей с поддержкой фильтров, поиска по изображению и технике"
>
  <TrpcProvider client:load>
    <SearchIsland
      client:load
      initialQuery={searchQuery}
      initialResults={searchResults}
      isPro={isPro}
    />
  </TrpcProvider>
</MainLayout>

