"use client"

import { type ColumnDef } from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { CheckCircle2, XCircle, <PERSON>clip, Crown } from "lucide-react"
import { cn } from "@/lib/utils"
import type { Part } from "@/lib/types"
import { MediaThumbnail } from "@/components/catalog/media/MediaThumbnail"
import { compareAttributeValues, getComparisonStatusColor, getComparisonStatusIcon, getComparisonStatusLabel } from "@/lib/comparators"

import { ModernButton } from "@/components/ui/modern-button"
import { analytics } from "@/lib/analytics"
import { navigate } from "astro:transitions/client"

type Applicability = Part["applicabilities"][0]
type CatalogItem = Applicability["catalogItem"]
type PartAttribute = Part["attributes"][0]



export const createColumns = (partAttributes: PartAttribute[], partId: number): ColumnDef<Applicability>[] => {
  const columns: ColumnDef<Applicability>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "image",
      header: "Фото",
      cell: ({ row }) => {
        const item = row.original.catalogItem;
        return (
          <MediaThumbnail
            mediaAsset={item.image || item.mediaAssets[0]}
            size="sm"
            className="cursor-pointer"
          />
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "catalogItem.sku",
      header: () => (
        <div className="flex items-center gap-2">
          <span>SKU / Бренд</span>
          <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0 text-xs">
            <Crown className="w-3 h-3" />
            PRO
          </Badge>
        </div>
      ),
      cell: ({ row }) => {
        const item = row.original.catalogItem;
        return (
          <div>
            <div className="font-bold font-mono">{item.sku}</div>
            <div className="text-xs text-muted-foreground flex items-center gap-1">
              {item.brand.name}
              {item.brand.isOem && (
                <Badge variant="secondary" className="text-[10px]">OEM</Badge>
              )}
            </div>
          </div>
        )
      },
    },
    ...partAttributes.slice(0, 5).map((partAttr): ColumnDef<Applicability> => ({
      id: `attr-${partAttr.templateId}`,
      header: () => (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className="cursor-help">{partAttr.template.title}</span>
            </TooltipTrigger>
            <TooltipContent>
              <p>Эталон: {partAttr.value} {partAttr.template.unit}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ),
      cell: ({ row }) => {
        const itemAttr = row.original.catalogItem.attributes.find((a: { templateId: number }) => a.templateId === partAttr.templateId);
        const comparison = compareAttributeValues(partAttr, itemAttr);
        const isMatch = comparison.status === 'exact' || comparison.status === 'near' || comparison.status === 'legacy';
        const colorClass = getComparisonStatusColor(comparison.status);
        const Icon = {
          CheckCircle2,
          XCircle,
          Check: CheckCircle2, // Or a different icon if you prefer
          CheckCircle: CheckCircle2,
          AlertCircle: XCircle,
          HelpCircle: XCircle,
        }[getComparisonStatusIcon(comparison.status)];


        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className={cn("flex items-center justify-center gap-2 p-2 rounded", colorClass)}>
                  <Icon className="h-4 w-4" />
                  <span className="font-medium">{itemAttr?.value || '-'}</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <div className="space-y-1">
                  <p className="font-semibold">{getComparisonStatusLabel(comparison.status)}</p>
                  <p className="text-xs text-muted-foreground">Эталон: {partAttr.value} {partAttr.template.unit}</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      },
    })),
    {
      accessorKey: "accuracy",
      header: "Точность",
      cell: ({ row }) => <Badge variant={row.original.accuracy === 'EXACT_MATCH' ? 'default' : 'secondary'}>{row.original.accuracy}</Badge>
    },
    {
      id: "media",
      header: "",
      cell: ({ row }) => {
        const item = row.original.catalogItem;
        if (item.mediaAssets.length === 0) return null;
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Paperclip className="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Доп. медиа: {item.mediaAssets.length}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      }
    },
    {
      id: "actions",
      header: "Действия",
      cell: ({ row }) => {
        const item = row.original.catalogItem;
        return (
          <ModernButton
            variant="outline"
            size="sm"
            onClick={() => {
              analytics.catalogItemDetailsClicked(item.id, partId)
              navigate(`/catalog/items/${item.id}`)
            }}
          >
            Подробнее
          </ModernButton>
        );
      },
      enableSorting: false,
    }
  ];

  return columns;
};
