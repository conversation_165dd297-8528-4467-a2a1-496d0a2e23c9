/**
 * Утилиты для отображения relevance score из бэкенда
 * 
 * Вся логика ранжирования выполняется на бэкенде:
 * - Сейчас: PostgreSQL full-text search с ts_rank
 * - Будущее: MeiliSearch для продвинутого ранжирования
 * 
 * Этот файл содержит только UI helpers для отображения scores
 */

/**
 * Возвращает Tailwind классы для цветовой индикации score
 * @param score - Relevance score (0-100)
 */
export function getRelevanceScoreColor(score: number): string {
  if (score >= 90) return 'text-green-500'
  if (score >= 70) return 'text-blue-500'
  if (score >= 50) return 'text-yellow-500'
  return 'text-gray-400'
}

/**
 * Возвращает русскую метку для relevance score
 * @param score - Relevance score (0-100)
 */
export function getRelevanceScoreLabel(score: number): string {
  if (score >= 90) return 'Отлично'
  if (score >= 70) return 'Хорошо'
  if (score >= 50) return 'Удовлетворительно'
  return 'Низкая'
}

/**
 * Форматирует score для отображения
 * @param score - Relevance score (0-100)
 */
export function formatRelevanceScore(score: number): string {
  return `${Math.round(score)}%`
}

/**
 * Сортирует массив объектов по полю relevanceScore (по убыванию)
 * @param items - Массив объектов с опциональным полем relevanceScore
 */
export function sortByRelevance<T extends { relevanceScore?: number | null }>(items: T[]): T[] {
  return [...items].sort((a, b) => (b.relevanceScore ?? 0) - (a.relevanceScore ?? 0))
}