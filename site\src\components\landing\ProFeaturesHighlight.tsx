"use client"

import { motion } from "motion/react"
import { Crown, Database, GitCompare, ShoppingCart, Sparkles, FileDown, Search, TrendingUp } from "lucide-react"
import { ModernCard, ModernCardHeader, ModernCardTitle, ModernCardDescription, ModernCardContent } from "@/components/ui/modern-card"
import { Button } from "@/components/ui/button"
import { trackEvent } from "@/lib/analytics"

const proFeatures = [
  {
    icon: Database,
    title: "Доступ к эталонным группам Part",
    description: "Просматривайте нормализованные, проверенные инженерами данные о деталях. Все размеры в мм, все материалы по ГОСТ.",
    example: "Сальник 30x40x5 → 47 аналогов от 12 брендов",
    color: "from-blue-500 to-cyan-500"
  },
  {
    icon: Git<PERSON>ompar<PERSON>,
    title: "Сравнение аналогов с точностью",
    description: "Детальное сравнение характеристик с эталоном. Система показывает отклонения с учетом допусков.",
    example: "Ø30.01 мм ✓ (эталон: 30.0 ±0.05)",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: ShoppingCart,
    title: "Информация о поставщиках и ценах",
    description: "Актуальные цены и наличие у поставщиков. Сравнивайте предложения и экономьте до 45%.",
    example: "Магазин А: 450₽ (в наличии) vs Магазин Б: 420₽",
    color: "from-amber-500 to-orange-500"
  },
  {
    icon: Sparkles,
    title: "AI-ассистент для умного поиска",
    description: "Поиск на естественном языке. Опишите деталь или технику — AI найдет нужные аналоги.",
    example: "\"Сальник для гидроцилиндра CAT 320D\" → 15 результатов",
    color: "from-purple-500 to-pink-500"
  },
  {
    icon: FileDown,
    title: "Экспорт данных в Excel/CSV",
    description: "Выгружайте результаты поиска и таблицы сравнения для работы в офлайн или интеграции с ERP.",
    example: "Экспорт 150 аналогов с характеристиками за 2 секунды",
    color: "from-teal-500 to-cyan-500"
  },
  {
    icon: Search,
    title: "Расширенный поиск по параметрам",
    description: "Фильтрация по десяткам технических характеристик. Диапазоны значений, материалы, применимость к технике.",
    example: "Ø внутр: 28-32мм, Материал: NBR, Техника: CAT",
    color: "from-indigo-500 to-blue-500"
  }
]

export function ProFeaturesHighlight() {
  return (
    <div className="container mx-auto px-4 py-16">
      {/* Заголовок секции */}
      <div className="text-center mb-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="inline-flex items-center gap-2 bg-gradient-to-r from-amber-500 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-medium mb-4"
        >
          <Crown className="w-4 h-4" />
          PRO Подписка
        </motion.div>
        
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.1 }}
          viewport={{ once: true }}
          className="text-5xl lg:text-5xl font-bold mb-4"
        >
          Что вы получаете с <span className="text-primary">PRO подпиской</span>
        </motion.h2>
        
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-xl text-muted-foreground max-w-3xl mx-auto"
        >
          Полный доступ к базе взаимозаменяемости, расширенным инструментам поиска и аналитики
        </motion.p>
      </div>

      {/* Сетка функций */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
        {proFeatures.map((feature, index) => (
          <motion.div
            key={feature.title}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            viewport={{ once: true }}
          >
            <ModernCard variant="elevated" className="h-full hover:shadow-strong transition-all duration-300 group">
              <ModernCardHeader>
                <div className="flex items-start gap-4">
                  <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${feature.color} flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <ModernCardTitle className="text-lg mb-2">{feature.title}</ModernCardTitle>
                  </div>
                </div>
              </ModernCardHeader>
              <ModernCardContent>
                <ModernCardDescription className="mb-4">
                  {feature.description}
                </ModernCardDescription>
                <div className="bg-muted/50 rounded-lg p-3 border border-border">
                  <div className="flex items-start gap-2">
                    <TrendingUp className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    <p className="text-xs text-muted-foreground">
                      <span className="font-semibold text-foreground">Пример:</span> {feature.example}
                    </p>
                  </div>
                </div>
              </ModernCardContent>
            </ModernCard>
          </motion.div>
        ))}
      </div>

      {/* CTA */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.6 }}
        viewport={{ once: true }}
        className="text-center mt-12"
      >
        <Button 
          size="lg" 
          className="gap-2"
          onClick={() => {
            trackEvent('cta_clicked', {
              source: 'pro_features',
              destination: '/register?trial=pro&source=pro_features',
              timestamp: new Date().toISOString()
            })
            window.location.href = '/register?trial=pro&source=pro_features'
          }}
        >
          <Crown className="w-5 h-5" />
          Попробовать PRO бесплатно
        </Button>
      </motion.div>
    </div>
  )
}
