"use client"

import { useState } from "react"
import { ChevronDown, ChevronUp, X, Filter, Sliders, Search } from "lucide-react"
import { ModernButton } from "@/components/ui/modern-button"
import { ModernCard, ModernCardContent, ModernCardHeader, ModernCardTitle } from "@/components/ui/modern-card"
import { ModernInput } from "@/components/ui/modern-input"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { AttributeFilter } from "./parts/AttributeFilter"
import { useFilterSystem } from "../filters/FilterSystem"
import FilterPresets from "../filters/FilterPresets"
import QuickFilters from "../filters/QuickFilters"
import { useIsMobile } from '@/hooks/useMediaQuery'

export function ModernFiltersPanel() {
  // Используем FilterSystem context
  const {
    filters,
    setFilters,
    activeFiltersCount,
    clearFilters,
    categories,
    brands,
    attributeTemplates,
    metadata,
  } = useFilterSystem()

  const isMobile = useIsMobile()

  // Don't render on mobile - MobileFiltersDrawer handles it
  if (isMobile) return null

  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [showQuickFilters, setShowQuickFilters] = useState(true)
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({ 
    categories: true, 
    brands: true 
  })
  const [categorySearch, setCategorySearch] = useState("")
  const [brandSearch, setBrandSearch] = useState("")

  const handleMultiSelect = (
    value: string | number,
    selected: (string | number)[],
    setter: (values: (string | number)[]) => void,
  ) => {
    if (selected.includes(value)) setter(selected.filter((v) => v !== value))
    else setter([...selected, value])
  }

  const toggleSection = (section: string) => 
    setExpandedSections((prev) => ({ ...prev, [section]: !prev[section] }))

  const filteredCategories = categories.filter((category) => 
    category.name.toLowerCase().includes(categorySearch.toLowerCase())
  )
  const filteredBrands = brands.filter((brand) => 
    brand.name.toLowerCase().includes(brandSearch.toLowerCase())
  )

  return (
    <div className="w-72 border-r border-border-strong bg-surface/50">
      <div className="sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto">
        <div className="p-4 space-y-4">
          {/* Header with filter count and clear button */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="relative">
                <Filter className="h-4 w-4 text-primary" />
                {activeFiltersCount > 0 && (
                  <div className="absolute -top-1 -right-1 h-3 w-3 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold">
                    {activeFiltersCount}
                  </div>
                )}
              </div>
              <div>
                <h2 className="font-semibold text-sm">Фильтры</h2>
                {activeFiltersCount > 0 && (
                  <p className="text-xs text-muted-foreground">
                    {activeFiltersCount} активных
                  </p>
                )}
              </div>
            </div>
            {activeFiltersCount > 0 && (
              <ModernButton 
                variant="outline" 
                size="sm" 
                onClick={clearFilters} 
                className="h-7 px-2 text-xs gap-1 hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30"
              >
                <X className="h-3 w-3" />
                Очистить
              </ModernButton>
            )}
          </div>

          {/* Filter Presets */}
          <FilterPresets variant="dropdown" />

          {/* Active filters summary */}
          {activeFiltersCount > 0 && (
            <ModernCard variant="glass" className="border-primary/20 bg-primary/5">
              <ModernCardContent className="p-3">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-xs font-medium">Активные фильтры</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {filters.categoryIds.length > 0 && (
                    <Badge variant="secondary" className="text-xs h-5">
                      Категории: {filters.categoryIds.length}
                    </Badge>
                  )}
                  {filters.brandIds.length > 0 && (
                    <Badge variant="secondary" className="text-xs h-5">
                      Бренды: {filters.brandIds.length}
                    </Badge>
                  )}
                  {filters.attributeFilters && Object.keys(filters.attributeFilters).length > 0 && (
                    <Badge variant="secondary" className="text-xs h-5">
                      Параметры: {Object.keys(filters.attributeFilters).length}
                    </Badge>
                  )}
                  {filters.isOemOnly && (
                    <Badge variant="secondary" className="text-xs h-5">
                      Только OEM
                    </Badge>
                  )}
                </div>
              </ModernCardContent>
            </ModernCard>
          )}

          {/* Quick Filters */}
          <ModernCard variant="default">
            <Collapsible open={showQuickFilters} onOpenChange={setShowQuickFilters}>
              <CollapsibleTrigger asChild>
                <ModernCardHeader className="cursor-pointer p-3 hover:bg-accent/5 transition-colors">
                  <div className="flex items-center justify-between">
                    <ModernCardTitle className="text-sm">Быстрые фильтры</ModernCardTitle>
                    {showQuickFilters ? (
                      <ChevronUp className="h-3 w-3 text-muted-foreground" />
                    ) : (
                      <ChevronDown className="h-3 w-3 text-muted-foreground" />
                    )}
                  </div>
                </ModernCardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <ModernCardContent className="p-3 pt-0">
                  <QuickFilters layout="vertical" maxVisible={5} />
                </ModernCardContent>
              </CollapsibleContent>
            </Collapsible>
          </ModernCard>

          {/* Categories */}
          <ModernCard variant="default">
            <Collapsible open={expandedSections.categories} onOpenChange={() => toggleSection("categories")}>
              <CollapsibleTrigger asChild>
                <ModernCardHeader className="cursor-pointer p-3 hover:bg-accent/5 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <ModernCardTitle className="text-sm">Категории</ModernCardTitle>
                      {filters.categoryIds.length > 0 && (
                        <Badge variant="default" className="h-4 w-4 rounded-full p-0 text-xs">
                          {filters.categoryIds.length}
                        </Badge>
                      )}
                    </div>
                    {expandedSections.categories ? (
                      <ChevronUp className="h-3 w-3 text-muted-foreground" />
                    ) : (
                      <ChevronDown className="h-3 w-3 text-muted-foreground" />
                    )}
                  </div>
                </ModernCardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <ModernCardContent className="p-3 pt-0 space-y-2">
                  {categories.length > 5 && (
                    <div className="relative">
                      <Search className="absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground" />
                      <ModernInput 
                        placeholder="Поиск категорий..." 
                        value={categorySearch} 
                        onChange={(e) => setCategorySearch(e.target.value)} 
                        className="pl-7 h-7 text-xs" 
                        variant="ghost" 
                      />
                    </div>
                  )}

                  {filteredCategories.map((category) => (
                    <div key={category.id} className="flex items-center space-x-2 group">
                      <Checkbox 
                        id={`category-${category.id}`} 
                        checked={filters.categoryIds.includes(category.id)} 
                        onCheckedChange={() => 
                          handleMultiSelect(
                            category.id, 
                            filters.categoryIds, 
                            (values) => setFilters({ ...filters, categoryIds: values as number[] })
                          )
                        } 
                        className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3" 
                      />
                      <Label 
                        htmlFor={`category-${category.id}`} 
                        className="text-xs cursor-pointer flex-1 group-hover:text-primary transition-colors"
                      >
                        {category.name}
                      </Label>
                    </div>
                  ))}
                </ModernCardContent>
              </CollapsibleContent>
            </Collapsible>
          </ModernCard>

          {/* Brands */}
          <ModernCard variant="default">
            <Collapsible open={expandedSections.brands} onOpenChange={() => toggleSection("brands")}>
              <CollapsibleTrigger asChild>
                <ModernCardHeader className="cursor-pointer p-3 hover:bg-accent/5 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <ModernCardTitle className="text-sm">Производители</ModernCardTitle>
                      {(filters.brandIds.length > 0 || filters.isOemOnly) && (
                        <Badge variant="default" className="h-4 w-4 rounded-full p-0 text-xs">
                          {filters.brandIds.length + (filters.isOemOnly ? 1 : 0)}
                        </Badge>
                      )}
                    </div>
                    {expandedSections.brands ? (
                      <ChevronUp className="h-3 w-3 text-muted-foreground" />
                    ) : (
                      <ChevronDown className="h-3 w-3 text-muted-foreground" />
                    )}
                  </div>
                </ModernCardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <ModernCardContent className="p-3 pt-0 space-y-2">
                  <div className="flex items-center space-x-2 p-2 rounded bg-accent/5 border border-border/30">
                    <Checkbox 
                      id="oem-only" 
                      checked={filters.isOemOnly} 
                      onCheckedChange={(checked) => setFilters({ ...filters, isOemOnly: !!checked })} 
                      className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3" 
                    />
                    <Label htmlFor="oem-only" className="text-xs cursor-pointer font-medium">
                      Только OEM производители
                    </Label>
                  </div>

                  <Separator className="bg-border/30" />

                  {brands.length > 5 && (
                    <div className="relative">
                      <Search className="absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground" />
                      <ModernInput 
                        placeholder="Поиск брендов..." 
                        value={brandSearch} 
                        onChange={(e) => setBrandSearch(e.target.value)} 
                        className="pl-7 h-7 text-xs" 
                        variant="ghost" 
                      />
                    </div>
                  )}

                  {filteredBrands.map((brand) => (
                    <div key={brand.id} className="flex items-center space-x-2 group">
                      <Checkbox 
                        id={`brand-${brand.id}`} 
                        checked={filters.brandIds.includes(brand.id)} 
                        onCheckedChange={() => 
                          handleMultiSelect(
                            brand.id, 
                            filters.brandIds, 
                            (values) => setFilters({ ...filters, brandIds: values as number[] })
                          )
                        } 
                        className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3" 
                      />
                      <Label 
                        htmlFor={`brand-${brand.id}`} 
                        className="text-xs cursor-pointer flex-1 group-hover:text-primary transition-colors flex items-center gap-1"
                      >
                        {brand.name}
                        {brand.isOem && (
                          <Badge variant="outline" className="text-xs h-4">
                            OEM
                          </Badge>
                        )}
                      </Label>
                    </div>
                  ))}
                </ModernCardContent>
              </CollapsibleContent>
            </Collapsible>
          </ModernCard>

          {/* Attribute Filters */}
          <ModernCard variant="default">
            <Collapsible open={showAdvancedFilters} onOpenChange={setShowAdvancedFilters}>
              <CollapsibleTrigger asChild>
                <ModernCardHeader className="cursor-pointer p-3 hover:bg-accent/5 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Sliders className="h-3 w-3 text-primary" />
                      <ModernCardTitle className="text-sm">Технические параметры</ModernCardTitle>
                      {filters.attributeFilters && Object.keys(filters.attributeFilters).length > 0 && (
                        <Badge variant="default" className="h-4 w-4 rounded-full p-0 text-xs">
                          {Object.keys(filters.attributeFilters).length}
                        </Badge>
                      )}
                    </div>
                    {showAdvancedFilters ? (
                      <ChevronUp className="h-3 w-3 text-muted-foreground" />
                    ) : (
                      <ChevronDown className="h-3 w-3 text-muted-foreground" />
                    )}
                  </div>
                </ModernCardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <ModernCardContent className="p-3 pt-0 space-y-3">
                  {attributeTemplates.map((template) => (
                    <AttributeFilter
                      key={template.id}
                      template={template}
                      selectedValues={filters.attributeFilters?.[template.id]?.values || []}
                      numericRange={filters.attributeFilters?.[template.id]?.numericRange}
                      onValuesChange={(values) => {
                        setFilters({ 
                          ...filters, 
                          attributeFilters: { 
                            ...(filters.attributeFilters || {}), 
                            [template.id]: { 
                              ...(filters.attributeFilters?.[template.id] || {}), 
                              values, 
                            }, 
                          }, 
                        })
                      }}
                      onRangeChange={(range) => {
                        setFilters({ 
                          ...filters, 
                          attributeFilters: { 
                            ...(filters.attributeFilters || {}), 
                            [template.id]: { 
                              ...(filters.attributeFilters?.[template.id] || {}), 
                              numericRange: range, 
                            }, 
                          }, 
                        })
                      }}
                      availableValues={metadata.availableValues[template.id] || []}
                      numericStats={metadata.numericRanges[template.id] 
                        ? {
                            min: metadata.numericRanges[template.id].min,
                            max: metadata.numericRanges[template.id].max,
                            avg: (metadata.numericRanges[template.id].min + metadata.numericRanges[template.id].max) / 2,
                          }
                        : undefined
                      }
                    />
                  ))}
                </ModernCardContent>
              </CollapsibleContent>
            </Collapsible>
          </ModernCard>
        </div>
      </div>
    </div>
  )
}
