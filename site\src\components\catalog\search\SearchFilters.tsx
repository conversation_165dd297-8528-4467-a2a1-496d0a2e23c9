import { useState, useEffect, useMemo } from 'react'
import { motion } from 'framer-motion'
import { X, Search as SearchIcon, RotateCcw } from 'lucide-react'
import type { Part, CatalogItem, PartCategory, Brand } from '@/types/catalog'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Checkbox } from '@/components/ui/checkbox'
import { cn } from '@/lib/utils'
import { fadeInUp } from '@/lib/animation-variants'

export interface SearchFilters {
  resultTypes: Array<'parts' | 'categories' | 'brands'>
  categoryIds: number[]
  brandIds: number[]
  isOemOnly: boolean
  dateRange?: { from: Date; to: Date }
}

export interface SearchFiltersProps {
  results: {
    parts: Part[]
    catalogItems: CatalogItem[]
    categories: PartCategory[]
    brands: Brand[]
  }
  onFilterChange: (filters: SearchFilters) => void
  className?: string
}

const FILTERS_KEY = 'search-filters'

export default function SearchFiltersComponent({
  results,
  onFilterChange,
  className
}: SearchFiltersProps) {
  const [filters, setFilters] = useState<SearchFilters>(() => {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(FILTERS_KEY)
        if (stored) {
          const parsed = JSON.parse(stored)
          return {
            ...parsed,
            dateRange: parsed.dateRange ? {
              from: new Date(parsed.dateRange.from),
              to: new Date(parsed.dateRange.to)
            } : undefined
          }
        }
      } catch {
        // Ignore errors
      }
    }
    return {
      resultTypes: ['parts', 'categories', 'brands'],
      categoryIds: [],
      brandIds: [],
      isOemOnly: false
    }
  })

  const [categorySearch, setCategorySearch] = useState('')
  const [brandSearch, setBrandSearch] = useState('')

  // Extract unique categories from results
  const uniqueCategories = useMemo(() => {
    const categories = new Map<number, PartCategory & { count: number }>()
    
    results.parts.forEach(part => {
      if (part.category) {
        const existing = categories.get(part.category.id)
        categories.set(part.category.id, {
          ...part.category,
          count: existing ? existing.count + 1 : 1
        })
      }
    })

    results.catalogItems.forEach(item => {
      if (item.category) {
        const existing = categories.get(item.category.id)
        categories.set(item.category.id, {
          ...item.category,
          count: existing ? existing.count + 1 : 1
        })
      }
    })

    return Array.from(categories.values()).sort((a, b) => b.count - a.count)
  }, [results])

  // Extract unique brands from results
  const uniqueBrands = useMemo(() => {
    const brands = new Map<number, Brand & { count: number }>()
    
    results.parts.forEach(part => {
      if (part.brand) {
        const existing = brands.get(part.brand.id)
        brands.set(part.brand.id, {
          ...part.brand,
          count: existing ? existing.count + 1 : 1
        })
      }
    })

    results.catalogItems.forEach(item => {
      if (item.brand) {
        const existing = brands.get(item.brand.id)
        brands.set(item.brand.id, {
          ...item.brand,
          count: existing ? existing.count + 1 : 1
        })
      }
    })

    return Array.from(brands.values()).sort((a, b) => b.count - a.count)
  }, [results])

  // Filter categories by search
  const filteredCategories = useMemo(() => {
    if (!categorySearch) return uniqueCategories
    return uniqueCategories.filter(cat =>
      cat.name.toLowerCase().includes(categorySearch.toLowerCase())
    )
  }, [uniqueCategories, categorySearch])

  // Filter brands by search
  const filteredBrands = useMemo(() => {
    if (!brandSearch) return uniqueBrands
    return uniqueBrands.filter(brand =>
      brand.name.toLowerCase().includes(brandSearch.toLowerCase())
    )
  }, [uniqueBrands, brandSearch])

  // Count OEM brands
  const oemCount = useMemo(() => {
    return uniqueBrands.filter(b => b.isOEM).reduce((sum, b) => sum + b.count, 0)
  }, [uniqueBrands])

  const totalCount = useMemo(() => {
    return results.parts.length + results.catalogItems.length
  }, [results])

  // Update filters
  const updateFilters = (updates: Partial<SearchFilters>) => {
    const newFilters = { ...filters, ...updates }
    setFilters(newFilters)
    onFilterChange(newFilters)
    
    if (typeof window !== 'undefined') {
      localStorage.setItem(FILTERS_KEY, JSON.stringify(newFilters))
    }
  }

  // Toggle result type
  const toggleResultType = (type: 'parts' | 'categories' | 'brands') => {
    const newTypes = filters.resultTypes.includes(type)
      ? filters.resultTypes.filter(t => t !== type)
      : [...filters.resultTypes, type]
    
    updateFilters({ resultTypes: newTypes })
  }

  // Toggle category
  const toggleCategory = (categoryId: number) => {
    const newIds = filters.categoryIds.includes(categoryId)
      ? filters.categoryIds.filter(id => id !== categoryId)
      : [...filters.categoryIds, categoryId]
    
    updateFilters({ categoryIds: newIds })
  }

  // Toggle brand
  const toggleBrand = (brandId: number) => {
    const newIds = filters.brandIds.includes(brandId)
      ? filters.brandIds.filter(id => id !== brandId)
      : [...filters.brandIds, brandId]
    
    updateFilters({ brandIds: newIds })
  }

  // Reset all filters
  const resetFilters = () => {
    const defaultFilters: SearchFilters = {
      resultTypes: ['parts', 'categories', 'brands'],
      categoryIds: [],
      brandIds: [],
      isOemOnly: false
    }
    setFilters(defaultFilters)
    onFilterChange(defaultFilters)
    
    if (typeof window !== 'undefined') {
      localStorage.removeItem(FILTERS_KEY)
    }
  }

  const activeFilterCount = 
    (filters.resultTypes.length < 3 ? 1 : 0) +
    filters.categoryIds.length +
    filters.brandIds.length +
    (filters.isOemOnly ? 1 : 0)

  return (
    <motion.div
      variants={fadeInUp}
      initial="initial"
      animate="animate"
      className={cn('space-y-4 sticky top-20', className)}
    >
      {/* Header */}
      <div className="flex items-center justify-between pb-3 border-b">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          Фильтры
          {activeFilterCount > 0 && (
            <span className="px-2 py-0.5 text-xs bg-primary text-primary-foreground rounded-full">
              {activeFilterCount}
            </span>
          )}
        </h3>
        {activeFilterCount > 0 && (
          <button
            onClick={resetFilters}
            className="text-sm text-muted-foreground hover:text-foreground transition-colors flex items-center gap-1"
          >
            <RotateCcw className="w-3 h-3" />
            Сбросить
          </button>
        )}
      </div>

      {/* Result Type Filter */}
      <Collapsible defaultOpen>
        <CollapsibleTrigger className="w-full flex items-center justify-between py-2 font-medium hover:text-primary transition-colors">
          Типы результатов
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-2 pt-2">
          <label className="flex items-center gap-2 cursor-pointer">
            <Checkbox
              checked={filters.resultTypes.includes('parts')}
              onCheckedChange={() => toggleResultType('parts')}
            />
            <span className="text-sm">
              Запчасти <span className="text-muted-foreground">({results.parts.length})</span>
            </span>
          </label>
          <label className="flex items-center gap-2 cursor-pointer">
            <Checkbox
              checked={filters.resultTypes.includes('categories')}
              onCheckedChange={() => toggleResultType('categories')}
            />
            <span className="text-sm">
              Категории <span className="text-muted-foreground">({results.categories.length})</span>
            </span>
          </label>
          <label className="flex items-center gap-2 cursor-pointer">
            <Checkbox
              checked={filters.resultTypes.includes('brands')}
              onCheckedChange={() => toggleResultType('brands')}
            />
            <span className="text-sm">
              Бренды <span className="text-muted-foreground">({results.brands.length})</span>
            </span>
          </label>
        </CollapsibleContent>
      </Collapsible>

      {/* Category Filter */}
      {uniqueCategories.length > 0 && (
        <Collapsible defaultOpen>
          <CollapsibleTrigger className="w-full flex items-center justify-between py-2 font-medium hover:text-primary transition-colors">
            Категории
            {filters.categoryIds.length > 0 && (
              <span className="text-xs px-2 py-0.5 bg-primary/10 text-primary rounded-full">
                {filters.categoryIds.length}
              </span>
            )}
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-2 pt-2">
            {uniqueCategories.length > 5 && (
              <div className="relative">
                <SearchIcon className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <input
                  type="text"
                  value={categorySearch}
                  onChange={(e) => setCategorySearch(e.target.value)}
                  placeholder="Поиск..."
                  className="w-full pl-8 pr-3 py-1.5 text-sm border rounded-md"
                />
              </div>
            )}
            <div className="max-h-64 overflow-y-auto space-y-2">
              {filteredCategories.map(category => (
                <label key={category.id} className="flex items-center gap-2 cursor-pointer">
                  <Checkbox
                    checked={filters.categoryIds.includes(category.id)}
                    onCheckedChange={() => toggleCategory(category.id)}
                  />
                  <span className="text-sm flex-1 truncate">
                    {category.name}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {category.count}
                  </span>
                </label>
              ))}
            </div>
          </CollapsibleContent>
        </Collapsible>
      )}

      {/* Brand Filter */}
      {uniqueBrands.length > 0 && (
        <Collapsible defaultOpen>
          <CollapsibleTrigger className="w-full flex items-center justify-between py-2 font-medium hover:text-primary transition-colors">
            Бренды
            {filters.brandIds.length > 0 && (
              <span className="text-xs px-2 py-0.5 bg-primary/10 text-primary rounded-full">
                {filters.brandIds.length}
              </span>
            )}
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-2 pt-2">
            {uniqueBrands.length > 5 && (
              <div className="relative">
                <SearchIcon className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <input
                  type="text"
                  value={brandSearch}
                  onChange={(e) => setBrandSearch(e.target.value)}
                  placeholder="Поиск..."
                  className="w-full pl-8 pr-3 py-1.5 text-sm border rounded-md"
                />
              </div>
            )}
            <div className="max-h-64 overflow-y-auto space-y-2">
              {filteredBrands.map(brand => (
                <label key={brand.id} className="flex items-center gap-2 cursor-pointer">
                  <Checkbox
                    checked={filters.brandIds.includes(brand.id)}
                    onCheckedChange={() => toggleBrand(brand.id)}
                  />
                  <span className="text-sm flex-1 truncate">
                    {brand.name}
                    {brand.isOEM && (
                      <span className="ml-1 px-1 py-0.5 text-xs bg-amber-500/10 text-amber-700 dark:text-amber-300 rounded">
                        OEM
                      </span>
                    )}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {brand.count}
                  </span>
                </label>
              ))}
            </div>
          </CollapsibleContent>
        </Collapsible>
      )}

      {/* OEM Only Filter */}
      {oemCount > 0 && (
        <div className="pt-2 border-t">
          <label className="flex items-center gap-2 cursor-pointer">
            <Checkbox
              checked={filters.isOemOnly}
              onCheckedChange={(checked) => updateFilters({ isOemOnly: Boolean(checked) })}
            />
            <span className="text-sm">
              Только OEM запчасти
              <span className="text-muted-foreground ml-1">
                ({oemCount} из {totalCount})
              </span>
            </span>
          </label>
        </div>
      )}

      {/* Active Filters Summary */}
      {activeFilterCount > 0 && (
        <div className="pt-3 border-t space-y-2">
          <p className="text-xs font-medium text-muted-foreground">Применённые фильтры:</p>
          <div className="flex flex-wrap gap-1">
            {filters.categoryIds.map(id => {
              const category = uniqueCategories.find(c => c.id === id)
              return category ? (
                <button
                  key={`cat-${id}`}
                  onClick={() => toggleCategory(id)}
                  className="px-2 py-1 text-xs bg-primary/10 text-primary rounded-md hover:bg-primary/20 transition-colors flex items-center gap-1"
                >
                  {category.name}
                  <X className="w-3 h-3" />
                </button>
              ) : null
            })}
            {filters.brandIds.map(id => {
              const brand = uniqueBrands.find(b => b.id === id)
              return brand ? (
                <button
                  key={`brand-${id}`}
                  onClick={() => toggleBrand(id)}
                  className="px-2 py-1 text-xs bg-primary/10 text-primary rounded-md hover:bg-primary/20 transition-colors flex items-center gap-1"
                >
                  {brand.name}
                  <X className="w-3 h-3" />
                </button>
              ) : null
            })}
            {filters.isOemOnly && (
              <button
                onClick={() => updateFilters({ isOemOnly: false })}
                className="px-2 py-1 text-xs bg-primary/10 text-primary rounded-md hover:bg-primary/20 transition-colors flex items-center gap-1"
              >
                OEM
                <X className="w-3 h-3" />
              </button>
            )}
          </div>
        </div>
      )}
    </motion.div>
  )
}

