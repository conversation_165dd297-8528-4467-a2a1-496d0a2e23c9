<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import VAutoComplete from '@/volt/AutoComplete.vue'
import { useTrpc } from '@/composables/useTrpc'

interface Props {
  modelValue: number | null
  placeholder?: string
  class?: string
}

interface Emits {
  (e: 'update:modelValue', value: number | null): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Поиск категории...',
  class: 'w-full'
})

const emit = defineEmits<Emits>()

// tRPC клиент
const { partCategories } = useTrpc()

// Состояние
const categorySuggestions = ref<Array<{ id: number; name: string }>>([])
const selectedCategory = ref<{ id: number; name: string } | null>(null)

// Синхронизация с v-model
watch(() => props.modelValue, async (newValue) => {
  if (newValue === null) {
    selectedCategory.value = null
  } else if (typeof newValue === 'number') {
    // Если у нас есть ID, найдем соответствующий объект в suggestions
    let category = categorySuggestions.value.find(cat => cat.id === newValue)

    // Если не нашли в текущих suggestions, попробуем загрузить по ID
    if (!category) {
      try {
        const result = await partCategories.findMany({
          where: { id: newValue }
        })
        if (result && result.length > 0) {
          category = result[0] as { id: number; name: string }
          // Добавим в suggestions для корректного отображения
          categorySuggestions.value = [...categorySuggestions.value, category]
        }
      } catch (err) {
        console.error('Ошибка загрузки категории по ID:', err)
      }
    }

    selectedCategory.value = category || null
  }
}, { immediate: true })

// Обработчики событий
const onCategorySelect = (event: any) => {
  const category = event.value
  if (category && typeof category.id === 'number') {
    emit('update:modelValue', category.id)
  }
}

const onCategoryClear = () => {
  emit('update:modelValue', null)
}

// Поиск категорий (серверный)
const searchCategories = async (event: any) => {
  const query = event.query.toLowerCase()
  try {
    const categories = await partCategories.findMany({
      where: {
        name: {
          contains: query,
          mode: "insensitive",
        },
      },
      take: 10,
    })

    if (categories && Array.isArray(categories)) {
      categorySuggestions.value = categories as Array<{ id: number; name: string }>
    }
  } catch (err) {
    console.error('Ошибка поиска категорий:', err)
  }
}
</script>

<template>
  <VAutoComplete
    v-model="selectedCategory"
    :suggestions="categorySuggestions"
    @complete="searchCategories"
    @item-select="onCategorySelect"
    @clear="onCategoryClear"
    option-label="name"
    :placeholder="placeholder"
    :class="class"
    dropdown
    show-clear
    force-selection
  />
</template>
