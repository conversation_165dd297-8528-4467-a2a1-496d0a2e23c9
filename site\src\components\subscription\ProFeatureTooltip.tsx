"use client"

import { type ReactNode } from "react"
import { Crown, Lock, Info } from "lucide-react"
import { Too<PERSON><PERSON>, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { useFeatureAccess, Feature } from "@/hooks/useFeatureAccess"
import { cn } from "@/lib/utils"

export interface ProFeatureTooltipProps {
  children: ReactNode
  feature?: Feature
  title: string
  description: string
  proDescription?: string
  showBadge?: boolean
  side?: "top" | "right" | "bottom" | "left"
  className?: string
  disabled?: boolean
}

export function ProFeatureTooltip({
  children,
  feature,
  title,
  description,
  proDescription,
  showBadge = true,
  side = "top",
  className,
  disabled = false
}: ProFeatureTooltipProps) {
  const { isPro, hasAccess } = useFeatureAccess()
  
  // Проверяем доступ к конкретной feature или общий Pro статус
  const hasFeatureAccess = feature ? hasAccess(feature) : isPro
  
  if (disabled) {
    return <>{children}</>
  }
  
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className={cn("inline-flex items-center gap-2", className)}>
          {children}
          {showBadge && !hasFeatureAccess && (
            <Badge 
              variant="secondary" 
              className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0 text-xs"
            >
              <Crown className="w-3 h-3" />
              PRO
            </Badge>
          )}
        </div>
      </TooltipTrigger>
      
      <TooltipContent side={side} className="max-w-xs">
        <div className="space-y-2">
          {/* Заголовок */}
          <div className="flex items-center gap-2">
            {hasFeatureAccess ? (
              <Info className="w-4 h-4 text-primary" />
            ) : (
              <Lock className="w-4 h-4 text-amber-500" />
            )}
            <span className="font-semibold">{title}</span>
          </div>
          
          {/* Описание */}
          <p className="text-sm">
            {hasFeatureAccess ? (
              proDescription || description
            ) : (
              description
            )}
          </p>
          
          {/* Сообщение о необходимости Pro */}
          {!hasFeatureAccess && (
            <div className="pt-2 border-t border-primary/20">
              <p className="text-xs text-amber-200">
                <Crown className="w-3 h-3 inline mr-1" />
                Доступно в PRO подписке
              </p>
            </div>
          )}
        </div>
      </TooltipContent>
    </Tooltip>
  )
}

export function PartViewingTooltip({ children }: { children: ReactNode }) {
  return (
    <ProFeatureTooltip
      feature={Feature.PART_VIEWING}
      title="Просмотр эталонов"
      description="Доступ к нормализованным данным Part и группам взаимозаменяемости"
      proDescription="Вы можете просматривать эталоны и все связанные аналоги"
    >
      {children}
    </ProFeatureTooltip>
  )
}

export function AIAssistantTooltip({ children }: { children: ReactNode }) {
  return (
    <ProFeatureTooltip
      feature={Feature.AI_ASSISTANT}
      title="AI-ассистент"
      description="Умный поиск запчастей на естественном языке с использованием искусственного интеллекта"
      proDescription="AI-ассистент активен. Задавайте вопросы на естественном языке"
    >
      {children}
    </ProFeatureTooltip>
  )
}

export function DataExportTooltip({ children }: { children: ReactNode }) {
  return (
    <ProFeatureTooltip
      feature={Feature.DATA_EXPORT}
      title="Экспорт данных"
      description="Выгрузка результатов поиска в Excel или CSV формате"
      proDescription="Экспортируйте данные в удобном формате"
    >
      {children}
    </ProFeatureTooltip>
  )
}

export function ApplicabilityTooltip({ children }: { children: ReactNode }) {
  return (
    <ProFeatureTooltip
      feature={Feature.PART_APPLICABILITY}
      title="Взаимозаменяемость"
      description="Полный список аналогов с точностью совпадения и сравнением характеристик"
      proDescription="Просматривайте все аналоги с детальным сравнением"
    >
      {children}
    </ProFeatureTooltip>
  )
}