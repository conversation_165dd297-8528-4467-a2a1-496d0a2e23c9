import { useMemo, useState, useEffect } from 'react'
import { Search, Layers, Building2, Target, TrendingUp, Clock, ChevronDown, ChevronUp } from 'lucide-react'
import { motion } from 'framer-motion'
import type { Part, CatalogItem, PartCategory, Brand } from '@/types/catalog'
import { ModernCard } from '@/components/ui/modern-card'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { cn } from '@/lib/utils'
import { staggerContainer, fadeInUp } from '@/lib/animation-variants'

export interface SearchInsightsProps {
  query: string
  results: {
    parts: Array<Part & { relevanceScore: number }>
    catalogItems: Array<CatalogItem & { relevanceScore: number }>
    categories: PartCategory[]
    brands: Brand[]
  }
  totalResults: number
  searchDuration?: number
  className?: string
}

const COLLAPSED_STATE_KEY = 'search-insights-collapsed'

export default function SearchInsights({
  query,
  results,
  totalResults,
  searchDuration = 0,
  className
}: SearchInsightsProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)

  // Load collapsed state from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(COLLAPSED_STATE_KEY)
      if (stored === 'true') {
        setIsCollapsed(true)
      }
    }
  }, [])

  // Save collapsed state to localStorage
  const handleToggle = () => {
    const newState = !isCollapsed
    setIsCollapsed(newState)
    if (typeof window !== 'undefined') {
      localStorage.setItem(COLLAPSED_STATE_KEY, String(newState))
    }
  }

  // Calculate insights
  const insights = useMemo(() => {
    const { parts, catalogItems, categories, brands } = results

    // Unique categories
    const uniqueCategories = new Map<number, PartCategory>()
    parts.forEach(part => {
      if (part.category) {
        uniqueCategories.set(part.category.id, part.category)
      }
    })
    catalogItems.forEach(item => {
      if (item.category) {
        uniqueCategories.set(item.category.id, item.category)
      }
    })

    const topCategories = Array.from(uniqueCategories.values())
      .slice(0, 3)

    // Unique brands
    const uniqueBrands = new Map<number, Brand>()
    parts.forEach(part => {
      if (part.brand) {
        uniqueBrands.set(part.brand.id, part.brand)
      }
    })
    catalogItems.forEach(item => {
      if (item.brand) {
        uniqueBrands.set(item.brand.id, item.brand)
      }
    })

    const topBrands = Array.from(uniqueBrands.values())
      .slice(0, 3)

    const oemBrands = topBrands.filter(brand => brand.isOEM)

    // Most relevant result
    const allResults = [...parts, ...catalogItems].sort((a, b) => b.relevanceScore - a.relevanceScore)
    const topResult = allResults[0]

    // Average relevance score
    const avgScore = allResults.length > 0
      ? allResults.reduce((sum, item) => sum + item.relevanceScore, 0) / allResults.length
      : 0

    // Query analysis
    const queryWords = query.trim().split(/\s+/)
    const isSKUPattern = /^[A-Z0-9-]+$/i.test(query)
    const detectedLanguage = /[а-яё]/i.test(query) ? 'Русский' : 'Английский'

    return {
      totalResults,
      partsCount: parts.length,
      catalogItemsCount: catalogItems.length,
      categoriesCount: categories.length,
      brandsCount: brands.length,
      uniqueCategoriesCount: uniqueCategories.size,
      uniqueBrandsCount: uniqueBrands.size,
      topCategories,
      topBrands,
      oemBrands,
      topResult,
      avgScore,
      searchDuration,
      queryLength: query.length,
      queryWords: queryWords.length,
      detectedLanguage,
      isSKUPattern
    }
  }, [results, totalResults, query, searchDuration])

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms.toFixed(0)} мс`
    return `${(ms / 1000).toFixed(2)} сек`
  }

  if (totalResults === 0) {
    return null
  }

  return (
    <div className={cn('w-full', className)}>
      <Collapsible open={!isCollapsed} onOpenChange={handleToggle}>
        <CollapsibleTrigger className="w-full">
          <div className="flex items-center justify-between py-3 px-4 bg-muted/50 hover:bg-muted rounded-lg transition-colors">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Аналитика поиска
            </h3>
            {isCollapsed ? (
              <ChevronDown className="w-5 h-5 text-muted-foreground" />
            ) : (
              <ChevronUp className="w-5 h-5 text-muted-foreground" />
            )}
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <motion.div
            variants={staggerContainer}
            initial="initial"
            animate="animate"
            className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4"
          >
            {/* Total Results */}
            <motion.div variants={fadeInUp}>
              <ModernCard className="h-full">
                <div className="flex items-start gap-3">
                  <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <Search className="w-5 h-5 text-primary" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-muted-foreground mb-1">Всего результатов</p>
                    <p className="text-2xl font-bold">{insights.totalResults}</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {insights.partsCount} запчастей, {insights.categoriesCount} категорий, {insights.brandsCount} брендов
                    </p>
                  </div>
                </div>
              </ModernCard>
            </motion.div>

            {/* Categories Found */}
            <motion.div variants={fadeInUp}>
              <ModernCard className="h-full">
                <div className="flex items-start gap-3">
                  <div className="w-10 h-10 rounded-lg bg-blue-500/10 flex items-center justify-center flex-shrink-0">
                    <Layers className="w-5 h-5 text-blue-500" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-muted-foreground mb-1">Найдено в категориях</p>
                    <p className="text-2xl font-bold">{insights.uniqueCategoriesCount}</p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {insights.topCategories.map(cat => (
                        <span
                          key={cat.id}
                          className="px-2 py-0.5 text-xs bg-blue-500/10 text-blue-700 dark:text-blue-300 rounded-full truncate"
                        >
                          {cat.name}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </ModernCard>
            </motion.div>

            {/* Brands Found */}
            <motion.div variants={fadeInUp}>
              <ModernCard className="h-full">
                <div className="flex items-start gap-3">
                  <div className="w-10 h-10 rounded-lg bg-green-500/10 flex items-center justify-center flex-shrink-0">
                    <Building2 className="w-5 h-5 text-green-500" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-muted-foreground mb-1">Найдено брендов</p>
                    <p className="text-2xl font-bold">{insights.uniqueBrandsCount}</p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {insights.topBrands.map(brand => (
                        <span
                          key={brand.id}
                          className={cn(
                            'px-2 py-0.5 text-xs rounded-full truncate',
                            brand.isOEM
                              ? 'bg-amber-500/10 text-amber-700 dark:text-amber-300'
                              : 'bg-green-500/10 text-green-700 dark:text-green-300'
                          )}
                        >
                          {brand.name}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </ModernCard>
            </motion.div>

            {/* Most Relevant */}
            {insights.topResult && (
              <motion.div variants={fadeInUp}>
                <ModernCard className="h-full">
                  <div className="flex items-start gap-3">
                    <div className="w-10 h-10 rounded-lg bg-purple-500/10 flex items-center justify-center flex-shrink-0">
                      <Target className="w-5 h-5 text-purple-500" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-muted-foreground mb-1">Наиболее релевантно</p>
                      <p className="text-sm font-semibold truncate">
                        {'name' in insights.topResult ? insights.topResult.name : insights.topResult.sku}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <div className="flex-1 bg-muted rounded-full h-2 overflow-hidden">
                          <div
                            className="h-full bg-purple-500 transition-all"
                            style={{ width: `${Math.min(insights.topResult.relevanceScore, 100)}%` }}
                          />
                        </div>
                        <span className="text-xs font-medium">{insights.topResult.relevanceScore.toFixed(0)}%</span>
                      </div>
                    </div>
                  </div>
                </ModernCard>
              </motion.div>
            )}

            {/* Average Match Score */}
            <motion.div variants={fadeInUp}>
              <ModernCard className="h-full">
                <div className="flex items-start gap-3">
                  <div className="w-10 h-10 rounded-lg bg-orange-500/10 flex items-center justify-center flex-shrink-0">
                    <TrendingUp className="w-5 h-5 text-orange-500" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-muted-foreground mb-1">Средняя релевантность</p>
                    <p className="text-2xl font-bold">{insights.avgScore.toFixed(0)}%</p>
                    <div className="flex-1 bg-muted rounded-full h-2 overflow-hidden mt-2">
                      <div
                        className="h-full bg-orange-500 transition-all"
                        style={{ width: `${Math.min(insights.avgScore, 100)}%` }}
                      />
                    </div>
                  </div>
                </div>
              </ModernCard>
            </motion.div>

            {/* Search Time */}
            {insights.searchDuration > 0 && (
              <motion.div variants={fadeInUp}>
                <ModernCard className="h-full">
                  <div className="flex items-start gap-3">
                    <div className="w-10 h-10 rounded-lg bg-cyan-500/10 flex items-center justify-center flex-shrink-0">
                      <Clock className="w-5 h-5 text-cyan-500" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-muted-foreground mb-1">Время поиска</p>
                      <p className="text-2xl font-bold">{formatDuration(insights.searchDuration)}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {insights.queryWords} {insights.queryWords === 1 ? 'слово' : 'слов'} • {insights.detectedLanguage}
                      </p>
                    </div>
                  </div>
                </ModernCard>
              </motion.div>
            )}
          </motion.div>

          {/* Suggestions for low results */}
          {insights.totalResults < 5 && insights.totalResults > 0 && (
            <motion.div
              variants={fadeInUp}
              initial="initial"
              animate="animate"
              className="mt-4"
            >
              <ModernCard className="bg-amber-50 dark:bg-amber-950/20 border-amber-200 dark:border-amber-800">
                <p className="text-sm font-medium mb-2">💡 Подсказки для улучшения результатов:</p>
                <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                  <li>Попробуйте использовать более широкие ключевые слова</li>
                  <li>Проверьте правильность написания</li>
                  <li>Используйте фильтры для уточнения поиска</li>
                  <li>Просмотрите похожие категории</li>
                </ul>
              </ModernCard>
            </motion.div>
          )}
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}

