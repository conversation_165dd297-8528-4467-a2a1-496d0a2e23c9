import { getSystemDB } from '../db'
import { compareAttributes, type AttributeWithTemplate, type SynonymEntry, type SynonymGroupInfo } from '../lib/matching/attributeComparator'

const uniqKey = (t: number, v: string) => `${t}__${v.trim().toLowerCase()}`

function norm(v: string) { return v.trim().toLowerCase() }

async function main() {
  const db = getSystemDB()
  const ids = [53, 54]
  const items = await db.catalogItem.findMany({
    where: { id: { in: ids } },
    include: { attributes: { include: { template: true } }, brand: true },
  })

  if (items.length !== 2) {
    console.log('WARN: expected 2 items, got', items.length)
  }

  // вытащим все атрибуты + оставим только те, где name либо title похожи на "seal" (не знаем точное имя шаблона в БД)
  const sealAttrs = items.map(it => ({
    id: it.id,
    attrs: it.attributes.filter(a =>
      a.template.name?.toLowerCase().includes('seal') || a.template.title?.toLowerCase().includes('seal') || a.template.name === 'seal_type'
    )
  }))

  const templatesInPlay = new Set<number>()
  for (const it of sealAttrs) for (const a of it.attrs) templatesInPlay.add(a.templateId)

  console.log('=== ITEMS: seal-like attributes ===')
  for (const it of sealAttrs) {
    console.log(`Item #${it.id}`)
    for (const a of it.attrs) {
      console.log({ templateId: a.templateId, template: { name: a.template.name, title: a.template.title, dataType: a.template.dataType }, raw: a.value, norm: norm(a.value) })
    }
  }

  const tIds = Array.from(templatesInPlay)
  if (tIds.length === 0) {
    console.log('No templates matching seal found for items, abort.')
    return
  }

  const synonyms = await db.attributeSynonym.findMany({
    where: { group: { templateId: { in: tIds } } },
    include: { group: true },
  })
  const groups = await db.attributeSynonymGroup.findMany({
    where: { templateId: { in: tIds } },
    select: { id: true, parentId: true, templateId: true, compatibilityLevel: true, canonicalValue: true, notes: true },
  })

  console.log('=== SYNONYMS for templatesInPlay ===')
  // выведем только интересующие значения TC / TCV
  for (const s of synonyms) {
    const v = s.value
    if (norm(v) === 'tc' || norm(v) === 'tcv') {
      console.log({
        value: s.value,
        value_norm: norm(s.value),
        groupId: s.groupId,
        groupLevel: s.group.compatibilityLevel,
        synLevel: (s as any).compatibilityLevel ?? null,
        notes: [(s as any).notes, s.group.notes].filter(Boolean).join('; ') || null,
        templateId: s.group.templateId,
      })
    }
  }

  console.log('=== GROUPS (tree) ===')
  const groupsById = new Map<number, SynonymGroupInfo>(groups.map(g => [g.id, { parentId: g.parentId ?? null, level: g.compatibilityLevel as any, notes: g.notes ?? null, canonical: g.canonicalValue ?? null }]))
  function lineage(id: number): number[] { const path:number[]=[]; let cur: number|undefined = id; const seen = new Set<number>(); while(cur && !seen.has(cur)){ seen.add(cur); path.push(cur); const g = groupsById.get(cur); if(!g?.parentId) break; cur = g.parentId } return path }
  for (const g of groups) {
    console.log({ id: g.id, parentId: g.parentId, templateId: g.templateId, level: g.compatibilityLevel, canonical: g.canonicalValue, notes: g.notes, pathUp: lineage(g.id) })
  }

  // Попробуем сравнить конкретные значения 53 vs 54 через compareAttributes
  const findSeal = (it: typeof items[number]) => it.attributes.find(a => a.templateId && (a.template.name?.toLowerCase().includes('seal') || a.template.title?.toLowerCase().includes('seal') || a.template.name === 'seal_type'))
  const it53 = items.find(x => x.id === 53)
  const it54 = items.find(x => x.id === 54)
  const a53 = it53 && findSeal(it53)
  const a54 = it54 && findSeal(it54)
  if (a53 && a54 && a53.templateId === a54.templateId) {
    const tId = a53.templateId
    const synonymMap = new Map<string, SynonymEntry[]>()
    for (const s of synonyms) {
      const lvl = (s as any).compatibilityLevel ?? s.group.compatibilityLevel
      const notes = [s.group.notes, (s as any).notes].filter(Boolean).join('; ') || null
      const key = uniqKey(s.group.templateId, s.value)
      const arr = synonymMap.get(key) || []
      arr.push({ templateId: s.group.templateId, groupId: s.groupId, level: lvl as any, notes, canonical: (s.group as any).canonicalValue ?? null })
      synonymMap.set(key, arr)
    }

    const itemAttrByTemplateId = new Map<number, AttributeWithTemplate>([[tId, { templateId: tId, value: a53.value, numericValue: null, template: { name: a53.template.name, title: a53.template.title, dataType: 'STRING' as const, tolerance: null } }]])
    const partAttrByTemplateId = new Map<number, AttributeWithTemplate>([[tId, { templateId: tId, value: a54.value, numericValue: null, template: { name: a54.template.name, title: a54.template.title, dataType: 'STRING' as const, tolerance: null } }]])

    const res = compareAttributes({ itemAttrByTemplateId, partAttrByTemplateId, synonymMap, groupsById })
    console.log('=== compareAttributes(53 vs 54) RESULT ===')
    console.log(JSON.stringify(res, null, 2))
  } else {
    console.log('WARN: Cannot directly compare: missing seal_type or templateId mismatch')
  }
}

main().catch((e) => { console.error(e); process.exit(1) }).then(() => process.exit(0))

