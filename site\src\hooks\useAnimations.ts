/**
 * Переиспользуемые хуки анимации для консистентных паттернов анимации
 * Все хуки автоматически поддерживают prefers-reduced-motion
 */

import { useEffect, useState, useRef, useMemo, type RefObject } from 'react'
import type { Variants, Transition, TargetAndTransition } from 'motion/react'
import { useAnimation } from 'motion/react'
import * as variants from '@/lib/animation-variants'

// ============================================================================
// Types
// ============================================================================

interface StaggerOptions {
  baseDelay?: number
  staggerDelay?: number
  variant?: Variants
}

interface FadeInOptions {
  delay?: number
  duration?: number
  direction?: 'up' | 'down' | 'left' | 'right' | 'none'
}

interface SlideInOptions {
  delay?: number
  duration?: number
  distance?: number
}

interface ScaleInOptions {
  delay?: number
  duration?: number
  from?: number
  spring?: boolean
}

interface HoverOptions {
  scale?: number
  y?: number
  transition?: Transition
}

interface StaggeredListOptions {
  staggerDelay?: number
  variant?: Variants
}

interface AnimationOptions {
  delay?: number
  duration?: number
}

// Типы для motion props
type MotionProps = {
  initial?: string | Record<string, unknown>
  animate?: string | Record<string, unknown>
  exit?: string | Record<string, unknown>
  transition?: Transition
  variants?: Variants
  whileHover?: TargetAndTransition
  whileTap?: TargetAndTransition
}

// ============================================================================
// Core Hooks
// ============================================================================

/**
 * Определяет, предпочитает ли пользователь уменьшенное движение
 * @returns true если пользователь предпочитает уменьшенное движение
 */
export function useReducedMotion(): boolean {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)

  useEffect(() => {
    // На клиенте проверяем настройки
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return prefersReducedMotion
}

/**
 * Создает stagger анимацию для списков/сеток
 * @param itemCount - Количество элементов
 * @param options - Опции анимации
 * @returns Массив props для каждого элемента
 */
export function useStaggerAnimation(
  itemCount: number,
  options: StaggerOptions = {}
): MotionProps[] {
  const {
    baseDelay = 0,
    staggerDelay = 0.1,
    variant = variants.staggerItem,
  } = options

  const reducedMotion = useReducedMotion()

  return useMemo(() => {
    return Array.from({ length: itemCount }, (_, index) => {
      if (reducedMotion) {
        return {
          initial: { opacity: 1, y: 0 },
          animate: { opacity: 1, y: 0 },
          transition: { duration: 0 },
        }
      }

      return {
        variants: variant,
        initial: 'initial',
        animate: 'animate',
        transition: {
          delay: baseDelay + index * staggerDelay,
          duration: 0.3,
        },
      }
    })
  }, [itemCount, baseDelay, staggerDelay, variant, reducedMotion])
}

/**
 * Простой fade-in хук с опциональным направлением
 * @param options - Опции fade-in анимации
 * @returns Motion props
 */
export function useFadeIn(options: FadeInOptions = {}): MotionProps {
  const { delay = 0, duration = 0.3, direction = 'none' } = options
  const reducedMotion = useReducedMotion()

  return useMemo(() => {
    if (reducedMotion) {
      return {
        initial: { opacity: 1 },
        animate: { opacity: 1 },
        transition: { duration: 0 },
      }
    }

    let variant: Variants
    switch (direction) {
      case 'up':
        variant = variants.fadeInUp
        break
      case 'down':
        variant = variants.fadeInDown
        break
      case 'left':
        variant = variants.fadeInLeft
        break
      case 'right':
        variant = variants.fadeInRight
        break
      default:
        variant = variants.fadeIn
    }

    return {
      variants: variant,
      initial: 'initial',
      animate: 'animate',
      exit: 'exit',
      transition: { delay, duration },
    }
  }, [delay, duration, direction, reducedMotion])
}

/**
 * Slide-in анимация с указанным направлением
 * @param direction - Направление скольжения
 * @param options - Опции анимации
 * @returns Motion props
 */
export function useSlideIn(
  direction: 'left' | 'right' | 'top' | 'bottom',
  options: SlideInOptions = {}
): MotionProps {
  const { delay = 0, duration = 0.3, distance = 100 } = options
  const reducedMotion = useReducedMotion()

  return useMemo(() => {
    if (reducedMotion) {
      return {
        initial: { opacity: 1, x: 0, y: 0 },
        animate: { opacity: 1, x: 0, y: 0 },
        transition: { duration: 0 },
      }
    }

    let variant: Variants
    switch (direction) {
      case 'left':
        variant = variants.slideInFromLeft
        break
      case 'right':
        variant = variants.slideInFromRight
        break
      case 'top':
        variant = variants.slideInFromTop
        break
      case 'bottom':
        variant = variants.slideInFromBottom
        break
    }

    // Масштабируем distance если отличается от 100
    if (distance !== 100) {
      const scale = distance / 100
      variant = {
        ...variant,
        initial: {
          ...(variant.initial as Record<string, unknown>),
          x: direction === 'left' ? -distance : direction === 'right' ? distance : 0,
          y: direction === 'top' ? -distance : direction === 'bottom' ? distance : 0,
        },
      }
    }

    return {
      variants: variant,
      initial: 'initial',
      animate: 'animate',
      exit: 'exit',
      transition: { delay, duration },
    }
  }, [direction, delay, duration, distance, reducedMotion])
}

/**
 * Scale-in анимация
 * @param options - Опции анимации
 * @returns Motion props
 */
export function useScaleIn(options: ScaleInOptions = {}): MotionProps {
  const { delay = 0, duration = 0.2, from = 0.95, spring = false } = options
  const reducedMotion = useReducedMotion()

  return useMemo(() => {
    if (reducedMotion) {
      return {
        initial: { opacity: 1, scale: 1 },
        animate: { opacity: 1, scale: 1 },
        transition: { duration: 0 },
      }
    }

    const variant = spring ? variants.popIn : variants.scaleIn

    // Кастомизируем начальный scale если отличается от дефолтного
    if (from !== 0.95 && !spring) {
      const customVariant = {
        ...variant,
        initial: { opacity: 0, scale: from },
      }
      return {
        variants: customVariant,
        initial: 'initial',
        animate: 'animate',
        exit: 'exit',
        transition: spring ? variants.springTransition : { delay, duration },
      }
    }

    return {
      variants: variant,
      initial: 'initial',
      animate: 'animate',
      exit: 'exit',
      transition: spring ? variants.springTransition : { delay, duration },
    }
  }, [delay, duration, from, spring, reducedMotion])
}

/**
 * Триггер анимации при появлении элемента в viewport
 * @param threshold - Порог видимости (0-1)
 * @returns ref для элемента, состояние видимости и контроллеры анимации
 */
export function useScrollAnimation(threshold: number = 0.1): {
  ref: RefObject<HTMLElement>
  isInView: boolean
  controls: ReturnType<typeof useAnimation>
} {
  const ref = useRef<HTMLElement>(null)
  const [isInView, setIsInView] = useState(false)
  const [hasAnimated, setHasAnimated] = useState(false)
  const controls = useAnimation()
  const reducedMotion = useReducedMotion()

  useEffect(() => {
    if (typeof window === 'undefined' || !ref.current) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setIsInView(true)
          setHasAnimated(true)

          if (!reducedMotion) {
            controls.start({
              opacity: 1,
              y: 0,
              transition: { duration: 0.5, ease: 'easeOut' },
            })
          }
        }
      },
      { threshold }
    )

    observer.observe(ref.current)

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [threshold, hasAnimated, controls, reducedMotion])

  return { ref, isInView, controls }
}

/**
 * Hover анимация для интерактивных элементов
 * @param options - Опции hover эффекта
 * @returns Motion props для hover/tap
 */
export function useHoverAnimation(options: HoverOptions = {}): MotionProps {
  const {
    scale = 1.02,
    y = -4,
    transition = variants.springTransition,
  } = options
  const reducedMotion = useReducedMotion()

  return useMemo(() => {
    if (reducedMotion) {
      return {
        whileHover: {},
        whileTap: {},
        transition: {},
      }
    }

    return {
      whileHover: { scale, y, transition },
      whileTap: { scale: 0.98 },
      transition,
    }
  }, [scale, y, transition, reducedMotion])
}

/**
 * Хук для page transition анимаций
 * @returns Motion props для page wrapper
 */
export function usePageTransition(): MotionProps {
  const reducedMotion = useReducedMotion()

  return useMemo(() => {
    if (reducedMotion) {
      return {
        initial: { opacity: 1 },
        animate: { opacity: 1 },
        exit: { opacity: 1 },
        transition: { duration: 0 },
      }
    }

    return {
      variants: variants.pageTransition,
      initial: 'initial',
      animate: 'animate',
      exit: 'exit',
    }
  }, [reducedMotion])
}

/**
 * Обертка вокруг useAnimation с поддержкой reduced motion
 * @returns AnimationControls с поддержкой reduced motion
 */
export function useAnimationControls() {
  const controls = useAnimation()
  const reducedMotion = useReducedMotion()

  // Оборачиваем метод start для поддержки reduced motion
  const wrappedControls = useMemo(() => {
    return {
      ...controls,
      start: (animation: TargetAndTransition | string) => {
        if (reducedMotion) {
          // Мгновенное завершение анимации
          if (typeof animation === 'object') {
            return controls.start({
              ...animation,
              transition: { duration: 0 },
            })
          }
        }
        return controls.start(animation)
      },
    }
  }, [controls, reducedMotion])

  return wrappedControls
}

/**
 * Комплексный хук для анимированных списков
 * @param items - Массив элементов
 * @param options - Опции stagger анимации
 * @returns containerProps и функцию getItemProps
 */
export function useStaggeredList<T>(
  items: T[],
  options: StaggeredListOptions = {}
): {
  containerProps: MotionProps
  getItemProps: (index: number) => MotionProps
} {
  const { staggerDelay = 0.1, variant = variants.listItem } = options
  const reducedMotion = useReducedMotion()

  return useMemo(() => {
    if (reducedMotion) {
      return {
        containerProps: {
          initial: { opacity: 1 },
          animate: { opacity: 1 },
        },
        getItemProps: () => ({
          initial: { opacity: 1, y: 0 },
          animate: { opacity: 1, y: 0 },
          transition: { duration: 0 },
        }),
      }
    }

    return {
      containerProps: {
        variants: variants.listContainer,
        initial: 'initial',
        animate: 'animate',
      },
      getItemProps: (index: number) => ({
        variants: variant,
        initial: 'initial',
        animate: 'animate',
        transition: {
          delay: index * staggerDelay,
        },
      }),
    }
  }, [items.length, staggerDelay, variant, reducedMotion])
}

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Получает animation props из variant с дополнительными опциями
 * @param variant - Variant анимации
 * @param options - Дополнительные опции
 * @returns Motion props
 */
export function getAnimationProps(
  variant: Variants,
  options: AnimationOptions = {}
): MotionProps {
  const { delay = 0, duration = 0.3 } = options

  return {
    variants: variant,
    initial: 'initial',
    animate: 'animate',
    exit: 'exit',
    transition: { delay, duration },
  }
}

/**
 * Объединяет несколько variants в один
 * @param variants - Массив variants для объединения
 * @returns Объединенный variant
 */
export function mergeVariants(...variantsList: Variants[]): Variants {
  const merged: Variants = {}

  variantsList.forEach((variant) => {
    Object.keys(variant).forEach((key) => {
      const state = variant[key]
      if (typeof state === 'object' && state !== null) {
        merged[key] = {
          ...(merged[key] as Record<string, unknown>),
          ...(state as Record<string, unknown>),
        }
      } else {
        merged[key] = state
      }
    })
  })

  return merged
}

/**
 * Добавляет задержку к variant
 * @param variant - Исходный variant
 * @param delay - Задержка в секундах
 * @returns Variant с задержкой
 */
export function createDelayedVariant(variant: Variants, delay: number): Variants {
  const delayed: Variants = {}

  Object.keys(variant).forEach((key) => {
    const state = variant[key]
    if (typeof state === 'object' && state !== null) {
      delayed[key] = {
        ...(state as Record<string, unknown>),
        transition: {
          ...(state as { transition?: Record<string, unknown> }).transition,
          delay,
        },
      }
    } else {
      delayed[key] = state
    }
  })

  return delayed
}

