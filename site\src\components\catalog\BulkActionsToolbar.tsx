import { motion } from 'framer-motion'
import { 
  Download, 
  FileSpreadsheet, 
  GitCompare, 
  X, 
  CheckSquare 
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { navigate } from 'astro:transitions/client'
import { 
  exportCatalogItemsToCSV, 
  exportCatalogItemsToExcel,
  exportPartsToCSV,
  exportPartsToExcel 
} from '@/services/export.service'
import { analytics } from '@/lib/analytics'
import type { Part } from '@/lib/types'

type CatalogItem = {
  id: number
  sku: string
  description: string | null
  brand?: {
    id: number
    name: string
    isOem: boolean
  } | null
  attributes?: Array<{
    value: string
    template?: {
      name: string
      dataType: string
    } | null
  }>
}

export interface BulkActionsToolbarProps {
  selectedItems: Array<Part | CatalogItem>
  isPro: boolean
  onClearSelection: () => void
  className?: string
}

export function BulkActionsToolbar({ 
  selectedItems, 
  isPro, 
  onClearSelection, 
  className = '' 
}: BulkActionsToolbarProps) {
  const selectedCount = selectedItems.length
  const isCatalogItems = selectedItems.length > 0 && 'sku' in selectedItems[0]
  const isParts = selectedItems.length > 0 && 'partCategory' in selectedItems[0]
  
  // Проверяем количество для сравнения (2-5 элементов)
  const canCompare = selectedCount >= 2 && selectedCount <= 5
  
  const handleExportCSV = () => {
    try {
      if (isCatalogItems) {
        exportCatalogItemsToCSV(selectedItems as CatalogItem[])
        analytics.bulkExportTriggered('csv', selectedCount, 'catalogItem')
      } else if (isParts) {
        exportPartsToCSV(selectedItems as Part[])
        analytics.bulkExportTriggered('csv', selectedCount, 'part')
      }
    } catch (error) {
      console.error('Failed to export CSV:', error)
      alert('Ошибка при экспорте CSV')
    }
  }
  
  const handleExportExcel = () => {
    if (!isPro) {
      alert('Экспорт в Excel доступен только для PRO пользователей')
      return
    }
    
    try {
      if (isCatalogItems) {
        exportCatalogItemsToExcel(selectedItems as CatalogItem[])
        analytics.bulkExportTriggered('excel', selectedCount, 'catalogItem')
      } else if (isParts) {
        exportPartsToExcel(selectedItems as Part[])
        analytics.bulkExportTriggered('excel', selectedCount, 'part')
      }
    } catch (error) {
      console.error('Failed to export Excel:', error)
      alert('Ошибка при экспорте Excel')
    }
  }
  
  const handleCompare = () => {
    if (!canCompare) {
      alert('Выберите от 2 до 5 элементов для сравнения')
      return
    }
    
    const ids = selectedItems.map(item => item.id).join(',')
    const itemType = isCatalogItems ? 'catalogItem' : 'part'
    
    analytics.bulkComparisonTriggered(selectedCount, itemType)
    
    // Навигация на страницу сравнения
    navigate(`/comparison?ids=${ids}&type=${itemType}`)
  }
  
  return (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      exit={{ y: 20, opacity: 0 }}
      className={`
        sticky top-0 z-20 
        bg-background/95 backdrop-blur-lg 
        border-b border-border 
        shadow-lg
        ${className}
      `}
    >
      <div className="container mx-auto px-4 py-3">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
          {/* Левая часть: информация о выделении */}
          <div className="flex items-center gap-2">
            <Badge variant="default" className="text-sm font-semibold">
              {selectedCount} выбрано
            </Badge>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearSelection}
              className="h-8 px-2"
            >
              <X className="h-4 w-4 mr-1" />
              Снять выделение
            </Button>
          </div>
          
          {/* Правая часть: действия */}
          <div className="flex items-center gap-2 flex-wrap">
            {/* Кнопка сравнения */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleCompare}
              disabled={!canCompare}
              className="h-8"
              title={
                canCompare 
                  ? 'Сравнить выбранные элементы' 
                  : 'Выберите от 2 до 5 элементов для сравнения'
              }
            >
              <GitCompare className="h-4 w-4 mr-1.5" />
              Сравнить
            </Button>
            
            {/* Кнопка экспорта CSV */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportCSV}
              className="h-8"
            >
              <Download className="h-4 w-4 mr-1.5" />
              CSV
            </Button>
            
            {/* Кнопка экспорта Excel (PRO) */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportExcel}
              disabled={!isPro}
              className="h-8 relative"
              title={isPro ? 'Экспорт в Excel' : 'Доступно только для PRO'}
            >
              <FileSpreadsheet className="h-4 w-4 mr-1.5" />
              Excel
              {!isPro && (
                <Badge 
                  variant="default" 
                  className="absolute -top-2 -right-2 text-[10px] px-1.5 py-0"
                >
                  PRO
                </Badge>
              )}
            </Button>
          </div>
        </div>
      </div>
      
      {/* Мобильная версия: фиксированный тулбар внизу */}
      <style>{`
        @media (max-width: 640px) {
          .bulk-actions-toolbar-mobile {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 50;
          }
        }
      `}</style>
    </motion.div>
  )
}

