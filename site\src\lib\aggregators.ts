/**
 * Утилиты агрегации и статистики для анализа данных
 */

import type { Part } from '@/lib/types';
import type {
  PartAttribute,
  CatalogItem,
  CatalogItemAttribute,
  PartApplicability,
  Brand,
} from 'packages/shared-types/src/index';
import type { ComparisonResult } from './comparators';
import { formatNumber, parseNumericValue } from './formatters';

// ============================================================================
// Types
// ============================================================================

export type NumericStats = {
  min: number;
  max: number;
  avg: number;
  median: number;
  count: number;
  sum: number;
  unit?: string | null;
};

export type AttributeDistribution = {
  value: string;
  count: number;
  percentage: number;
};

export type AttributeStats = {
  templateId: number;
  templateName: string;
  dataType: string;
  numericStats?: NumericStats;
  distribution?: AttributeDistribution[];
  uniqueValues: number;
  missingCount: number;
};

// ============================================================================
// Numeric Statistics
// ============================================================================

/**
 * Рассчитать статистику для массива числовых значений
 */
export function calculateNumericStats(
  values: number[],
  unit?: string
): NumericStats | null {
  if (values.length === 0) return null;

  const sorted = [...values].sort((a, b) => a - b);
  const min = sorted[0];
  const max = sorted[sorted.length - 1];
  const sum = values.reduce((acc, val) => acc + val, 0);
  const avg = sum / values.length;

  // Медиана
  const mid = Math.floor(sorted.length / 2);
  const median =
    sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];

  return {
    min,
    max,
    avg,
    median,
    count: values.length,
    sum,
    unit,
  };
}

/**
 * Агрегировать числовой атрибут по всем элементам
 */
export function aggregateNumericAttribute(
  attributes: (PartAttribute | CatalogItemAttribute)[],
  templateId: number
): NumericStats | null {
  const values = attributes
    .filter((attr) => attr.templateId === templateId)
    .map((attr) => parseNumericValue(attr.value))
    .filter((val): val is number => val !== null);

  if (values.length === 0) return null;

  const firstAttr = attributes.find((attr) => attr.templateId === templateId);
  const unit = firstAttr && 'template' in firstAttr ? firstAttr.template.unit : null;

  return calculateNumericStats(values, unit);
}

/**
 * Рассчитать процентиль
 */
export function calculatePercentile(values: number[], percentile: number): number {
  if (values.length === 0) return 0;

  const sorted = [...values].sort((a, b) => a - b);
  const index = (percentile / 100) * (sorted.length - 1);
  const lower = Math.floor(index);
  const upper = Math.ceil(index);
  const weight = index - lower;

  return sorted[lower] * (1 - weight) + sorted[upper] * weight;
}

/**
 * Рассчитать стандартное отклонение
 */
export function calculateStandardDeviation(values: number[]): number {
  if (values.length === 0) return 0;

  const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
  const variance =
    values.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / values.length;

  return Math.sqrt(variance);
}

// ============================================================================
// Distribution Analysis
// ============================================================================

/**
 * Рассчитать распределение значений
 */
export function calculateDistribution(values: string[]): AttributeDistribution[] {
  if (values.length === 0) return [];

  const counts = new Map<string, number>();

  for (const value of values) {
    counts.set(value, (counts.get(value) || 0) + 1);
  }

  const total = values.length;
  const distribution: AttributeDistribution[] = [];

  for (const [value, count] of counts) {
    distribution.push({
      value,
      count,
      percentage: (count / total) * 100,
    });
  }

  // Сортируем по частоте (убывание)
  return distribution.sort((a, b) => b.count - a.count);
}

/**
 * Агрегировать строковый атрибут по всем элементам
 */
export function aggregateStringAttribute(
  attributes: (PartAttribute | CatalogItemAttribute)[],
  templateId: number
): AttributeDistribution[] {
  const values = attributes
    .filter((attr) => attr.templateId === templateId)
    .map((attr) => attr.value)
    .filter((val) => val && val.trim() !== '');

  return calculateDistribution(values);
}

/**
 * Получить топ N наиболее частых значений
 */
export function getTopValues(
  distribution: AttributeDistribution[],
  limit: number
): AttributeDistribution[] {
  return distribution.slice(0, limit);
}

// ============================================================================
// Multi-Attribute Aggregation
// ============================================================================

/**
 * Агрегировать статистику по всем атрибутам
 */
export function aggregateAllAttributes(
  items: Part[] | CatalogItem[]
): AttributeStats[] {
  const allAttributes = items.flatMap((item) => item.attributes);
  return aggregateByTemplate(allAttributes);
}

/**
 * Группировать и агрегировать по template ID
 */
export function aggregateByTemplate(
  attributes: (PartAttribute | CatalogItemAttribute)[]
): AttributeStats[] {
  // Группируем по templateId
  const grouped = new Map<number, (PartAttribute | CatalogItemAttribute)[]>();

  for (const attr of attributes) {
    const existing = grouped.get(attr.templateId) || [];
    existing.push(attr);
    grouped.set(attr.templateId, existing);
  }

  const stats: AttributeStats[] = [];

  // Агрегируем каждую группу
  for (const [templateId, attrs] of grouped) {
    const firstAttr = attrs[0];
    if (!firstAttr || !('template' in firstAttr)) continue;

    const template = firstAttr.template;
    const dataType = template.dataType;

    const stat: AttributeStats = {
      templateId,
      templateName: template.name,
      dataType,
      uniqueValues: new Set(attrs.map((a) => a.value)).size,
      missingCount: attrs.filter((a) => !a.value || a.value.trim() === '').length,
    };

    if (dataType === 'NUMBER') {
      const numStats = aggregateNumericAttribute(attrs, templateId);
      if (numStats) {
        stat.numericStats = numStats;
      }
    } else if (dataType === 'STRING') {
      stat.distribution = aggregateStringAttribute(attrs, templateId);
    }

    stats.push(stat);
  }

  return stats;
}

// ============================================================================
// Brand/Category Aggregation
// ============================================================================

/**
 * Агрегировать по брендам
 */
export function aggregateBrands(items: CatalogItem[]): Array<{
  brandId: number;
  brandName: string;
  isOem: boolean;
  count: number;
  percentage: number;
}> {
  const brandCounts = new Map<
    number,
    { name: string; isOem: boolean; count: number }
  >();

  for (const item of items) {
    const brand = item.brand;
    if (!brand) continue;

    const existing = brandCounts.get(brand.id);
    if (existing) {
      existing.count++;
    } else {
      brandCounts.set(brand.id, {
        name: brand.name,
        isOem: brand.isOem,
        count: 1,
      });
    }
  }

  const total = items.length;
  const result = [];

  for (const [brandId, data] of brandCounts) {
    result.push({
      brandId,
      brandName: data.name,
      isOem: data.isOem,
      count: data.count,
      percentage: (data.count / total) * 100,
    });
  }

  return result.sort((a, b) => b.count - a.count);
}

/**
 * Агрегировать по категориям
 */
export function aggregateCategories(items: Part[]): Array<{
  categoryId: number;
  categoryName: string;
  count: number;
  percentage: number;
}> {
  const categoryCounts = new Map<number, { name: string; count: number }>();

  for (const item of items) {
    const category = item.partCategory;
    if (!category) continue;

    const existing = categoryCounts.get(category.id);
    if (existing) {
      existing.count++;
    } else {
      categoryCounts.set(category.id, {
        name: category.name,
        count: 1,
      });
    }
  }

  const total = items.length;
  const result = [];

  for (const [categoryId, data] of categoryCounts) {
    result.push({
      categoryId,
      categoryName: data.name,
      count: data.count,
      percentage: (data.count / total) * 100,
    });
  }

  return result.sort((a, b) => b.count - a.count);
}

/**
 * Агрегировать по уровням точности
 */
export function aggregateAccuracy(applicabilities: PartApplicability[]): Array<{
  accuracy: 'EXACT_MATCH' | 'MATCH_WITH_NOTES' | 'PARTIAL_MATCH' | 'LEGACY_MATCH';
  count: number;
  percentage: number;
}> {
  const accuracyCounts = new Map<string, number>();

  for (const app of applicabilities) {
    const accuracy = app.accuracy;
    accuracyCounts.set(accuracy, (accuracyCounts.get(accuracy) || 0) + 1);
  }

  const total = applicabilities.length;
  const result = [];

  for (const [accuracy, count] of accuracyCounts) {
    result.push({
      accuracy: accuracy as
        | 'EXACT_MATCH'
        | 'MATCH_WITH_NOTES'
        | 'PARTIAL_MATCH'
        | 'LEGACY_MATCH',
      count,
      percentage: (count / total) * 100,
    });
  }

  return result.sort((a, b) => b.count - a.count);
}

// ============================================================================
// Comparison Aggregation
// ============================================================================

/**
 * Агрегировать результаты сравнений по статусам
 */
export function aggregateComparisonResults(comparisons: ComparisonResult[]): Array<{
  status: ComparisonResult['status'];
  count: number;
  percentage: number;
}> {
  const statusCounts = new Map<ComparisonResult['status'], number>();

  for (const comparison of comparisons) {
    statusCounts.set(
      comparison.status,
      (statusCounts.get(comparison.status) || 0) + 1
    );
  }

  const total = comparisons.length;
  const result = [];

  for (const [status, count] of statusCounts) {
    result.push({
      status,
      count,
      percentage: (count / total) * 100,
    });
  }

  return result.sort((a, b) => b.count - a.count);
}

/**
 * Рассчитать общее качество соответствия
 */
export function calculateOverallMatchQuality(comparisons: ComparisonResult[]): {
  score: number;
  label: 'excellent' | 'good' | 'fair' | 'poor';
  exactMatches: number;
  nearMatches: number;
  mismatches: number;
  missingAttributes: number;
} {
  const exactMatches = comparisons.filter((c) => c.status === 'exact').length;
  const nearMatches = comparisons.filter(
    (c) => c.status === 'near' || c.status === 'legacy'
  ).length;
  const mismatches = comparisons.filter((c) => c.status === 'mismatch').length;
  const missingAttributes = comparisons.filter((c) => c.status === 'missing').length;

  const total = comparisons.length;
  const score =
    total > 0
      ? Math.round(
          ((exactMatches * 1.0 + nearMatches * 0.7) / total) * 100
        )
      : 0;

  let label: 'excellent' | 'good' | 'fair' | 'poor';
  if (score >= 90) label = 'excellent';
  else if (score >= 70) label = 'good';
  else if (score >= 50) label = 'fair';
  else label = 'poor';

  return {
    score,
    label,
    exactMatches,
    nearMatches,
    mismatches,
    missingAttributes,
  };
}

// ============================================================================
// Time-based Aggregation
// ============================================================================

/**
 * Группировать элементы по временному диапазону
 */
export function aggregateByTimeRange<T extends { createdAt: Date }>(
  items: T[],
  range: 'day' | 'week' | 'month'
): Map<string, T[]> {
  const grouped = new Map<string, T[]>();

  for (const item of items) {
    const date = new Date(item.createdAt);
    let key: string;

    switch (range) {
      case 'day':
        key = date.toISOString().split('T')[0];
        break;
      case 'week': {
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        key = weekStart.toISOString().split('T')[0];
        break;
      }
      case 'month':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        break;
    }

    const existing = grouped.get(key) || [];
    existing.push(item);
    grouped.set(key, existing);
  }

  return grouped;
}

/**
 * Рассчитать темп роста
 */
export function calculateGrowthRate(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
}

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Группировать элементы по ключу
 */
export function groupBy<T>(
  items: T[],
  keyFn: (item: T) => string | number
): Map<string | number, T[]> {
  const grouped = new Map<string | number, T[]>();

  for (const item of items) {
    const key = keyFn(item);
    const existing = grouped.get(key) || [];
    existing.push(item);
    grouped.set(key, existing);
  }

  return grouped;
}

/**
 * Подсчитать элементы по ключу
 */
export function countBy<T>(
  items: T[],
  keyFn: (item: T) => string | number
): Map<string | number, number> {
  const counts = new Map<string | number, number>();

  for (const item of items) {
    const key = keyFn(item);
    counts.set(key, (counts.get(key) || 0) + 1);
  }

  return counts;
}

/**
 * Рассчитать процент с точностью
 */
export function percentageOf(value: number, total: number): number {
  if (total === 0) return 0;
  return (value / total) * 100;
}

