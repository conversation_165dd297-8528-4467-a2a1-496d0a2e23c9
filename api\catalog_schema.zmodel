import "aggregate_schemas_schema"

abstract model TimeStamped {
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}


// =====================================================
// ОСНОВНАЯ СХЕМА ДАННЫХ КАТАЛОГА
// =====================================================
// ВАЖНО (контекст домена):
// 1) Эталон vs Реализация
//    - Part/PartAttribute — эталонная группа взаимозаменяемости ("идеальная" деталь) и её нормализованные атрибуты.
//    - CatalogItem/CatalogItemAttribute — конкретный товар производителя (реальные значения как в каталоге).
//
// 2) Правила сопоставления (для автоматического объединения CatalogItem в Part):
//    - Для числовых атрибутов используется поле AttributeTemplate.tolerance: значения считаются эквивалентными,
//      если |value_item − value_part| ≤ tolerance (единица измерения следует из шаблона/контекста атрибута).
//    - Для строковых атрибутов используется AttributeSynonymGroup/AttributeSynonym, привязанная к конкретному шаблону (templateId).
//      Значения считаются эквивалентными, если они равны без учета регистра или входят в одну и ту же группу синонимов.
//
// 3) Уровень совместимости строковых значений (SynonymCompatibilityLevel)
//    влияет на итоговую метку точности применимости (ApplicabilityAccuracy):
//      - EXACT → обычно ведёт к EXACT_MATCH
//      - NEAR или LEGACY → обычно ведёт к MATCH_WITH_NOTES (и требует notes для пояснений/ограничений)
//
// 4) Источники notes (примечаний) при создании связи CatalogItem ↔ Part:
//    - notes из AttributeSynonymGroup, если совпадение достигнуто по группе синонимов уровня NEAR/LEGACY;
//    - текстовая пометка о совпадении "по допуску", если числовые значения совпали в пределах tolerance.
//
// Эти правила консистентны с документацией в ARCHITECTURE_ATTRIBUTES_AND_SIMILARITY.md и используются в серверном подборе.

// Перечисление для типов данных атрибута.
// Позволяет системе понимать, как обрабатывать и валидировать поле `value` в модели Attribute.
enum AttributeDataType {
    STRING  // Строковые значения (например, "NBR", "ABC123")
    NUMBER  // Числовые значения (целые или дробные, например, "10.000", "100")
    BOOLEAN // Логические значения (true/false)
    DATE    // Значения даты/времени
    JSON    // Для комплексных, неструктурированных данных (использовать осторожно)
}

// Перечисление для единиц измерения атрибутов.
// Стандартизирует единицы измерения по всему каталогу для удобства фильтрации и сравнения.
enum AttributeUnit {
    // Длина
    MM      // Миллиметры
    INCH    // Дюймы
    FT      // Футы

    // Вес
    G       // Граммы
    KG      // Килограммы
    T       // Тонны
    LB      // Фунты

    // Объем
    ML      // Миллилитры
    L       // Литры
    GAL     // Галлоны

    // Время
    SEC     // Секунды
    MIN     // Минуты
    H       // Часы

    // Количество
    PCS     // Штуки
    SET     // Комплекты
    PAIR    // Пары

    // Давление
    BAR     // Бары
    PSI     // Фунты на квадратный дюйм

    // Мощность
    KW      // Киловатты
    HP      // Лошадиные силы

    // Крутящий момент
    NM      // Ньютон-метры
    RPM     // Обороты в минуту

    // Температура
    C       // Градусы Цельсия
    F       // Градусы Фаренгейта

    // Относительные единицы
    PERCENT // Проценты
}

// Перечисление для меток точности применимости.
// Определяет, насколько точно CatalogItem соответствует группе взаимозаменяемости Part.
enum ApplicabilityAccuracy {
    EXACT_MATCH          // Полное совпадение: деталь является прямым аналогом.
    MATCH_WITH_NOTES     // Совпадение с нюансами: аналог, но есть условия, описанные в поле `notes`.
    REQUIRES_MODIFICATION // Требуется доработка: деталь подходит после внесения изменений.
    PARTIAL_MATCH        // Частичное совпадение: подходит только часть комплекта или не все функции.
}

// Уровень совместимости значений в группе синонимов
// Используется для тонкой настройки степени эквивалентности строковых значений
// при сопоставлении CatalogItem с эталонными атрибутами Part
enum SynonymCompatibilityLevel {
    EXACT  // Полная эквивалентность (без ограничений). Обычно маппится на EXACT_MATCH
    NEAR   // Близкая эквивалентность. Требует пояснений/notes. Обычно маппится на MATCH_WITH_NOTES
    LEGACY // Условная/устаревшая эквивалентность. Требует осторожности и notes. Обычно маппится на MATCH_WITH_NOTES
}

// =====================================================
// МЕДИА-ХРАНИЛИЩЕ (локальная ФС)
// =====================================================
// Единая сущность для хранения метаданных загруженных изображений.
// Файл физически лежит в `api/uploads/**`, а доступен по URL `/api/uploads/**`.
model MediaAsset {
    id        Int      @id @default(autoincrement())

    // Базовое имя файла на диске (уникальное внутри своей папки)
    fileName  String
    // MIME-тип, например: image/jpeg, image/png, image/webp, application/pdf
    mimeType  String
    // Размер файла в байтах
    fileSize  Int?
    // Относительный URL для отдачи через статику Hono: `/api/uploads/...`
    url       String   @unique

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Обратные связи 1:1
    part         Part?         @relation("PartImage")
    partCategory PartCategory? @relation("PartCategoryImage")


    // Обратные связи M:N (галереи/вложения)
    parts         Part[]         @relation("PartMediaAssets")
    catalogItems  CatalogItem[]  @relation("CatalogItemMediaAssets")

    // Обратная связь 1:1 с CatalogItem (основное изображение/медиа)
    catalogItem   CatalogItem?   @relation("CatalogItemImage")

    @@index([mimeType])
    @@index([fileName])
    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}


// =====================================================
// АРХИТЕКТУРА АТРИБУТОВ (Простая + Шаблоны)
// =====================================================

// Группа атрибутов для организации шаблонов
model AttributeGroup {
    id          Int                 @id @default(autoincrement())
    name        String              @unique
    description String?

    // Связь с шаблонами атрибутов
    templates   AttributeTemplate[]

    // Иерархия групп (родительские категории)
    parent      AttributeGroup? @relation("AttributeGroupHierarchy", fields: [parentId], references: [id])
    parentId    Int?
    children    AttributeGroup[] @relation("AttributeGroupHierarchy")

    @@index([parentId])
    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// СПРАВОЧНИК ШАБЛОНОВ АТРИБУТОВ - источник правды
// Определяет, какие атрибуты могут существовать в системе, их тип, единицы измерения и т.д.
// Атрибутыв моем проекте это как характеристики товара, только динамические.
// Например: ширина, длина, высота, материал.
model AttributeTemplate {
    id          Int               @id @default(autoincrement())
    name        String            @unique // Системное имя (например, "inner_diameter", "material")
    title       String            // Отображаемое название ("Внутренний диаметр", "Материал")
    description String?

    dataType    AttributeDataType @default(STRING)
    unit        AttributeUnit?    // Единица измерения по умолчанию
    isRequired  Boolean           @default(false)

    // Валидация (опционально)
    minValue      Float?   // Для числовых значений
    maxValue      Float?   // Для числовых значений
    allowedValues String[] // Для enum значений, например ["steel", "aluminum", "plastic"]

    // Допустимое отклонение для числовых атрибутов (толеранс)
    // Пример: PartAttribute = 30.0, CatalogItemAttribute = 30.01 → считаем совпадением,
    // если |30.01 − 30.0| ≤ tolerance. Единица измерения следует из контекста атрибута (unit).
    tolerance   Float?            @default(0)

    // Группировка
    group       AttributeGroup?   @relation(fields: [groupId], references: [id])
    groupId     Int?

    // Обратные связи с конкретными экземплярами атрибутов
    partAttributes        PartAttribute[]
    catalogItemAttributes CatalogItemAttribute[]
    equipmentAttributes   EquipmentModelAttribute[]

    // Обратная связь с группами синонимов
    synonymGroups AttributeSynonymGroup[]

    createdAt   DateTime          @default(now())
    updatedAt   DateTime          @updatedAt

    @@index([groupId])
    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// НОВАЯ МОДЕЛЬ: Группа синонимов для значений атрибутов
// Позволяет объединять разные строковые значения в одну логическую группу.
// Применяется при сравнении CatalogItemAttribute.value с эталонным PartAttribute.value
// Например, для атрибута "Тип сальника" можно создать группу "Стандартные типы",
// в которую войдут значения "TC", "SC", "BABSL".
model AttributeSynonymGroup {
    id          Int      @id @default(autoincrement())
    name        String   @unique // Название группы, например "Стандартные типы уплотнений TC/SC"
    description String?

    // Связь с шаблоном атрибута, к которому применяется эта группа
    template    AttributeTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
    templateId  Int


    // Иерархия групп (опционально)
    parent      AttributeSynonymGroup? @relation("SynonymGroupHierarchy", fields: [parentId], references: [id])
    parentId    Int?
    children    AttributeSynonymGroup[] @relation("SynonymGroupHierarchy")

    // Каноническое значение для группы (нормализованная форма). Может отсутствовать у "контейнерных" (родительских) групп
    canonicalValue String?

    // Список всех синонимов в этой группе
    synonyms    AttributeSynonym[]

    // Уровень совместимости значений в группе (влияет на итоговую точность сопоставления)
    compatibilityLevel SynonymCompatibilityLevel @default(EXACT)

    // Пояснения/правила применения данной эквивалентности.
    // Эти notes рекомендуется показывать в UI при матче NEAR/LEGACY
    // и переносить в PartApplicability.notes при создании связи.
    notes       String?


    @@allow('all', auth() != null && auth().role == 'ADMIN')
    @@index([parentId])

    @@index([templateId])
}

// НОВАЯ МОДЕЛЬ: Конкретный синоним в группе
// Хранит одно из эквивалентных значений.
model AttributeSynonym {
    id          Int      @id @default(autoincrement())
    value       String   // Значение синонима, например, "TC" (сравнение в логике сопоставления происходит без учета регистра)

    // Связь с родительской группой
    group       AttributeSynonymGroup @relation(fields: [groupId], references: [id], onDelete: Cascade)
    groupId     Int

    // Доп. метаданные
    notes String?
    brand   Brand? @relation(fields: [brandId], references: [id])
    brandId Int?
    // Персональный override уровня совместимости
    compatibilityLevel SynonymCompatibilityLevel?

    @@unique([groupId, value]) // Значение может входить в группу только один раз
    @@index([value])           // Индекс для быстрого поиска группы по значению
    @@allow('all', auth() != null && auth().role == 'ADMIN')
}


// Атрибут для группы взаимозаменяемости (Part)
// Атрибутыв моем проекте это как характеристики товара, только динамические.
// Например: ширина, длина, высота, материал.
model PartAttribute {
    id         Int      @id @default(autoincrement())
    value      String   // Эталонное значение атрибута (строка, хранит и числа в виде строки: "15.5", и текст: "Сталь")

    // Дублирование числового значения для оптимизации запросов
    // Автоматически синхронизируется с value если value содержит число
    numericValue Float? // NULL для нечисловых атрибутов, число для числовых

    // Связь с группой
    part       Part     @relation(fields: [partId], references: [id], onDelete: Cascade)
    partId     Int

    // Связь с шаблоном. `onDelete: Restrict` предотвращает удаление шаблона, если он используется.
    template   AttributeTemplate @relation(fields: [templateId], references: [id], onDelete: Restrict)
    templateId Int

    @@unique([partId, templateId]) // У одной детали не может быть два одинаковых атрибута
    @@index([partId])
    @@index([templateId])
    @@index([value]) // Для быстрого поиска по значению
    @@index([numericValue]) // Индекс для диапазонных запросов

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// Атрибут для каталожной позиции (CatalogItem)
model CatalogItemAttribute {
    id         Int      @id @default(autoincrement())
    value      String   // Фактическое значение от производителя (сопоставляется с PartAttribute с учетом tolerance/синонимов)

    // Дублирование числового значения для оптимизации запросов
    numericValue Float? // NULL для нечисловых атрибутов, число для числовых

    // Связь с позицией каталога
    catalogItem   CatalogItem @relation(fields: [catalogItemId], references: [id], onDelete: Cascade)
    catalogItemId Int

    // Связь с шаблоном
    template      AttributeTemplate @relation(fields: [templateId], references: [id], onDelete: Restrict)
    templateId    Int

    @@unique([catalogItemId, templateId])
    @@index([catalogItemId])
    @@index([templateId])
    @@index([value])
    @@index([numericValue])

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// Атрибут для модели техники (EquipmentModel)
model EquipmentModelAttribute {
    id         Int      @id @default(autoincrement())
    value      String

    // Дублирование числового значения для оптимизации запросов
    numericValue Float? // NULL для нечисловых атрибутов, число для числовых

    // Связь с моделью техники
    equipmentModel   EquipmentModel @relation(fields: [equipmentModelId], references: [id], onDelete: Cascade)
    equipmentModelId String

    // Связь с шаблоном
    template         AttributeTemplate @relation(fields: [templateId], references: [id], onDelete: Restrict)
    templateId       Int

    @@unique([equipmentModelId, templateId])
    @@index([equipmentModelId])
    @@index([templateId])
    @@index([value])
    @@index([numericValue])

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}


// Применимость Детали к Модели Техники
// Связывает группу взаимозаменяемости (Part) с конкретной моделью техники (EquipmentModel)
model EquipmentApplicability {
    id               Int              @id @default(autoincrement())

    // Связь с группой взаимозаменимости
    part             Part             @relation(fields: [partId], references: [id], onDelete: Cascade)
    partId           Int

    // Связь с моделью техники
    equipmentModel   EquipmentModel   @relation(fields: [equipmentModelId], references: [id], onDelete: Cascade)
    equipmentModelId String

    // Метаданные о применимости
    notes            String?          // Заметки, например, "Только для моделей выпущенных после 2021 года"

    @@unique([partId, equipmentModelId])
    @@index([equipmentModelId])

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// Применимость Каталожной Позиции к Модели Техники
// Связывает конкретный артикул (CatalogItem) с конкретной моделью техники (EquipmentModel)
model CatalogItemApplicability {
    id               Int              @id @default(autoincrement())

    // Связь с каталожной позицией
    catalogItem      CatalogItem      @relation(fields: [catalogItemId], references: [id], onDelete: Cascade)
    catalogItemId    Int

    // Связь с моделью техники
    equipmentModel   EquipmentModel   @relation(fields: [equipmentModelId], references: [id], onDelete: Cascade)
    equipmentModelId String

    // Метаданные о применимости
    notes            String?          // Заметки, например, "Только для моделей выпущенных после 2021 года"

    @@unique([catalogItemId, equipmentModelId])
    @@index([equipmentModelId])

    // Чтение Part доступно только PRO пользователям (коммерческая информация)
    @@allow('read', auth() != null && auth().subscription == 'PRO')
    // Создание/изменение/удаление только для админов
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}


// Группа взаимозаменяемости (деталь).
// Представляет собой абстрактную "деталь", которая объединяет несколько конкретных артикулов (CatalogItem) от разных производителей,
// являющихся полными или частичными аналогами друг друга.
model Part extends TimeStamped {
    id               Int                 @id @default(autoincrement()) // Уникальный идентификатор группы

    name             String?       // Наименование группы (например, "Сальник коленвала передний")

    // Иерархия для представления сложных узлов и агрегатов (например, двигатель -> блок цилиндров -> поршень)
    parent           Part?               @relation("PartHierarchy", fields: [parentId], references: [id]) // Родительская группа (узел)
    parentId         Int?          // Внешний ключ для связи с родительской группой
    children         Part[]              @relation("PartHierarchy") // Список дочерних групп (подузлов)
    level            Int                 @default(0) // Уровень вложенности в иерархии: 0 - корень, 1 - подузел и т.д.
    path             String        // Материализованный путь для быстрого выбора поддеревьев

    // ПРОСТАЯ СВЯЗЬ С АТРИБУТАМИ - магия Prisma снова с нами!
    attributes       PartAttribute[]

    // Связь с конкретными артикулами через промежуточную таблицу PartApplicability.
    // Позволяет хранить метаданные о связи (точность, примечания).
    applicabilities  PartApplicability[]
    equipmentApplicabilities EquipmentApplicability[]
    matchingProposals MatchingProposal[]

    // Связь со схемами агрегатов
    aggregateSchemas AggregateSchema[]
    schemaPositions  SchemaPosition[]

    // Основное изображение детали (локальная ФС)
    imageId   Int? @unique
    image     MediaAsset? @relation("PartImage", fields: [imageId], references: [id], onDelete: SetNull)
    // Дополнительные медиа (галерея/вложения: изображения и PDF)
    mediaAssets MediaAsset[] @relation("PartMediaAssets")

    @@index([parentId])
    @@index([path])

    // Доступ к данным о взаимозаменяемости для PRO пользователей и ADMIN
    @@allow('read', auth() != null && (auth().subscription == 'PRO' || auth().role == 'ADMIN'))
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')

    partCategory PartCategory @relation(fields: [partCategoryId], references: [id])
    partCategoryId Int
}

model PartApplicability {
    id            Int                   @id @default(autoincrement()) // Уникальный идентификатор записи о применимости

    part          Part                  @relation(fields: [partId], references: [id]) // Ссылка на группу взаимозаменяемости
    partId        Int                   // Внешний ключ для Part

    catalogItem   CatalogItem           @relation(fields: [catalogItemId], references: [id]) // Ссылка на конкретный артикул
    catalogItemId Int                   // Внешний ключ для CatalogItem

    // Метаданные о связи (итог сопоставления)
    // accuracy:
    //   - EXACT_MATCH: все ключевые атрибуты совпали (числа — точно или в пределах tolerance, строки — точно или по группе EXACT)
    //   - MATCH_WITH_NOTES: присутствуют мягкие совпадения (числа — по tolerance; строки — по группе NEAR/LEGACY);
    //                        рекомендуется сохранять пояснения в notes
    // notes: пояснения по условиям применимости (например, из AttributeSynonymGroup.notes для NEAR/LEGACY
    //        или системная пометка «Совпадение по допуску» для числовых атрибутов)
    accuracy      ApplicabilityAccuracy @default(EXACT_MATCH)
    notes         String?

    // Уникальный ключ, чтобы один и тот же артикул не мог быть добавлен в одну группу дважды.
    @@unique([partId, catalogItemId])
    // Индекс для быстрого поиска всех групп, в которые входит один артикул.
    @@index([catalogItemId])

    @@allow('read', auth() != null && (auth().subscription == 'PRO' || auth().role == 'ADMIN'))
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// Каталожная позиция (артикул).
// Конкретная деталь от конкретного производителя с уникальным номером (SKU).
model CatalogItem {
    id              Int                    @id @default(autoincrement()) // Уникальный идентификатор каталожной позиции

    sku             String                 @trim @upper @length(1, 64) // Артикул (SKU) - уникальный номер детали у производителя.
    source          String? // Источник информации об этой записи (например, "Официальный каталог", "Данные клиента")

    description     String? // Описание детали от производителя

    brand           Brand                  @relation(fields: [brandId], references: [id]) // Производитель (бренд) детали
    brandId         Int     // Внешний ключ для связи с Brand

    isPublic        Boolean                @default(true) // Является ли запись публичной и видимой для всех пользователей

    // ПРОСТАЯ СВЯЗЬ С АТРИБУТАМИ
    attributes      CatalogItemAttribute[]

    // Связь с группами взаимозаменяемости через промежуточную таблицу PartApplicability.
    applicabilities PartApplicability[]
    matchingProposals MatchingProposal[]
    equipmentApplicabilities CatalogItemApplicability[]

    // Основное изображение/медиа позиции (локальная ФС)
    imageId   Int? @unique
    image     MediaAsset? @relation("CatalogItemImage", fields: [imageId], references: [id], onDelete: SetNull)

    // Дополнительные медиа (галерея/вложения: изображения и PDF)
    mediaAssets MediaAsset[] @relation("CatalogItemMediaAssets")

    @@unique([sku, brandId])
    @@index([brandId])

    // @@allow('read', true)
    @@allow('read,create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// ----------------------------------------------------
// ПРЕДЛОЖЕНИЯ ЭКВИВАЛЕНТОВ (очередь на подтверждение инженером)
// ----------------------------------------------------

// Статус предложения
enum ProposalStatus {
    PENDING
    APPROVED
    REJECTED
    INVALIDATED
}

// Предложение связать CatalogItem с существующей группой Part
model MatchingProposal {
    id                   Int                   @id @default(autoincrement())

    catalogItem          CatalogItem           @relation(fields: [catalogItemId], references: [id], onDelete: Cascade)
    catalogItemId        Int

    part                 Part                  @relation(fields: [partId], references: [id], onDelete: Cascade)
    partId               Int

    // Автоподсказки из алгоритма
    accuracySuggestion   ApplicabilityAccuracy @default(EXACT_MATCH)
    notesSuggestion      String?
    details              Json?                 // подробности сопоставления для UI

    status               ProposalStatus        @default(PENDING)

    createdAt            DateTime              @default(now())
    updatedAt            DateTime              @updatedAt

    @@index([status])
    // Один активный (PENDING) дубликат стараемся не создавать на уровне кода;
    // уникальный составной ключ по status допускает историю решений
    @@unique([catalogItemId, partId, status])

    @@allow('all', auth() != null && auth().role == 'ADMIN')
}

// ----------------------------------------------------
// ОПЕРАЦИИ МАССОВОГО ПОДБОРА (Bulk Matching Operations)
// ----------------------------------------------------

// Статус операции массового подбора
enum BulkMatchingOperationStatus {
    DRAFT       // Только настроена, не выполнялась
    PREVIEWED   // Проведён предварительный расчёт/превью результатов
    EXECUTED    // Операция выполнена — созданы PartApplicability
    ROLLED_BACK // Операция откатана
}
enum BulkMatchingAuditAction {
    CREATE_APPLICABILITY
    DELETE_APPLICABILITY
    SKIP_EXISTING
}

// Модель для отслеживания операций массового подбора эквивалентов.
// Служит для хранения конфигурации подбора, результатов превью/выполнения и информации для отката.
model BulkMatchingOperation extends TimeStamped {
    id           Int                           @id @default(autoincrement())

    // Текущий статус операции
    status       BulkMatchingOperationStatus   @default(DRAFT)

    // Конфигурация операции (набор правил/фильтров), хранится как JSON
    config       Json

    // Сводные данные по превью/выполнению (счётчики, статистика). Заполняется после превью/выполнения.
    summary      Json?

    // Информация, необходимая для отката (например, список созданных идентификаторов), хранится как JSON
    rollbackInfo Json?

    // Логи аудита для этой операции
    auditLogs    BulkMatchingAuditLog[] @relation("BulkMatchingAuditLogOperation")

    // Метки времени выполнения/отката
    performedAt  DateTime?
    rolledBackAt DateTime?

    @@index([status])
    @@index([performedAt])
    @@index([rolledBackAt])
    @@allow('all', auth() != null && auth().role == 'ADMIN')
}

// Модель для логирования действий массового подбора
model BulkMatchingAuditLog extends TimeStamped {
    id            Int                      @id @default(autoincrement())
    operation     BulkMatchingOperation    @relation("BulkMatchingAuditLogOperation", fields: [operationId], references: [id])
    operationId   Int

    action        BulkMatchingAuditAction

    partId        Int?
    catalogItemId Int?

    accuracy      ApplicabilityAccuracy?
    notes         String?
    details       Json?
    error         String?

    @@index([operationId])
    @@index([partId])
    @@index([catalogItemId])

    @@allow('all', auth() != null && auth().role == 'ADMIN')
}

// ----------------------------------------------------
// ПРАВИЛА ПОДБОРА (ПРОЕКТ, НА БУДУЩЕЕ)
// ----------------------------------------------------
// Ниже — черновик моделей для персистентных «наборов правил подбора эквивалентов».
// Задача этих моделей: централизованно описывать, какие атрибуты обязательны
// для совпадения при поиске эквивалентов, чтобы минимизировать ложные объединения
// «по одному признаку». В текущей итерации правила задаются в UI динамически
// (requiredTemplateIds), но в будущем мы сможем хранить пресеты по категориям/брендам.
// ВАЖНО: модели закомментированы, чтобы не менять рабочую Prisma-схему.
// Когда придёт время — раскомментируем и добавим миграции/роуты/UI.

// Зачем это
// Централизованные «наборы правил подбора» позволят фиксировать обязательные атрибуты для совпадения в зависимости от контекста (категория детали, бренд или глобально).
// Это снизит риск ошибок при объединении «по одному признаку» и даст единый управляемый механизм без правки кода.
// Сейчас мы уже поддержали выбор обязательных атрибутов в UI и в API (requiredTemplateIds), а когда понадобится — можно раскомментировать модели, сделать миграции и добавить UI для управления наборами правил.

/*
model MatchingRuleSet {
    id          Int           @id @default(autoincrement())
    // Человекочитаемое имя набора правил (например: «Подшипники — базовые атрибуты»)
    name        String        @length(1, 150)

    // Область применения набора правил
    // GLOBAL — по умолчанию для всего каталога
    // PART_CATEGORY — для конкретного каталога запчастей (PartCategory)
    // BRAND — для конкретного бренда (Brand)
    scopeType   MatchingRuleScope
    scopeId     Int?

    // Может быть дефолтным для своей области (например, для категории)
    isDefault   Boolean       @default(false)

    // Список правил этой группы
    rules       MatchingRule[]

    createdAt   DateTime      @default(now())
    updatedAt   DateTime      @updatedAt

    @@index([scopeType, scopeId])
    @@allow('all', auth() != null && auth().role == 'ADMIN')
}

enum MatchingRuleScope {
    GLOBAL
    PART_CATEGORY
    BRAND
}

model MatchingRule {
    id          Int             @id @default(autoincrement())

    ruleSet     MatchingRuleSet @relation(fields: [ruleSetId], references: [id], onDelete: Cascade)
    ruleSetId   Int

    // Какой атрибут обязателен (или иной тип условия в будущем)
    template    AttributeTemplate @relation(fields: [templateId], references: [id])
    templateId  Int

    // На будущее: поддержка гибких условий (минимальное число совпадений, вес, и т.д.)
    // Пока достаточно «обязательный признак».
    required    Boolean         @default(true)

    // Порядок для UI
    order       Int             @default(0)

    createdAt   DateTime        @default(now())
    updatedAt   DateTime        @updatedAt

    @@index([ruleSetId])
    @@index([templateId])
    @@allow('all', auth() != null && auth().role == 'ADMIN')
}
*/


/// Иерархическая категория запчастей
/// Примеры: «Двигатель», «Система смазки», «Фильтры топлива» …
model PartCategory {
    id          Int                @id @default(autoincrement())
    name        String             @length(1, 150)                 // Отображаемое название
    slug        String             @unique @regex("^[a-z0-9-]+$")  // Для SEO-/URL
    description String?            @length(1, 1000)

    // Иерархия «родитель ↔ дети»
    parent      PartCategory?      @relation("CategoryHierarchy", fields: [parentId], references: [id])
    parentId    Int?
    children    PartCategory[]     @relation("CategoryHierarchy")

    level       Int                @default(0)       // Глубина в дереве
    path        String         // Материализованный путь: '01/02/15'
    icon        String?        // Опциональная пиктограмма

    // Связь с группами взаимозаменяемости (Part)
    parts       Part[]

    // Основное изображение категории (локальная ФС)
    imageId   Int? @unique
    image     MediaAsset? @relation("PartCategoryImage", fields: [imageId], references: [id], onDelete: SetNull)

    createdAt   DateTime           @default(now())
    updatedAt   DateTime          @updatedAt

    @@index([parentId])
    @@index([slug])
    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// Производитель (Бренд).
// Хранит информацию о производителях техники (OEM) и производителях запчастей.
model Brand {
    id             Int              @id @default(autoincrement()) // Уникальный идентификатор бренда

    name           String           @length(1, 100) // Название бренда: "Caterpillar", "Komatsu", "SKF"
    slug           String           @unique @regex("^[a-z0-9-]+$") @length(1, 100) @trim @lower // URL-совместимое имя для использования в ссылках (например, "caterpillar")
    country        String?          @length(2, 100) // Страна происхождения бренда
    isOem          Boolean          @default(false) // Флаг: true = производитель техники (OEM), false = производитель запчастей (Aftermarket)

    // Связанные сущности
    catalogItems   CatalogItem[]    // Список всех каталожных позиций этого бренда
    equipmentModel EquipmentModel[] // Список всех моделей техники этого бренда

    @@index([isOem]) // Индекс для ускорения фильтрации по типу производителя
    // Обратная связь с синонимами атрибутов (информационная)
    attributeSynonyms AttributeSynonym[]


    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// Модель техники.
// Описывает конкретную модель техники, к которой могут применяться запчасти.
model EquipmentModel {
    id         String                    @id @default(uuid()) // Уникальный идентификатор модели техники

    createdAt  DateTime                  @default(now()) // Дата создания записи
    updatedAt  DateTime                  @updatedAt      // Дата последнего обновления

    name       String                    @length(1, 100) // Название модели: "Экскаватор CAT 320D"
    partApplicabilities EquipmentApplicability[]
    catalogItemApplicabilities CatalogItemApplicability[]
    brand      Brand?                    @relation(fields: [brandId], references: [id]) // Связь с брендом-производителем техники
    brandId    Int?        // Внешний ключ для связи с Brand

    // ПРОСТАЯ СВЯЗЬ С АТРИБУТАМИ
    attributes EquipmentModelAttribute[]

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'ADMIN')
    @@index([brandId])
    @@map("equipment_model")

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}
