"use client"

import { useMemo } from "react"
import { authClient } from "@/lib/auth-client"

export function useSubscription() {
  const { data: session, isPending } = authClient.useSession()
  
  const subscriptionData = useMemo(() => {
    const user = session?.user ?? null
    const subscription = user?.subscription ?? null
    
    return {
      user,
      subscription,
      isPro: subscription === 'PRO',
      isFree: subscription === 'FREE' || (!subscription && !!user), // Default to FREE for authenticated users
      isAuthenticated: !!user,
      isPending,
    }
  }, [session, isPending])
  
  return subscriptionData
}