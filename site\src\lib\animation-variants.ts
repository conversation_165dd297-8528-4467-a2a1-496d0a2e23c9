/**
 * Централизованная библиотека вариантов анимации для motion
 * Все варианты используют GPU-ускоренные свойства (transform, opacity) для производительности
 * Поддерживает prefers-reduced-motion через хуки useAnimations
 */

import type { Variants, Transition } from 'motion/react'

// ============================================================================
// Base Transitions
// ============================================================================

/**
 * Плавный переход по умолчанию
 */
export const smoothTransition: Transition = {
  duration: 0.3,
  ease: 'easeInOut',
}

/**
 * Быстрый переход для мгновенных взаимодействий
 */
export const fastTransition: Transition = {
  duration: 0.15,
  ease: 'easeOut',
}

/**
 * Медленный переход для акцента
 */
export const slowTransition: Transition = {
  duration: 0.5,
  ease: 'easeInOut',
}

/**
 * Spring переход для естественных движений
 */
export const springTransition: Transition = {
  type: 'spring',
  stiffness: 300,
  damping: 30,
}

// ============================================================================
// Fade Variants
// ============================================================================

/**
 * Базовое затухание входа/выхода
 */
export const fadeIn: Variants = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 },
}

/**
 * Затухание с движением вверх
 */
export const fadeInUp: Variants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
}

/**
 * Затухание с движением вниз
 */
export const fadeInDown: Variants = {
  initial: { opacity: 0, y: -20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: 20 },
}

/**
 * Затухание с движением слева
 */
export const fadeInLeft: Variants = {
  initial: { opacity: 0, x: -20 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 20 },
}

/**
 * Затухание с движением справа
 */
export const fadeInRight: Variants = {
  initial: { opacity: 0, x: 20 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -20 },
}

// ============================================================================
// Scale Variants
// ============================================================================

/**
 * Масштабирование с затуханием
 */
export const scaleIn: Variants = {
  initial: { opacity: 0, scale: 0.95 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.95 },
}

/**
 * Масштабирование из центра
 */
export const scaleInCenter: Variants = {
  initial: { opacity: 0, scale: 0.8 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.8 },
}

/**
 * Появление с эффектом "pop" (с spring анимацией)
 */
export const popIn: Variants = {
  initial: { opacity: 0, scale: 0.8 },
  animate: {
    opacity: 1,
    scale: 1,
    transition: springTransition,
  },
  exit: { opacity: 0, scale: 0.9 },
}

// ============================================================================
// Slide Variants
// ============================================================================

/**
 * Скольжение слева с затуханием
 */
export const slideInFromLeft: Variants = {
  initial: { opacity: 0, x: -100 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -100 },
}

/**
 * Скольжение справа с затуханием
 */
export const slideInFromRight: Variants = {
  initial: { opacity: 0, x: 100 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 100 },
}

/**
 * Скольжение сверху с затуханием
 */
export const slideInFromTop: Variants = {
  initial: { opacity: 0, y: -100 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -100 },
}

/**
 * Скольжение снизу с затуханием
 */
export const slideInFromBottom: Variants = {
  initial: { opacity: 0, y: 100 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: 100 },
}

// ============================================================================
// Stagger Variants
// ============================================================================

/**
 * Контейнер для stagger анимации (задержка между дочерними элементами)
 */
export const staggerContainer: Variants = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
}

/**
 * Элемент для stagger анимации
 */
export const staggerItem: Variants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
}

/**
 * Быстрый stagger контейнер
 */
export const staggerFast: Variants = {
  animate: {
    transition: {
      staggerChildren: 0.05,
    },
  },
}

/**
 * Медленный stagger контейнер
 */
export const staggerSlow: Variants = {
  animate: {
    transition: {
      staggerChildren: 0.2,
    },
  },
}

// ============================================================================
// Card Variants
// ============================================================================

/**
 * Эффект hover для карточки
 */
export const cardHover: Variants = {
  hover: {
    scale: 1.02,
    y: -4,
    transition: springTransition,
  },
}

/**
 * Эффект нажатия для карточки
 */
export const cardTap: Variants = {
  tap: { scale: 0.98 },
}

/**
 * Комплексная анимация входа карточки с поддержкой stagger
 */
export const cardEntry: Variants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
}

// ============================================================================
// Modal/Dialog Variants
// ============================================================================

/**
 * Оверлей модального окна
 */
export const modalOverlay: Variants = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 },
}

/**
 * Контент модального окна
 */
export const modalContent: Variants = {
  initial: { opacity: 0, scale: 0.95, y: 20 },
  animate: { opacity: 1, scale: 1, y: 0 },
  exit: { opacity: 0, scale: 0.95, y: 20 },
}

/**
 * Drawer скольжение снизу (для мобильных меню)
 */
export const drawerSlideIn: Variants = {
  initial: { y: '100%' },
  animate: { y: 0 },
  exit: { y: '100%' },
}

// ============================================================================
// List Variants
// ============================================================================

/**
 * Контейнер списка с stagger эффектом
 */
export const listContainer: Variants = {
  initial: { opacity: 0 },
  animate: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

/**
 * Элемент списка
 */
export const listItem: Variants = {
  initial: { opacity: 0, y: 10 },
  animate: { opacity: 1, y: 0 },
}

// ============================================================================
// Page Transition Variants
// ============================================================================

/**
 * Переход между страницами (затухание)
 */
export const pageTransition: Variants = {
  initial: { opacity: 0 },
  animate: { opacity: 1, transition: smoothTransition },
  exit: { opacity: 0, transition: fastTransition },
}

/**
 * Переход между страницами (скольжение)
 */
export const pageSlide: Variants = {
  initial: { opacity: 0, x: 20 },
  animate: { opacity: 1, x: 0, transition: smoothTransition },
  exit: { opacity: 0, x: -20, transition: fastTransition },
}

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Вычисляет задержку для stagger анимации
 * @param index - Индекс элемента
 * @param baseDelay - Базовая задержка в секундах
 * @returns Задержка в секундах
 */
export function getStaggerDelay(index: number, baseDelay: number = 0.1): number {
  return index * baseDelay
}

/**
 * Создает кастомный вариант анимации
 * @param initial - Начальное состояние
 * @param animate - Состояние анимации
 * @param exit - Состояние выхода (опционально)
 * @param transition - Настройки перехода (опционально)
 * @returns Вариант анимации
 */
export function createCustomVariant(
  initial: Record<string, unknown>,
  animate: Record<string, unknown>,
  exit?: Record<string, unknown>,
  transition?: Transition
): Variants {
  const variant: Variants = {
    initial,
    animate: transition ? { ...animate, transition } : animate,
  }
  
  if (exit) {
    variant.exit = exit
  }
  
  return variant
}

/**
 * Оборачивает варианты для поддержки reduced motion
 * Если пользователь предпочитает уменьшенное движение, возвращает мгновенные переходы
 * ПРИМЕЧАНИЕ: Используйте хук useReducedMotion для проверки предпочтений
 * @param variants - Исходные варианты
 * @returns Модифицированные варианты с мгновенными переходами
 */
export function withReducedMotion(variants: Variants): Variants {
  // Эта функция используется внутри хуков, где проверяется prefersReducedMotion
  // Возвращает варианты без анимации (мгновенные переходы)
  const reducedVariants: Variants = {}
  
  Object.keys(variants).forEach((key) => {
    const state = variants[key]
    if (typeof state === 'object' && state !== null) {
      reducedVariants[key] = {
        ...state,
        transition: { duration: 0 },
      }
    } else {
      reducedVariants[key] = state
    }
  })
  
  return reducedVariants
}

