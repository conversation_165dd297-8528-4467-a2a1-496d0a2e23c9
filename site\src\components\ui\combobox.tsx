/**
 * Combobox - autocomplete компонент с multi-select поддержкой
 * Использует cmdk для поиска и фильтрации опций
 */

import React, { useState, useRef, useEffect } from 'react';
import { Check, ChevronsUpDown, X, Search } from 'lucide-react';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from 'cmdk';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { Button } from './button';
import { Badge } from './badge';
import { Checkbox } from './checkbox';
import { cn } from '@/lib/utils';

// ============================================================================
// Types
// ============================================================================

export interface ComboboxOption {
  value: string;
  label: string;
  count?: number;
}

export interface ComboboxProps {
  options: ComboboxOption[];
  value: string[];
  onValueChange: (value: string[]) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  maxSelected?: number;
  showCount?: boolean;
  className?: string;
  disabled?: boolean;
}

// ============================================================================
// Component
// ============================================================================

export default function Combobox({
  options,
  value,
  onValueChange,
  placeholder = 'Выберите опции...',
  searchPlaceholder = 'Поиск...',
  emptyMessage = 'Ничего не найдено',
  maxSelected,
  showCount = false,
  className,
  disabled = false,
}: ComboboxProps): React.ReactElement {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const triggerRef = useRef<HTMLButtonElement>(null);

  // Закрываем popover при клике вне
  useEffect(() => {
    if (!open) {
      setSearch('');
    }
  }, [open]);

  // Обработчик выбора/снятия опции
  const handleToggle = (optionValue: string): void => {
    if (disabled) return;

    const isSelected = value.includes(optionValue);

    if (isSelected) {
      // Удаляем из выбранных
      onValueChange(value.filter((v) => v !== optionValue));
    } else {
      // Добавляем в выбранные (проверяем лимит)
      if (maxSelected && value.length >= maxSelected) {
        return;
      }
      onValueChange([...value, optionValue]);
    }
  };

  // Удаление конкретного значения
  const handleRemove = (optionValue: string, e: React.MouseEvent): void => {
    e.stopPropagation();
    if (disabled) return;
    onValueChange(value.filter((v) => v !== optionValue));
  };

  // Очистка всех значений
  const handleClearAll = (e: React.MouseEvent): void => {
    e.stopPropagation();
    if (disabled) return;
    onValueChange([]);
  };

  // Получаем выбранные опции для отображения
  const selectedOptions = options.filter((opt) => value.includes(opt.value));

  // Получаем label для trigger button
  const getTriggerLabel = (): string => {
    if (value.length === 0) {
      return placeholder;
    }
    if (value.length === 1) {
      return selectedOptions[0]?.label || placeholder;
    }
    return `Выбрано: ${value.length}`;
  };

  return (
    <div className={cn('w-full', className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            ref={triggerRef}
            variant="outline"
            role="combobox"
            aria-expanded={open}
            disabled={disabled}
            className={cn(
              'w-full justify-between',
              value.length === 0 && 'text-muted-foreground'
            )}
          >
            <span className="truncate flex-1 text-left">{getTriggerLabel()}</span>
            <div className="flex items-center gap-1 shrink-0">
              {value.length > 0 && !disabled && (
                <Badge
                  variant="secondary"
                  className="h-5 min-w-5 px-1.5 text-xs mr-1"
                  onClick={handleClearAll}
                >
                  {value.length}
                </Badge>
              )}
              <ChevronsUpDown className="h-4 w-4 opacity-50" />
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start" style={{ width: triggerRef.current?.offsetWidth }}>
          <Command shouldFilter={false}>
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <input
                className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                placeholder={searchPlaceholder}
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
            <CommandList className="max-h-64">
              <CommandEmpty className="py-6 text-center text-sm text-muted-foreground">
                {emptyMessage}
              </CommandEmpty>
              <CommandGroup>
                {options
                  .filter((option) => {
                    if (!search) return true;
                    return option.label.toLowerCase().includes(search.toLowerCase());
                  })
                  .map((option) => {
                    const isSelected = value.includes(option.value);
                    return (
                      <CommandItem
                        key={option.value}
                        value={option.value}
                        onSelect={() => handleToggle(option.value)}
                        className="flex items-center gap-2 px-2 py-2 cursor-pointer"
                      >
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={() => handleToggle(option.value)}
                          className="h-4 w-4"
                          onClick={(e) => e.stopPropagation()}
                        />
                        <span className="flex-1 truncate">{option.label}</span>
                        {showCount && option.count !== undefined && (
                          <span className="text-xs text-muted-foreground">
                            ({option.count})
                          </span>
                        )}
                        {isSelected && <Check className="h-4 w-4 shrink-0" />}
                      </CommandItem>
                    );
                  })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Отображение выбранных значений как badges */}
      {selectedOptions.length > 0 && (
        <div className="flex flex-wrap gap-1.5 mt-2">
          {selectedOptions.map((option) => (
            <Badge
              key={option.value}
              variant="secondary"
              className="gap-1 pr-1 pl-2 py-1"
            >
              <span className="text-xs">{option.label}</span>
              <button
                type="button"
                onClick={(e) => handleRemove(option.value, e)}
                disabled={disabled}
                className="rounded-full hover:bg-secondary-foreground/20 p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}

