<template>
  <div class="space-y-4">
    <div class="flex justify-between items-center">
      <h2 class="text-xl font-semibold">Database Snapshots</h2>
      <VButton @click="showCreateDialog = true">
        <template #default>
          <div class="flex items-center gap-2">
            <Plus class="w-4 h-4" />
            <span>Create Snapshot</span>
          </div>
        </template>
      </VButton>
    </div>

    <VDataTable :value="snapshots?.snapshots" :loading="isLoading" striped-rows>
      <Column field="name" header="Name" />
      <Column field="description" header="Description" />
      <Column field="type" header="Type" />
      <Column field="status" header="Status">
        <template #body="{ data }">
          <VBadge
            :value="data.status"
            :severity="getBadgeSeverity(data.status)"
          />
        </template>
      </Column>
      <Column field="error" header="Error">
        <template #body="{ data }">
          <span
            v-if="data.error"
            class="block max-w-xs truncate text-sm text-red-500"
            :title="data.error"
          >
            {{ data.error }}
          </span>
          <span v-else class="text-sm text-muted-foreground">-</span>
        </template>
      </Column>
      <Column field="createdAt" header="Created At">
        <template #body="{ data }">
          {{ formatDate(data.createdAt) }}
        </template>
      </Column>
      <Column field="fileSize" header="Size">
        <template #body="{ data }">
          {{ formatSize(data.fileSize) }}
        </template>
      </Column>
      <Column header="Actions">
        <template #body="{ data }">
          <div class="flex items-center gap-2">
            <VButton
              text
              rounded
              :disabled="data.status !== 'READY'"
              @click="restoreSnapshot(data.id)"
            >
              <template #default>
                <RotateCcw class="w-4 h-4" />
              </template>
            </VButton>
            <VButton text rounded @click="deleteSnapshot(data.id)">
              <template #default>
                <Trash2 class="w-4 h-4 text-red-500" />
              </template>
            </VButton>
          </div>
        </template>
      </Column>
    </VDataTable>

    <CreateSnapshotDialog
      v-model:visible="showCreateDialog"
      @created="queryClient.invalidateQueries({ queryKey: snapshotsQueryKey })"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useTrpc } from "@/composables/useTrpc";
import CreateSnapshotDialog from "./CreateSnapshotDialog.vue";
import { useToast } from "@/composables/useToast";
import VButton from "@/volt/Button.vue";
import VDataTable from "@/volt/DataTable.vue";
import VBadge from "@/volt/Badge.vue";
import Column from "primevue/column";
import { Plus, RotateCcw, Trash2 } from "lucide-vue-next";
import { useQuery, useMutation, useQueryClient } from "@tanstack/vue-query";

type SnapshotStatus = "CREATING" | "READY" | "RESTORING" | "FAILED" | "DELETED";
type SnapshotsResponse = Awaited<ReturnType<typeof client.snapshots.list.query>>;

const { client } = useTrpc();
const toast = useToast();
const queryClient = useQueryClient();

const showCreateDialog = ref(false);

const snapshotsQueryKey = ["admin", "snapshots", "list"] as const;

const { data: snapshots, isLoading } = useQuery<SnapshotsResponse>({
  queryKey: snapshotsQueryKey,
  queryFn: () => client.snapshots.list.query({}),
  refetchInterval: (query) => {
    const data = query.state.data as SnapshotsResponse | undefined;
    const hasInProgress =
      data?.snapshots?.some(
        (snapshot: SnapshotsResponse["snapshots"][number]) =>
          snapshot.status === "CREATING" || snapshot.status === "RESTORING"
      ) ?? false;
    return hasInProgress ? 3000 : false;
  },
});

const restoreMutation = useMutation({
  mutationFn: (id: string) => client.snapshots.restore.mutate({ id }),
  onSuccess: () => {
    toast.success("Success", "Snapshot restore initiated");
    queryClient.invalidateQueries({ queryKey: snapshotsQueryKey });
  },
  onError: (error) => {
    const message = error instanceof Error ? error.message : "Unknown error";
    toast.error("Error", `Failed to restore snapshot: ${message}`);
  },
});

const deleteMutation = useMutation({
  mutationFn: (id: string) => client.snapshots.delete.mutate({ id }),
  onSuccess: () => {
    toast.success("Success", "Snapshot deleted");
    queryClient.invalidateQueries({ queryKey: snapshotsQueryKey });
  },
  onError: (error) => {
    const message = error instanceof Error ? error.message : "Unknown error";
    toast.error("Error", `Failed to delete snapshot: ${message}`);
  },
});

const restoreSnapshot = (id: string) => {
  if (
    !confirm(
      "Are you sure you want to restore this snapshot? This will overwrite the current database."
    )
  )
    return;
  restoreMutation.mutate(id);
};

const deleteSnapshot = (id: string) => {
  if (!confirm("Are you sure you want to delete this snapshot?")) return;
  deleteMutation.mutate(id);
};

const getBadgeSeverity = (status: SnapshotStatus) => {
  switch (status) {
    case "READY":
      return "success";
    case "CREATING":
    case "RESTORING":
      return "info";
    case "FAILED":
      return "danger";
    default:
      return "warning";
  }
};

function formatDate(d: Date | string) {
  const dt = typeof d === "string" ? new Date(d) : d;
  return dt.toLocaleString();
}

function formatSize(size?: number | null) {
  if (!size || size <= 0) return "-";
  const units = ["B", "KB", "MB", "GB", "TB"];
  let s = size;
  let i = 0;
  while (s >= 1024 && i < units.length - 1) {
    s /= 1024;
    i++;
  }
  return `${s.toFixed(1)} ${units[i]}`;
}
</script>
