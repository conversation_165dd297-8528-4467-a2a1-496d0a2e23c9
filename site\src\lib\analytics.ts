/**
 * Analytics utility for tracking Pro feature usage
 * Stores events in localStorage and optionally syncs to backend
 */

/// <reference types="vite/client" />

interface AnalyticsEvent {
  event: string
  timestamp: number
  userId?: string
  metadata?: Record<string, any>
}

const STORAGE_KEY = 'parttec_analytics'
const MAX_EVENTS = 100 // Максимум событий в localStorage

/**
 * Track an analytics event
 */
export function trackEvent(
  event: string,
  metadata?: Record<string, any>
): void {
  try {
    // Создаем событие
    const analyticsEvent: AnalyticsEvent = {
      event,
      timestamp: Date.now(),
      metadata,
    }

    // Получаем существующие события
    const stored = localStorage.getItem(STORAGE_KEY)
    const events: AnalyticsEvent[] = stored ? JSON.parse(stored) : []

    // Добавляем новое событие
    events.push(analyticsEvent)

    // Ограничиваем размер массива
    if (events.length > MAX_EVENTS) {
      events.shift()
    }

    // Сохраняем обратно
    localStorage.setItem(STORAGE_KEY, JSON.stringify(events))

    // Логируем в dev режиме
    if (import.meta.env.DEV) {
      console.log('[Analytics]', event, metadata)
    }

    // TODO: Опционально отправить на backend
    // syncToBackend(analyticsEvent)
  } catch (error) {
    console.error('Failed to track event:', error)
  }
}

/**
 * Get all stored events
 */
export function getEvents(): AnalyticsEvent[] {
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    return stored ? JSON.parse(stored) : []
  } catch (error) {
    console.error('Failed to get events:', error)
    return []
  }
}

/**
 * Clear all stored events
 */
export function clearEvents(): void {
  try {
    localStorage.removeItem(STORAGE_KEY)
  } catch (error) {
    console.error('Failed to clear events:', error)
  }
}

/**
 * Pro feature specific tracking functions
 */
export const analytics = {
  // Part page viewed
  partPageViewed: (partId: number, partName: string) => {
    trackEvent('part_page_viewed', { partId, partName })
  },

  // Synonym popover opened
  synonymPopoverOpened: (attributeName: string, partId: number) => {
    trackEvent('synonym_popover_opened', { attributeName, partId })
  },

  // DataTable sorted
  dataTableSorted: (column: string, direction: 'asc' | 'desc', partId: number) => {
    trackEvent('data_table_sorted', { column, direction, partId })
  },

  // DataTable row selected
  dataTableRowSelected: (catalogItemId: number, partId: number) => {
    trackEvent('data_table_row_selected', { catalogItemId, partId })
  },

  // Equipment section viewed
  equipmentSectionViewed: (partId: number, equipmentCount: number) => {
    trackEvent('equipment_section_viewed', { partId, equipmentCount })
  },

  // Media gallery opened
  mediaGalleryOpened: (fileName: string, contextId: number | string) => {
    trackEvent('media_gallery_opened', { fileName, contextId })
  },

  // Data export initiated
  dataExportInitiated: (format: 'csv' | 'excel', partId: number, rowCount: number) => {
    trackEvent('data_export_initiated', { format, partId, rowCount })
  },

  // Request implementations button clicked
  requestImplementationsClicked: (partId: number) => {
    trackEvent('request_implementations_clicked', { partId })
  },
  // Part description viewed
  partDescriptionViewed: (partId: number) => {
    trackEvent('part_description_viewed', { partId })
  },

  // Equipment model card clicked
  equipmentModelClicked: (modelId: string | number, partId: number) => {
    trackEvent('equipment_model_clicked', { modelId, partId })
  },

  // Catalog item details button clicked
  catalogItemDetailsClicked: (catalogItemId: number, partId: number) => {
    trackEvent('catalog_item_details_clicked', { catalogItemId, partId })
  },


  // Statistics tab viewed
  statisticsTabViewed: (tabName: string, partId: number) => {
    trackEvent('statistics_tab_viewed', { tabName, partId })
  },

  // Statistics chart interacted
  statisticsChartInteracted: (chartType: string, partId: number) => {
    trackEvent('statistics_chart_interacted', { chartType, partId })
  },

  // Statistics exported
  statisticsExported: (format: 'csv' | 'json', partId: number, dataType: string) => {
    trackEvent('statistics_exported', { format, partId, dataType })
  },

  // Timeline grouping changed
  statisticsTimelineGroupingChanged: (groupBy: 'day' | 'week' | 'month', partId: number) => {
    trackEvent('statistics_timeline_grouping_changed', { groupBy, partId })
  },

  // Applicability map layout changed
  statisticsApplicabilityLayoutChanged: (layout: string, partId: number) => {
    trackEvent('statistics_applicability_layout_changed', { layout, partId })
  },

  // CatalogItem comparison viewed
  catalogItemComparisonViewed: (itemId: number, partId: number, matchScore: number) => {
    trackEvent('catalog_item_comparison_viewed', { itemId, partId, matchScore })
  },

  // Alternative brand clicked
  alternativeBrandClicked: (fromItemId: number, toItemId: number, brandName: string) => {
    trackEvent('alternative_brand_clicked', { fromItemId, toItemId, brandName })
  },

  // Similar item clicked
  similarItemClicked: (fromItemId: number, toItemId: number, similarityScore: number) => {
    trackEvent('similar_item_clicked', { fromItemId, toItemId, similarityScore })
  },

  // Equipment applicability viewed
  equipmentApplicabilityViewed: (itemId: number, equipmentCount: number) => {
    trackEvent('equipment_applicability_viewed', { itemId, equipmentCount })
  },

  // Catalog item exported
  catalogItemExported: (itemId: number, format: 'csv' | 'json', includeOptions: string[]) => {
    trackEvent('catalog_item_exported', { itemId, format, includeOptions })
  },

  // Tab changed on catalog item page
  catalogItemTabChanged: (itemId: number, tabName: string) => {
    trackEvent('catalog_item_tab_changed', { itemId, tabName })
  },

  // Tab swipe navigation
  tabSwipeNavigated: (contextId: number, direction: 'left' | 'right', newTab: string) => {
    trackEvent('tab_swipe_navigated', { contextId, direction, newTab })
  },

  // Saved search applied
  savedSearchApplied: (query: string) => {
    trackEvent('saved_search_applied', { query })
  },

  // Search history used
  searchHistoryUsed: (query: string) => {
    trackEvent('search_history_used', { query })
  },

  // Bulk selection started
  bulkSelectionStarted: (count: number, itemType: 'part' | 'catalogItem') => {
    trackEvent('bulk_selection_started', { count, itemType })
  },

  // Bulk export triggered
  bulkExportTriggered: (format: 'csv' | 'excel', count: number, itemType: 'part' | 'catalogItem') => {
    trackEvent('bulk_export_triggered', { format, count, itemType })
  },

  // Bulk comparison triggered
  bulkComparisonTriggered: (count: number, itemType: 'part' | 'catalogItem') => {
    trackEvent('bulk_comparison_triggered', { count, itemType })
  },

  // View mode changed
  viewModeChanged: (mode: 'detailed' | 'grid' | 'table') => {
    trackEvent('view_mode_changed', { mode })
  },

  // Search insights viewed
  searchInsightsViewed: (resultCount: number, isPro: boolean) => {
    trackEvent('search_insights_viewed', { resultCount, isPro })
  },

  // Autocomplete suggestion selected
  autocompleteSelected: (suggestionType: 'recent' | 'brand' | 'category', value: string) => {
    trackEvent('autocomplete_selected', { suggestionType, value })
  },

  // Saved search created
  savedSearchCreated: (name: string, hasFilters: boolean) => {
    trackEvent('saved_search_created', { name, hasFilters })
  },

  // Saved search deleted
  savedSearchDeleted: (name: string) => {
    trackEvent('saved_search_deleted', { name })
  },

  // Search performed
  searchPerformed: (query: string, resultCount: number, mode: 'text' | 'image' | 'equipment', duration: number) => {
    trackEvent('search_performed', { query, resultCount, mode, duration })
  },

  // Search filter applied
  searchFilterApplied: (filterType: string, value: string | number | boolean) => {
    trackEvent('search_filter_applied', { filterType, value })
  },

  // Search result clicked
  searchResultClicked: (resultType: 'part' | 'category' | 'brand', resultId: number, position: number, relevanceScore: number) => {
    trackEvent('search_result_clicked', { resultType, resultId, position, relevanceScore })
  },

  // Search mode changed
  searchModeChanged: (mode: 'text' | 'image' | 'equipment') => {
    trackEvent('search_mode_changed', { mode })
  },

  // Search sorted
  searchSorted: (sortBy: 'relevance' | 'name' | 'date') => {
    trackEvent('search_sorted', { sortBy })
  },

  // Search view mode changed
  searchViewModeChanged: (viewMode: 'grouped' | 'unified') => {
    trackEvent('search_view_mode_changed', { viewMode })
  },

  // Typo suggestion clicked
  typoSuggestionClicked: (originalQuery: string, suggestion: string) => {
    trackEvent('typo_suggestion_clicked', { originalQuery, suggestion })
  },

  // Image search performed
  imageSearchPerformed: (fileSize: number, fileType: string, resultCount: number) => {
    trackEvent('image_search_performed', { fileSize, fileType, resultCount })
  },

  // Equipment search performed
  equipmentSearchPerformed: (equipmentId: number, equipmentName: string, resultCount: number) => {
    trackEvent('equipment_search_performed', { equipmentId, equipmentName, resultCount })
  },

  // Search history item clicked
  searchHistoryItemClicked: (query: string, position: number) => {
    trackEvent('search_history_item_clicked', { query, position })
  },

  // Search load more
  searchLoadMore: (page: number, totalResults: number) => {
    trackEvent('search_load_more', { page, totalResults })
  },

  // Performance metrics
  performanceMetricRecorded: (metricName: string, value: number, context?: string) => {
    trackEvent('performance_metric_recorded', { metricName, value, context })
  },

  // Component render time
  componentRenderTimeRecorded: (componentName: string, renderTime: number) => {
    trackEvent('component_render_time', { componentName, renderTime })
  },

  // Data load time
  dataLoadTimeRecorded: (queryKey: string, loadTime: number, isSlowQuery: boolean) => {
    trackEvent('data_load_time', { queryKey, loadTime, isSlowQuery })
  },

  // Virtualization enabled
  virtualizationEnabled: (componentName: string, itemCount: number) => {
    trackEvent('virtualization_enabled', { componentName, itemCount })
  },

  // Prefetch triggered
  prefetchTriggered: (resourceType: string, resourceId: number | string) => {
    trackEvent('prefetch_triggered', { resourceType, resourceId })
  },

  // Code split component loaded
  codeSplitComponentLoaded: (componentName: string, loadTime: number) => {
    trackEvent('code_split_component_loaded', { componentName, loadTime })
  },

  // Long task detected
  longTaskDetected: (duration: number, startTime: number) => {
    trackEvent('long_task_detected', { duration, startTime })
  },
}

// Mobile-specific analytics
export const mobileAnalytics = {
  // Pull to refresh triggered
  pullToRefreshTriggered: (page: string, resultCount: number) => {
    trackEvent('pull_to_refresh_triggered', { page, resultCount })
  },

  // FAB action clicked
  fabActionClicked: (action: string, page: string) => {
    trackEvent('fab_action_clicked', { action, page })
  },

  // Bottom sheet opened
  bottomSheetOpened: (title: string, page: string) => {
    trackEvent('bottom_sheet_opened', { title, page })
  },

  // Tab swipe navigation
  tabSwipeNavigated: (contextId: number, direction: 'left' | 'right', newTab: string) => {
    trackEvent('tab_swipe_navigated', { contextId, direction, newTab })
  },

  // Mobile view mode changed
  mobileViewModeChanged: (mode: 'detailed' | 'grid', page: string) => {
    trackEvent('mobile_view_mode_changed', { mode, page })
  },

  // Responsive table switched to cards
  responsiveTableSwitched: (partId: number, itemCount: number) => {
    trackEvent('responsive_table_switched', { partId, itemCount })
  },

  // Haptic feedback triggered
  hapticFeedbackTriggered: (action: string, duration: number) => {
    trackEvent('haptic_feedback_triggered', { action, duration })
  },

  // Mobile filter drawer opened
  mobileFilterDrawerOpened: (activeFiltersCount: number) => {
    trackEvent('mobile_filter_drawer_opened', { activeFiltersCount })
  },

  // Scroll to top clicked
  scrollToTopClicked: (page: string, scrollPosition: number) => {
    trackEvent('scroll_to_top_clicked', { page, scrollPosition })
  },
}

// Merge all analytics objects
// Object.assign(analytics, mobileAnalytics); // Merging is problematic for TS inference, so we dupe for now