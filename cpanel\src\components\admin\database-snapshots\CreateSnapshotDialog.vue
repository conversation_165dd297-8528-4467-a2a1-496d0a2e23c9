<template>
  <VDialog header="Create Snapshot" :visible="visible" :modal="true" @update:visible="$emit('update:visible', $event)">
    <div class="space-y-4">
      <div class="space-y-1">
        <label for="name" class="text-sm font-medium">Name</label>
        <VInputText id="name" v-model="form.name" />
      </div>
      <div class="space-y-1">
        <label for="description" class="text-sm font-medium">Description</label>
        <VTextarea id="description" v-model="form.description" rows="3" />
      </div>
      <div class="space-y-1">
        <label for="type" class="text-sm font-medium">Type</label>
        <VSelectButton v-model="form.type" :options="snapshotTypes" optionLabel="label" optionValue="value" />
      </div>
      <div v-if="form.type === 'SELECTIVE_TABLES'" class="space-y-1">
        <label for="tables" class="text-sm font-medium">Tables</label>
        <VMultiSelect id="tables" v-model="form.tablesIncluded" :options="availableTables" placeholder="Select Tables" />
      </div>
    </div>
    <template #footer>
      <div class="flex w-full justify-end gap-2">
        <VButton text @click="$emit('update:visible', false)">
          <template #default>
            <span>Cancel</span>
          </template>
        </VButton>
        <VButton :loading="isCreating" @click="createSnapshot">
          <template #default>
            <span>Create</span>
          </template>
        </VButton>
      </div>
    </template>
  </VDialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineEmits, defineProps } from 'vue'
import VDialog from '@/volt/Dialog.vue'
import VInputText from '@/volt/InputText.vue'
import VTextarea from '@/volt/Textarea.vue'
import VSelectButton from '@/volt/SelectButton.vue'
import VMultiSelect from '@/volt/MultiSelect.vue'
import VButton from '@/volt/Button.vue'
import { useTrpc } from '@/composables/useTrpc'
import { useToast } from '@/composables/useToast'
import { SnapshotTypeSchema } from '@api-types/enums/SnapshotType.schema'

const props = defineProps<{ visible: boolean }>()
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'created'): void
}>()

const { client } = useTrpc()
const toast = useToast()

const isCreating = ref(false)
const form = reactive({
  name: '',
  description: '',
  type: 'FULL_DATABASE',
  tablesIncluded: [],
})

const snapshotTypes = SnapshotTypeSchema.options.map((value: string) => ({
  label: value.replace(/_/g, ' ').replace(/\w\S*/g, (txt: string) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()),
  value,
}))

const availableTables = ref<string[]>([])

const fetchAvailableTables = async () => {
  try {
    availableTables.value = await client.snapshots.getTablesList.query()
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Unknown error'
    toast.error('Error', `Failed to fetch tables: ${message}`)
  }
}

const createSnapshot = async () => {
  // const result = CreateDatabaseSnapshotSchema.safeParse(form)
  // if (!result.success) {
  //   toast.error('Validation Error', result.error.issues.map(issue => issue.message).join(', '))
  //   return
  // }

  isCreating.value = true
  try {
    await client.snapshots.create.mutate(form)
    toast.success('Success', 'Snapshot creation initiated')
    emit('created')
    emit('update:visible', false)
    form.name = ''
    form.description = ''
    form.type = 'FULL_DATABASE'
    form.tablesIncluded = []
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Unknown error'
    toast.error('Error', `Failed to create snapshot: ${message}`)
  } finally {
    isCreating.value = false
  }
}

onMounted(fetchAvailableTables)
</script>