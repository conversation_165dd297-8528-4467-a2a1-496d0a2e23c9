import { useState } from 'react'
import { Bookmark, Clock, Trash2, Plus, MoreVertical } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  loadSavedSearches, 
  saveSearch, 
  deleteSavedSearch,
  getSearchHistory,
  clearSearchHistory,
  type SavedSearch 
} from '@/lib/saved-searches'
import { useCatalogGlobalState } from '@/lib/catalog-state'
import { formatRelativeTime } from '@/lib/formatters'
import { analytics } from '@/lib/analytics'

export interface SavedSearchesDropdownProps {
  onApply: (search: SavedSearch) => void
  className?: string
}

export function SavedSearchesDropdown({ 
  onApply, 
  className = '' 
}: SavedSearchesDropdownProps) {
  const { filters } = useCatalogGlobalState()
  
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>(() => loadSavedSearches())
  const [searchHistory, setSearchHistory] = useState(() => getSearchHistory())
  
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [searchToDelete, setSearchToDelete] = useState<SavedSearch | null>(null)
  const [newSearchName, setNewSearchName] = useState('')
  const [nameError, setNameError] = useState('')
  
  const refreshData = () => {
    setSavedSearches(loadSavedSearches())
    setSearchHistory(getSearchHistory())
  }
  
  const handleSaveSearch = () => {
    if (!newSearchName.trim()) {
      setNameError('Введите название поиска')
      return
    }
    
    try {
      saveSearch(newSearchName.trim(), filters.query, filters)
      analytics.savedSearchCreated(newSearchName.trim(), Object.keys(filters).length > 1)
      
      setIsSaveDialogOpen(false)
      setNewSearchName('')
      setNameError('')
      refreshData()
      
      // Показать уведомление
      alert(`Поиск "${newSearchName.trim()}" сохранен`)
    } catch (error) {
      if (error instanceof Error) {
        setNameError(error.message)
      }
    }
  }
  
  const handleDeleteSearch = () => {
    if (!searchToDelete) return
    
    try {
      deleteSavedSearch(searchToDelete.id)
      analytics.savedSearchDeleted(searchToDelete.name)
      
      setIsDeleteDialogOpen(false)
      setSearchToDelete(null)
      refreshData()
      
      alert(`Поиск "${searchToDelete.name}" удален`)
    } catch (error) {
      console.error('Failed to delete search:', error)
      alert('Ошибка при удалении поиска')
    }
  }
  
  const handleApplySearch = (search: SavedSearch) => {
    onApply(search)
    setIsDropdownOpen(false)
  }
  
  const handleApplyHistoryItem = (query: string) => {
    analytics.searchHistoryUsed(query)
    onApply({
      id: '',
      name: '',
      query,
      filters: { ...filters, query },
      createdAt: new Date(),
      lastUsedAt: new Date()
    })
    setIsDropdownOpen(false)
  }
  
  const handleClearHistory = () => {
    clearSearchHistory()
    setSearchHistory([])
    alert('История поисков очищена')
  }
  
  const handleOpenSaveDialog = () => {
    // Автозаполнение имени
    const autoName = filters.query.trim() || `Поиск ${new Date().toLocaleDateString('ru-RU')}`
    setNewSearchName(autoName)
    setNameError('')
    setIsSaveDialogOpen(true)
    setIsDropdownOpen(false)
  }
  
  return (
    <>
      <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className={className}>
            <Bookmark className="h-4 w-4 mr-1.5" />
            Сохраненные поиски
            {savedSearches.length > 0 && (
              <Badge variant="secondary" className="ml-2 text-[10px] px-1.5 py-0">
                {savedSearches.length}
              </Badge>
            )}
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent align="end" className="w-80">
          {/* Сохраненные поиски */}
          {savedSearches.length > 0 && (
            <>
              <DropdownMenuLabel>Сохраненные поиски</DropdownMenuLabel>
              
              {savedSearches.map((search) => (
                <DropdownMenuItem
                  key={search.id}
                  className="flex items-start justify-between gap-2 py-2 cursor-pointer"
                  onSelect={(e) => {
                    e.preventDefault()
                  }}
                >
                  <div 
                    className="flex-1 min-w-0"
                    onClick={() => handleApplySearch(search)}
                  >
                    <div className="font-medium text-sm truncate">
                      {search.name}
                    </div>
                    <div className="text-xs text-muted-foreground truncate">
                      {search.query || '(без запроса)'}
                    </div>
                    <div className="text-xs text-muted-foreground mt-0.5">
                      {formatRelativeTime(search.lastUsedAt)}
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-destructive/10 hover:text-destructive"
                    onClick={(e) => {
                      e.stopPropagation()
                      setSearchToDelete(search)
                      setIsDeleteDialogOpen(true)
                    }}
                  >
                    <Trash2 className="h-3.5 w-3.5" />
                  </Button>
                </DropdownMenuItem>
              ))}
              
              <DropdownMenuSeparator />
            </>
          )}
          
          {/* Недавние поиски */}
          {searchHistory.length > 0 && (
            <>
              <DropdownMenuLabel className="flex items-center justify-between">
                <span>Недавние</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-xs"
                  onClick={handleClearHistory}
                >
                  Очистить
                </Button>
              </DropdownMenuLabel>
              
              {searchHistory.slice(0, 5).map((item, index) => (
                <DropdownMenuItem
                  key={index}
                  className="flex items-center gap-2 cursor-pointer"
                  onClick={() => handleApplyHistoryItem(item.query)}
                >
                  <Clock className="h-3.5 w-3.5 text-muted-foreground" />
                  <div className="flex-1 min-w-0">
                    <div className="text-sm truncate">{item.query}</div>
                    <div className="text-xs text-muted-foreground">
                      {formatRelativeTime(item.timestamp)}
                    </div>
                  </div>
                </DropdownMenuItem>
              ))}
              
              <DropdownMenuSeparator />
            </>
          )}
          
          {/* Пустое состояние */}
          {savedSearches.length === 0 && searchHistory.length === 0 && (
            <div className="py-6 text-center">
              <Bookmark className="h-8 w-8 mx-auto text-muted-foreground/50 mb-2" />
              <p className="text-sm text-muted-foreground mb-1">
                Нет сохраненных поисков
              </p>
              <p className="text-xs text-muted-foreground">
                Сохраните текущий поиск для быстрого доступа
              </p>
            </div>
          )}
          
          {/* Действия */}
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={handleOpenSaveDialog}
          >
            <Plus className="h-4 w-4 mr-2" />
            Сохранить текущий поиск
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      
      {/* Диалог сохранения поиска */}
      <Dialog open={isSaveDialogOpen} onOpenChange={setIsSaveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Сохранить поиск</DialogTitle>
            <DialogDescription>
              Дайте название текущему поиску для быстрого доступа в будущем
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="search-name">Название</Label>
              <Input
                id="search-name"
                value={newSearchName}
                onChange={(e) => {
                  setNewSearchName(e.target.value)
                  setNameError('')
                }}
                placeholder="Например: Подшипники SKF"
                maxLength={50}
                autoFocus
              />
              {nameError && (
                <p className="text-sm text-destructive">{nameError}</p>
              )}
            </div>
            
            <div className="text-xs text-muted-foreground space-y-1">
              <p><strong>Запрос:</strong> {filters.query || '(не указан)'}</p>
              {filters.categoryIds && filters.categoryIds.length > 0 && (
                <p><strong>Категории:</strong> {filters.categoryIds.length} выбрано</p>
              )}
              {filters.brandIds && filters.brandIds.length > 0 && (
                <p><strong>Бренды:</strong> {filters.brandIds.length} выбрано</p>
              )}
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSaveDialogOpen(false)}>
              Отмена
            </Button>
            <Button onClick={handleSaveSearch}>
              Сохранить
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Диалог подтверждения удаления */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Удалить сохраненный поиск?</AlertDialogTitle>
            <AlertDialogDescription>
              Удалить поиск "{searchToDelete?.name}"? Это действие нельзя отменить.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Отмена</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteSearch}>
              Удалить
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

