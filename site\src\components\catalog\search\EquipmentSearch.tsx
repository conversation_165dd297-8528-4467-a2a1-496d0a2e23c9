import { useState, useEffect, useMemo } from 'react'
import { Search, X, Building2, Hash, Package } from 'lucide-react'
import { motion } from 'framer-motion'
import {
  Combobox,
  ComboboxInput,
  ComboboxContent,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxSeparator
} from '@/components/ui/combobox'
import LoadingState from '@/components/shared/LoadingState'
import { ModernCard } from '@/components/ui/modern-card'
import { trpc } from '@/lib/trpc'
import { cn } from '@/lib/utils'
import { fadeInUp } from '@/lib/animation-variants'

export interface EquipmentSearchProps {
  onEquipmentSelect: (equipmentId: number, equipmentName: string) => void
  className?: string
}

const RECENT_EQUIPMENT_KEY = 'search-recent-equipment'
const MAX_RECENT = 5

interface RecentEquipment {
  id: number
  name: string
  brandName?: string
  timestamp: Date
}

export default function EquipmentSearch({
  onEquipmentSelect,
  className
}: EquipmentSearchProps) {
  const [query, setQuery] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [selectedEquipment, setSelectedEquipment] = useState<{ id: number; name: string } | null>(null)
  const [recentEquipment, setRecentEquipment] = useState<RecentEquipment[]>([])

  // Load recent equipment from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(RECENT_EQUIPMENT_KEY)
        if (stored) {
          const parsed = JSON.parse(stored)
          setRecentEquipment(
            parsed.map((item: RecentEquipment) => ({
              ...item,
              timestamp: new Date(item.timestamp)
            }))
          )
        }
      } catch {
        // Ignore errors
      }
    }
  }, [])

  // Query equipment models
  const { data: equipment, isLoading } = trpc.crud.equipmentModel.findMany.useQuery(
    {
      where: query
        ? {
            OR: [
              { name: { contains: query, mode: 'insensitive' } },
              { modelNumber: { contains: query, mode: 'insensitive' } },
              { brand: { name: { contains: query, mode: 'insensitive' } } }
            ]
          }
        : undefined,
      include: {
        brand: true,
        _count: {
          select: { parts: true }
        }
      },
      take: 10,
      orderBy: { name: 'asc' }
    },
    { enabled: query.length > 0 }
  )

  // Group equipment by brand
  const groupedEquipment = useMemo(() => {
    if (!equipment) return new Map()

    const groups = new Map<string, typeof equipment>()
    
    equipment.forEach(item => {
      const brandName = item.brand?.name || 'Без бренда'
      const existing = groups.get(brandName) || []
      groups.set(brandName, [...existing, item])
    })

    return groups
  }, [equipment])

  const addToRecent = (id: number, name: string, brandName?: string) => {
    const newItem: RecentEquipment = {
      id,
      name,
      brandName,
      timestamp: new Date()
    }

    const updated = [
      newItem,
      ...recentEquipment.filter(item => item.id !== id)
    ].slice(0, MAX_RECENT)

    setRecentEquipment(updated)

    if (typeof window !== 'undefined') {
      localStorage.setItem(RECENT_EQUIPMENT_KEY, JSON.stringify(updated))
    }
  }

  const handleSelect = (id: number, name: string, brandName?: string) => {
    setSelectedEquipment({ id, name })
    addToRecent(id, name, brandName)
    onEquipmentSelect(id, name)
    setIsOpen(false)
  }

  const handleClearSelection = () => {
    setSelectedEquipment(null)
    setQuery('')
  }

  const clearRecent = () => {
    setRecentEquipment([])
    if (typeof window !== 'undefined') {
      localStorage.removeItem(RECENT_EQUIPMENT_KEY)
    }
  }

  return (
    <div className={cn('w-full space-y-4', className)}>
      {selectedEquipment ? (
        <motion.div
          variants={fadeInUp}
          initial="initial"
          animate="animate"
        >
          <ModernCard>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                  <Package className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Выбранная техника</p>
                  <p className="font-semibold">{selectedEquipment.name}</p>
                </div>
              </div>
              <button
                onClick={handleClearSelection}
                className="p-2 hover:bg-muted rounded-lg transition-colors"
                aria-label="Сбросить выбор"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </ModernCard>
        </motion.div>
      ) : (
        <Combobox open={isOpen} onOpenChange={setIsOpen}>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground pointer-events-none" />
            <ComboboxInput
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Поиск по модели техники (например, CAT 320D)"
              className="w-full h-12 pl-10 pr-4 text-base border-2 focus:border-primary transition-colors"
            />
          </div>

          <ComboboxContent>
            {isLoading ? (
              <div className="p-4">
                <LoadingState variant="list" />
              </div>
            ) : equipment && equipment.length > 0 ? (
              <>
                {Array.from(groupedEquipment.entries()).map(([brandName, items], groupIndex) => (
                  <div key={brandName}>
                    {groupIndex > 0 && <ComboboxSeparator />}
                    <ComboboxGroup heading={brandName}>
                      {items.map((item) => (
                        <ComboboxItem
                          key={item.id}
                          value={item.name}
                          onSelect={() => handleSelect(item.id, item.name, item.brand?.name)}
                          className="flex items-start gap-3 py-3"
                        >
                          <div className="w-10 h-10 rounded bg-muted flex items-center justify-center flex-shrink-0">
                            <Building2 className="w-5 h-5 text-muted-foreground" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="font-medium truncate">{item.name}</p>
                            {item.modelNumber && (
                              <p className="text-sm text-muted-foreground flex items-center gap-1 mt-1">
                                <Hash className="w-3 h-3" />
                                {item.modelNumber}
                              </p>
                            )}
                            <p className="text-xs text-muted-foreground mt-1">
                              <Package className="w-3 h-3 inline mr-1" />
                              {item._count.parts} {item._count.parts === 1 ? 'запчасть' : 'запчастей'} доступно
                            </p>
                          </div>
                        </ComboboxItem>
                      ))}
                    </ComboboxGroup>
                  </div>
                ))}
              </>
            ) : query.length > 0 ? (
              <ComboboxEmpty>
                <div className="py-8 text-center">
                  <p className="text-muted-foreground mb-2">Техника не найдена</p>
                  <p className="text-sm text-muted-foreground">
                    Попробуйте изменить запрос
                  </p>
                </div>
              </ComboboxEmpty>
            ) : recentEquipment.length > 0 ? (
              <>
                <ComboboxGroup heading="Недавно выбранные">
                  {recentEquipment.map((item) => (
                    <ComboboxItem
                      key={item.id}
                      value={item.name}
                      onSelect={() => handleSelect(item.id, item.name, item.brandName)}
                      className="flex items-center justify-between group"
                    >
                      <div className="flex items-center gap-2">
                        <Package className="w-4 h-4 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{item.name}</p>
                          {item.brandName && (
                            <p className="text-xs text-muted-foreground">{item.brandName}</p>
                          )}
                        </div>
                      </div>
                    </ComboboxItem>
                  ))}
                </ComboboxGroup>
                <ComboboxSeparator />
                <button
                  onClick={clearRecent}
                  className="w-full px-3 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-muted transition-colors text-left"
                >
                  Очистить историю
                </button>
              </>
            ) : (
              <ComboboxEmpty>
                <div className="py-8 text-center">
                  <p className="text-muted-foreground">
                    Начните вводить название или модель техники
                  </p>
                </div>
              </ComboboxEmpty>
            )}
          </ComboboxContent>
        </Combobox>
      )}
    </div>
  )
}

