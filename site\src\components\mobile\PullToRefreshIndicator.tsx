import React from 'react';
import { motion } from 'framer-motion';
import { ArrowDown, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface PullToRefreshIndicatorProps {
  pullDistance: number;
  threshold: number;
  isRefreshing: boolean;
  className?: string;
}

export function PullToRefreshIndicator({
  pullDistance,
  threshold,
  isRefreshing,
  className,
}: PullToRefreshIndicatorProps) {
  const pullProgress = Math.min(pullDistance / threshold, 1);
  const rotation = pullProgress * 180;
  const isReady = pullDistance >= threshold;

  const text = isRefreshing
    ? "Обновление..."
    : isReady
    ? "Отпустите для обновления"
    : "Потяните для обновления";

  return (
    <motion.div
      className={cn(
        'absolute top-0 left-0 right-0 flex items-center justify-center pt-4 pb-2 text-sm text-muted-foreground z-10',
        className
      )}
      style={{
        translateY: pullDistance > 0 ? 0 : '-100%',
        opacity: pullDistance > 0 ? 1 : 0,
      }}
      initial={{ y: '-100%' }}
      animate={{ y: 0 }}
      transition={{ duration: 0.2 }}
    >
      <div className="flex items-center gap-2 p-2 rounded-full bg-background/80 backdrop-blur-sm border border-border">
        {isRefreshing ? (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          >
            <RefreshCw className="h-4 w-4" />
          </motion.div>
        ) : (
          <motion.div style={{ rotate }}>
            <ArrowDown className="h-4 w-4" />
          </motion.div>
        )}
        <span>{text}</span>
      </div>
    </motion.div>
  );
}

export default PullToRefreshIndicator;