import type { PartListItem } from '@/types/catalog';
import { Clock } from 'lucide-react';
import { useIsMobile } from '@/hooks/useMediaQuery';
import BaseCard from './cards/BaseCard';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  createCardClickHandler, 
  createPartUrl, 
  formatCardDate, 
  formatAttributeForCard, 
  createCategoryBadge 
} from './cards/card-utils';

export interface PartCardProps {
  part: PartListItem;
  onClick?: () => void;
  animationDelay?: number;
  showCheckbox?: boolean;
  isSelected?: boolean;
  onSelectionChange?: (selected: boolean) => void;
}

export function PartCard({ 
  part, 
  onClick, 
  animationDelay, 
  showCheckbox = false, 
  isSelected = false, 
  onSelectionChange 
}: PartCardProps) {
  const isMobile = useIsMobile();
  const handleClick = createCardClickHandler(createPartUrl(part.id), onClick);

  // Format attributes
  const formattedAttributes = part.attributes.map(formatAttributeForCard);
  
  // Prepare category badge
  const categoryBadge = createCategoryBadge(part.partCategory.name);

  return (
    <BaseCard
      variant="part"
      layout={isMobile ? "compact" : "detailed"}
      onClick={handleClick}
      animationDelay={animationDelay}
      className={isSelected ? 'border-primary bg-primary/5' : ''}
    >
      {showCheckbox && (
        <div className="absolute top-2 left-2 z-10">
          <Checkbox
            checked={isSelected}
            onCheckedChange={onSelectionChange}
            onClick={(e) => e.stopPropagation()}
            className="bg-background border-2"
            aria-label={`Выбрать ${part.name || 'запчасть'}`}
          />
        </div>
      )}
      
      <div className="flex items-start gap-4">
        {/* Media on the left for mobile compact view */}
        {isMobile && part.image && (
            <BaseCard.Media mediaAsset={part.image} size="md" />
        )}
        {/* Content */}
        <div className="flex-1 min-w-0 space-y-3">
          {/* Header */}
          <BaseCard.Header
            title={part.name || `Запчасть #${part.id}`}
            description={part.partCategory.name}
          />

          {/* Badges */}
          <BaseCard.Badges badges={[categoryBadge]} />

          {/* Attributes */}
          {formattedAttributes.length > 0 && (
            <BaseCard.Attributes
              attributes={formattedAttributes}
              maxVisible={isMobile ? 2 : 3}
            />
          )}

          {/* Footer */}
          <BaseCard.Footer>
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span>Обновлено:</span>
              <span>{formatCardDate(part.updatedAt)}</span>
            </div>
          </BaseCard.Footer>
        </div>

        {/* Media on the right for desktop */}
        {!isMobile && part.image && (
          <BaseCard.Media mediaAsset={part.image} size="md" />
        )}
      </div>
    </BaseCard>
  );
}
