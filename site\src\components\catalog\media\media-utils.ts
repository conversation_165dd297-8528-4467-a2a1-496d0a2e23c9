import { formatCompactNumber } from '@/lib/formatters'
import type { MediaAsset } from '@/lib/types'
import {
  FileIcon,
  FileImage,
  FileVideo,
  FileText,
  type LucideIcon,
} from 'lucide-react'

// MIME Type Detection
export function isImage(mimeType: string): boolean {
  return mimeType.startsWith('image/')
}

export function isVideo(mimeType: string): boolean {
  return mimeType.startsWith('video/')
}

export function isPDF(mimeType: string): boolean {
  return mimeType === 'application/pdf'
}

export function getMediaType(
  mimeType: string
): 'image' | 'video' | 'pdf' | 'other' {
  if (isImage(mimeType)) return 'image'
  if (isVideo(mimeType)) return 'video'
  if (isPDF(mimeType)) return 'pdf'
  return 'other'
}

export function getFileExtension(fileName: string): string {
  const parts = fileName.split('.')
  return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : ''
}

export function getMediaIcon(mimeType: string): LucideIcon {
  const type = getMediaType(mimeType)
  switch (type) {
    case 'image':
      return FileImage
    case 'video':
      return FileVideo
    case 'pdf':
      return FileText
    default:
      return FileIcon
  }
}

// Image Preloading
const imageCache = new Map<string, HTMLImageElement>()

export function preloadImage(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    if (imageCache.has(url)) {
      resolve()
      return
    }

    const img = new Image()
    img.onload = () => {
      imageCache.set(url, img)
      resolve()
    }
    img.onerror = reject
    img.src = url
  })
}

export function preloadImages(urls: string[]): Promise<void[]> {
  return Promise.all(urls.map((url) => preloadImage(url)))
}

export function createImageCache() {
  const cache = new Map<string, HTMLImageElement>()
  const maxSize = 50

  return {
    get(url: string): HTMLImageElement | undefined {
      return cache.get(url)
    },
    set(url: string, img: HTMLImageElement): void {
      if (cache.size >= maxSize) {
        const firstKey = cache.keys().next().value
        if (firstKey) cache.delete(firstKey)
      }
      cache.set(url, img)
    },
    has(url: string): boolean {
      return cache.has(url)
    },
    clear(): void {
      cache.clear()
    },
  }
}

// Thumbnail Generation
export function generateThumbnailUrl(
  url: string,
  size: 'sm' | 'md' | 'lg',
  density: 1 | 2 | 3 = 1
): string {
  // If backend supports thumbnail generation, construct URL here
  // Example: Cloudinary, Imgix, etc.
  // For now, we assume no backend resizing and return original URL.
  // In a real implementation, you would append query params like ?w=400&dpr=2
  const widths = { sm: 400, md: 800, lg: 1200 };
  const targetWidth = widths[size] * density;
  // This is a placeholder. Replace with your actual image resizing logic.
  if (url.includes('cloudinary')) {
    return url.replace('/upload/', `/upload/w_${targetWidth},q_auto,f_auto/`);
  }
  return url;
}

export function generateSrcSet(url: string): string {
  const sizes = [400, 800, 1200, 1600];
  return sizes.map(size => `${generateThumbnailUrl(url, 'sm')} ${size}w`).join(', ');
}

export function generateSizesAttribute(columns: number = 4): string {
    if (columns === 2) return `(max-width: 640px) 50vw, 33vw`;
    if (columns === 3) return `(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw`;
    return `(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 20vw`;
}

export function getOptimalImageSize(
  containerWidth: number,
  devicePixelRatio: number
): number {
  return Math.ceil(containerWidth * devicePixelRatio)
}

// File Size Formatting
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Б'
  if (bytes < 1024) return `${bytes} Б`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} КБ`
  if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} МБ`
  return `${formatCompactNumber(bytes / (1024 * 1024 * 1024))} ГБ`
}

// Keyboard Navigation
interface KeyboardHandlers {
  onNext?: () => void
  onPrev?: () => void
  onClose?: () => void
  onZoomIn?: () => void
  onZoomOut?: () => void
}

export function createKeyboardHandler(
  handlers: KeyboardHandlers
): (e: KeyboardEvent) => void {
  return (e: KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowRight':
        e.preventDefault()
        handlers.onNext?.()
        break
      case 'ArrowLeft':
        e.preventDefault()
        handlers.onPrev?.()
        break
      case 'Escape':
        e.preventDefault()
        handlers.onClose?.()
        break
      case '+':
      case '=':
        e.preventDefault()
        handlers.onZoomIn?.()
        break
      case '-':
      case '_':
        e.preventDefault()
        handlers.onZoomOut?.()
        break
      case ' ':
        e.preventDefault()
        handlers.onNext?.()
        break
    }
  }
}

// Touch Gesture Detection
export function detectSwipe(
  startX: number,
  startY: number,
  endX: number,
  endY: number,
  threshold = 50
): 'left' | 'right' | 'up' | 'down' | null {
  const deltaX = endX - startX
  const deltaY = endY - startY
  const absDeltaX = Math.abs(deltaX)
  const absDeltaY = Math.abs(deltaY)

  if (Math.max(absDeltaX, absDeltaY) < threshold) {
    return null
  }

  if (absDeltaX > absDeltaY) {
    return deltaX > 0 ? 'right' : 'left'
  } else {
    return deltaY > 0 ? 'down' : 'up'
  }
}

interface PinchResult {
  scale: number
  center: { x: number; y: number }
}

export function detectPinch(touches: TouchList): PinchResult | null {
  if (touches.length !== 2) return null

  const touch1 = touches[0]
  const touch2 = touches[1]

  const distance = Math.hypot(
    touch2.clientX - touch1.clientX,
    touch2.clientY - touch1.clientY
  )

  const centerX = (touch1.clientX + touch2.clientX) / 2
  const centerY = (touch1.clientY + touch2.clientY) / 2

  return {
    scale: distance,
    center: { x: centerX, y: centerY },
  }
}

// Lazy Loading
export function createLazyLoadObserver(
  callback: (entry: IntersectionObserverEntry) => void,
  options?: IntersectionObserverInit
): IntersectionObserver {
  const defaultOptions: IntersectionObserverInit = {
    rootMargin: '200px', // Более агрессивный prefetch
    threshold: 0.01,
    ...options,
  }

  return new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        callback(entry)
      }
    })
  }, defaultOptions)
}

// Progressive Loading
export function generateBlurDataURL(width: number, height: number): string {
  if (typeof document === 'undefined') return ''
  
  const canvas = document.createElement('canvas')
  canvas.width = width
  canvas.height = height
  const ctx = canvas.getContext('2d')
  if (!ctx) return ''

  const gradient = ctx.createLinearGradient(0, 0, width, height)
  gradient.addColorStop(0, '#e5e7eb')
  gradient.addColorStop(1, '#d1d5db')
  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, width, height)

  return canvas.toDataURL()
}

export function createProgressiveLoader(
  lowResUrl: string,
  highResUrl: string,
  onProgress: (stage: 'blur' | 'lowres' | 'highres') => void
): Promise<void> {
  return new Promise((resolve, reject) => {
    onProgress('blur')

    // Load low-res first
    const lowResImg = new Image()
    lowResImg.onload = () => {
      onProgress('lowres')

      // Then load high-res
      const highResImg = new Image()
      highResImg.onload = () => {
        onProgress('highres')
        resolve()
      }
      highResImg.onerror = reject
      highResImg.src = highResUrl
    }
    lowResImg.onerror = () => {
      // If low-res fails, try high-res directly
      const highResImg = new Image()
      highResImg.onload = () => {
        onProgress('highres')
        resolve()
      }
      highResImg.onerror = reject
      highResImg.src = highResUrl
    }
    lowResImg.src = lowResUrl
  })
}

// Validation
interface ValidationResult {
  isValid: boolean
  errors: string[]
}

export function validateMediaAsset(asset: MediaAsset): ValidationResult {
  const errors: string[] = []

  if (!asset.id) {
    errors.push('ID медиафайла обязателен')
  }

  if (!asset.fileName) {
    errors.push('Имя файла обязательно')
  }

  if (!asset.mimeType) {
    errors.push('MIME-тип обязателен')
  }

  if (!asset.url) {
    errors.push('URL медиафайла обязателен')
  } else {
    try {
      new URL(asset.url)
    } catch {
      errors.push('Некорректный URL медиафайла')
    }
  }

  if (asset.fileSize !== undefined && asset.fileSize < 0) {
    errors.push('Размер файла не может быть отрицательным')
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

