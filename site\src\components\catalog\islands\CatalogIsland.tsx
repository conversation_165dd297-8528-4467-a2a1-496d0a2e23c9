"use client"

import { TrpcBoundary } from "@/components/providers/TrpcBoundary"
import { useCatalogSearch } from "../pro/useCatalogSearch"
import { useCatalogMetadata } from "@/hooks/useCatalogData"
import ResultsContent from "./ResultsContent"
import { lazy, Suspense } from 'react'
import { usePrefetchCategories, usePrefetchBrands, usePrefetchAttributeTemplates } from '@/hooks/usePrefetch'
import { LoadingState } from '@/components/shared/LoadingState'

const FilterSystem = lazy(() => import('../filters/FilterSystem').then(module => ({ default: module.FilterSystem })));
const ModernFiltersPanel = lazy(() => import('../pro/ModernFiltersPanel').then(module => ({ default: module.ModernFiltersPanel })));

export default function CatalogIsland() {
  return (
    <TrpcBoundary>
      <CatalogIslandInner />
    </TrpcBoundary>
  )
}

function CatalogIslandInner() {
  const { filters, setFilters, updateFilters, clearFilters, metadata } = useCatalogSearch()
  const { categories, brands, templates } = useCatalogMetadata()

  // Prefetch часто используемых данных при загрузке каталога
  usePrefetchCategories()
  usePrefetchBrands()
  usePrefetchAttributeTemplates()

  return (
    <Suspense fallback={<LoadingState variant="page" />}>
      <FilterSystem
        filters={filters}
        setFilters={setFilters}
        updateFilters={updateFilters}
        clearFilters={clearFilters}
        metadata={metadata}
        categories={categories}
        brands={brands}
        attributeTemplates={templates}
      >
        <div className="flex flex-col md:flex-row">
          {/* Десктопная версия фильтров */}
          <div className="hidden md:block">
            <Suspense fallback={<LoadingState variant="card" count={3} />}>
              <ModernFiltersPanel />
            </Suspense>
          </div>
          {/* Мобильная версия - фильтры будут в drawer внутри ResultsContent */}
          <ResultsContent />
        </div>
      </FilterSystem>
    </Suspense>
  )
}

