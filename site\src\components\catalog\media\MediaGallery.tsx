import { useState, useEffect, useRef, useCallback } from 'react'
import { motion } from 'motion/react'
import type { MediaAsset } from '@/lib/types'
import {
  isImage,
  isVideo,
  isPDF,
  getMediaType,
  formatFileSize,
  createLazyLoadObserver,
  validateMediaAsset,
  createProgressiveLoader,
  generateBlurDataURL,
  generateThumbnailUrl,
  generateSrcSet,
  generateSizesAttribute,
} from './media-utils'
import MediaZoom from './MediaZoom'
import MediaCarousel from './MediaCarousel'
import { fadeInUp, staggerContainer, cardHover } from '@/lib/animation-variants'
import { analytics } from '@/lib/analytics'
import { LoadingState } from '@/components/shared/LoadingState'
import { EmptyState } from '@/components/shared/EmptyState'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { ImageIcon, PlayCircle, FileText } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useIsMobile } from '@/hooks/useMediaQuery'

export interface MediaGalleryProps {
  assets: MediaAsset[]
  layout?: 'grid' | 'carousel' | 'masonry'
  initialIndex?: number
  showThumbnails?: boolean
  enableLightbox?: boolean
  enableLazyLoad?: boolean
  columns?: 2 | 3 | 4 | 5
  onMediaClick?: (asset: MediaAsset, index: number) => void
  className?: string
  contextId?: number | string
  contextType?: 'part' | 'catalogItem'
}

export default function MediaGallery({
  assets,
  layout = 'grid',
  initialIndex = 0,
  showThumbnails = false,
  enableLightbox = true,
  enableLazyLoad = true,
  columns = 4,
  onMediaClick,
  className,
  contextId,
  contextType = 'part',
}: MediaGalleryProps) {
  const [showLightbox, setShowLightbox] = useState(false)
  const [showCarousel, setShowCarousel] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(initialIndex)
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [loadedImages, setLoadedImages] = useState<Set<number>>(new Set())
  const [isLoading, setIsLoading] = useState(true)
  const isMobile = useIsMobile();
  const [imageSources, setImageSources] = useState<Map<number, string>>(new Map())
  const [imageLoadingStage, setImageLoadingStage] = useState<Map<number, 'blur' | 'lowres' | 'highres'>>(new Map())
  const observerRef = useRef<IntersectionObserver | null>(null)
  const blurPlaceholderRef = useRef<string | null>(null)
  const loadingStartedRef = useRef<Set<number>>(new Set())

  // Generate blur placeholder once
  useEffect(() => {
    if (!blurPlaceholderRef.current) {
      blurPlaceholderRef.current = generateBlurDataURL(20, 20)
    }
  }, [])

  // Validate assets and reset state on change
  useEffect(() => {
    const validAssets = assets.filter((asset) => validateMediaAsset(asset).isValid)
    if (validAssets.length !== assets.length) {
      console.warn('Некоторые медиафайлы имеют некорректные данные')
    }
    
    // Reset image sources and loading stages when assets change
    setImageSources(new Map())
    setImageLoadingStage(new Map())
    setLoadedImages(new Set())
    loadingStartedRef.current = new Set()
    
    setIsLoading(false)
  }, [assets])


  // Track gallery view
  useEffect(() => {
    if (assets.length > 0 && contextId) {
      analytics.mediaGalleryViewed(contextId, assets.length)
    }
  }, [assets.length, contextId])

  // Helper to start progressive loading for an image
  const startProgressiveLoad = useCallback((index: number, asset: MediaAsset) => {
    if (!isImage(asset.mimeType)) return
    
    // Prevent duplicate loading
    if (loadingStartedRef.current.has(index)) return
    loadingStartedRef.current.add(index)
    
    // Initialize with blur placeholder
    if (blurPlaceholderRef.current) {
      setImageSources((prev) => new Map(prev).set(index, blurPlaceholderRef.current!))
      setImageLoadingStage((prev) => new Map(prev).set(index, 'blur'))
    }
    
    // Generate URLs for progressive loading
    const lowResUrl = generateThumbnailUrl(asset.url, 'sm')
    const highResUrl = asset.url
    
    // Start progressive loading with actual different URLs
    createProgressiveLoader(
      lowResUrl,
      highResUrl,
      (stage) => {
        setImageLoadingStage((prev) => new Map(prev).set(index, stage))
        
        // Update displayed src based on stage
        if (stage === 'lowres') {
          setImageSources((prev) => new Map(prev).set(index, lowResUrl))
        } else if (stage === 'highres') {
          setImageSources((prev) => new Map(prev).set(index, highResUrl))
        }
      }
    ).catch((error) => {
      console.error('Progressive loading failed:', error)
      // Fallback to high-res if loading fails
      setImageSources((prev) => new Map(prev).set(index, highResUrl))
      setImageLoadingStage((prev) => new Map(prev).set(index, 'highres'))
    })
    
    setLoadedImages((prev) => new Set(prev).add(index))
  }, [])

  // Setup lazy loading observer with true progressive loading
  useEffect(() => {
    if (!enableLazyLoad) {
      // Non-lazy mode: load all images immediately with progressive loading
      assets.forEach((asset, index) => {
        if (isImage(asset.mimeType)) {
          startProgressiveLoad(index, asset)
        }
      })
      return
    }

    observerRef.current = createLazyLoadObserver((entry) => {
      const index = Number(entry.target.getAttribute('data-index'))
      if (!Number.isNaN(index)) {
        const asset = assets[index]
        if (asset) {
          startProgressiveLoad(index, asset)
        }
      }
    })

    return () => {
      observerRef.current?.disconnect()
    }
  }, [enableLazyLoad, assets, startProgressiveLoad])

  const handleMediaClick = (asset: MediaAsset, index: number) => {
    if (onMediaClick) {
      onMediaClick(asset, index)
    }
    
    if (enableLightbox) {
      setSelectedIndex(index)
      if (isMobile) {
        setShowCarousel(true) // Open MediaCarousel
      } else {
        setShowLightbox(true) // Open MediaZoom
      }
    }
    
    // Track analytics
    if (contextId) {
      analytics.mediaGalleryOpened(asset.fileName, contextId)
    }
  }

  const getMediaTypeLabel = (mimeType: string): string => {
    const type = getMediaType(mimeType)
    switch (type) {
      case 'image':
        return 'Изображение'
      case 'video':
        return 'Видео'
      case 'pdf':
        return 'PDF'
      default:
        return 'Файл'
    }
  }

  const renderMediaItem = (asset: MediaAsset, index: number) => {
    const type = getMediaType(asset.mimeType)
    const isLoaded = !enableLazyLoad || loadedImages.has(index)
    const loadingStage = imageLoadingStage.get(index)
    const currentSrc = imageSources.get(index)

    return (
      <TooltipProvider key={asset.id}>
        <Tooltip>
          <TooltipTrigger asChild>
            <motion.div
              variants={fadeInUp}
              className={cn(
                'relative aspect-square overflow-hidden rounded-lg bg-muted cursor-pointer group',
                'border-2 border-transparent hover:border-primary transition-colors'
              )}
              whileHover="hover"
              onClick={() => handleMediaClick(asset, index)}
              data-index={index}
              ref={(el) => {
                if (el && enableLazyLoad && observerRef.current) {
                  observerRef.current.observe(el)
                }
              }}
            >
              {/* Media content */}
              {type === 'image' && (
                <>
                  {/* Progressive loading: blur → low-res → high-res */}
                  <img
                    src={currentSrc || (isLoaded ? asset.url : undefined)}
                    srcSet={isLoaded ? generateSrcSet(asset.url) : undefined}
                    sizes={generateSizesAttribute(columns)}
                    alt={asset.fileName}
                    className={cn(
                      'w-full h-full object-cover transition-all duration-500',
                      loadingStage === 'blur' && 'blur-md scale-110',
                      loadingStage === 'lowres' && 'blur-[1px]',
                      loadingStage === 'highres' && 'blur-0',
                      !loadingStage && 'blur-0',
                      'group-hover:scale-105'
                    )}
                    loading={enableLazyLoad ? 'lazy' : 'eager'}
                    decoding="async"
                    style={{
                      opacity: currentSrc || isLoaded ? 1 : 0,
                      transition: 'opacity 0.5s ease-in-out, filter 0.5s ease-in-out, transform 0.3s ease-in-out',
                    }}
                  />
                </>
              )}

              {type === 'video' && (
                <div className="relative w-full h-full flex items-center justify-center bg-black/10">
                  {isLoaded && (
                    <video
                      src={asset.url}
                      className="w-full h-full object-cover"
                      muted
                    />
                  )}
                  <div className="absolute inset-0 flex items-center justify-center bg-black/30">
                    <PlayCircle className="w-12 h-12 text-white drop-shadow-lg" />
                  </div>
                </div>
              )}

              {type === 'pdf' && (
                <div className="w-full h-full flex flex-col items-center justify-center bg-muted">
                  <FileText className="w-12 h-12 text-muted-foreground mb-2" />
                  <span className="text-xs text-muted-foreground text-center px-2 line-clamp-2">
                    {asset.fileName}
                  </span>
                </div>
              )}

              {type === 'other' && (
                <div className="w-full h-full flex items-center justify-center bg-muted">
                  <ImageIcon className="w-12 h-12 text-muted-foreground" />
                </div>
              )}

              {/* Type badge */}
              <div className="absolute top-2 right-2">
                <Badge variant="secondary" className="text-xs">
                  {getMediaTypeLabel(asset.mimeType)}
                </Badge>
              </div>

              {/* Loading overlay - only show if no src available yet */}
              {!currentSrc && !isLoaded && enableLazyLoad && type === 'image' && (
                <div className="absolute inset-0 bg-muted animate-pulse" />
              )}
            </motion.div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-xs">
              <div className="font-medium">{asset.fileName}</div>
              {asset.fileSize && (
                <div className="text-muted-foreground">
                  {formatFileSize(asset.fileSize)}
                </div>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  if (isLoading) {
    return <LoadingState variant="card" />
  }

  if (assets.length === 0) {
    return (
      <EmptyState
        icon={ImageIcon}
        title="Нет медиафайлов"
        description="Для этого элемента пока нет загруженных изображений или файлов"
      />
    )
  }

  return (
    <>
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className={cn('w-full', className)}
      >
        {layout === 'grid' && (
          <div
            className={cn(
              'grid gap-4',
              `grid-cols-${Math.min(columns || 4, 2)} sm:grid-cols-${columns || 4}`
            )}
          >
            {assets.map((asset, index) => renderMediaItem(asset, index))}
          </div>
        )}

        {layout === 'carousel' && (
          <div className="space-y-4">
            {/* Main image display */}
            <div className="relative aspect-video rounded-lg overflow-hidden bg-muted">
              {renderMediaItem(assets[selectedIndex], selectedIndex)}
            </div>

            {/* Thumbnail strip */}
            {showThumbnails && assets.length > 1 && (
              <div className="flex gap-2 overflow-x-auto snap-x snap-mandatory pb-2">
                {assets.map((asset, index) => (
                  <button
                    key={asset.id}
                    onClick={() => setSelectedIndex(index)}
                    className={cn(
                      'flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden snap-center',
                      'border-2 transition-colors',
                      index === selectedIndex
                        ? 'border-primary'
                        : 'border-transparent hover:border-muted-foreground'
                    )}
                  >
                    {isImage(asset.mimeType) ? (
                      <img
                        src={asset.url}
                        alt={asset.fileName}
                        className="w-full h-full object-cover"
                      />
                    ) : isVideo(asset.mimeType) ? (
                      <div className="w-full h-full bg-black/10 flex items-center justify-center">
                        <PlayCircle className="w-6 h-6 text-white" />
                      </div>
                    ) : (
                      <div className="w-full h-full bg-muted flex items-center justify-center">
                        <FileText className="w-6 h-6 text-muted-foreground" />
                      </div>
                    )}
                  </button>
                ))}
              </div>
            )}
          </div>
        )}

        {layout === 'masonry' && (
          <div className="columns-2 md:columns-3 lg:columns-4 gap-4 space-y-4">
            {assets.map((asset, index) => (
              <div key={asset.id} className="break-inside-avoid">
                {renderMediaItem(asset, index)}
              </div>
            ))}
          </div>
        )}
      </motion.div>

      {/* Desktop: MediaZoom with zoom/pan */}
      {!isMobile && showLightbox && (
        <MediaZoom
          assets={assets}
          initialIndex={selectedIndex}
          isOpen={showLightbox}
          onClose={() => setShowLightbox(false)}
          enableZoom={true}
          enablePan={true}
        />
      )}

      {/* Mobile: MediaCarousel with swipe */}
      {isMobile && showCarousel && (
        <MediaCarousel
          assets={assets}
          initialIndex={selectedIndex}
          isOpen={showCarousel}
          onClose={() => setShowCarousel(false)}
          showIndicators={true}
        />
      )}
    </>
  )
}

