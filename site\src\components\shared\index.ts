/**
 * Barrel export для shared компонентов
 */

export { default as LoadingState } from './LoadingState';
export type { LoadingStateProps } from './LoadingState';
export {
  SkeletonBox,
  SkeletonCircle,
  SkeletonText,
  SkeletonImage,
} from './LoadingState';

export { default as ErrorState } from './ErrorState';
export type { ErrorStateProps } from './ErrorState';

export { default as EmptyState } from './EmptyState';
export type { EmptyStateProps, EmptyStateAction } from './EmptyState';
export { Inbox, SearchX, Filter, PackageOpen } from './EmptyState';


