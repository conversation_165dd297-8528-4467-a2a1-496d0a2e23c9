import { useRef, useCallback } from 'react';
import type { RefObject } from 'react';

interface SwipeGestureOptions {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  threshold?: number;
  velocityThreshold?: number;
  preventScroll?: boolean;
}

interface TouchStart {
  x: number;
  y: number;
  timestamp: number;
}

export function useSwipeGesture({
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  threshold = 50,
  velocityThreshold = 0.3,
  preventScroll = false,
}: SwipeGestureOptions): {
  ref: RefObject<HTMLDivElement>;
} {
  const ref = useRef<HTMLDivElement>(null);
  const touchStartRef = useRef<TouchStart | null>(null);

  const handleTouchStart = useCallback((e: TouchEvent) => {
    const { clientX, clientY } = e.touches[0];
    touchStartRef.current = { x: clientX, y: clientY, timestamp: Date.now() };
  }, []);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (!touchStartRef.current) return;

    const { clientX, clientY } = e.changedTouches[0];
    const { x: startX, y: startY, timestamp: startTime } = touchStartRef.current;

    const deltaX = clientX - startX;
    const deltaY = clientY - startY;
    const timeDelta = Date.now() - startTime;

    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);

    const velocity = Math.sqrt(deltaX ** 2 + deltaY ** 2) / timeDelta;

    const isFastSwipe = velocity > velocityThreshold;

    if (absDeltaX > absDeltaY) { // Horizontal swipe
      if (absDeltaX > threshold || isFastSwipe) {
        if (deltaX > 0) {
          onSwipeRight?.();
        } else {
          onSwipeLeft?.();
        }
        if (preventScroll) e.preventDefault();
      }
    } else { // Vertical swipe
      if (absDeltaY > threshold || isFastSwipe) {
        if (deltaY > 0) {
          onSwipeDown?.();
        } else {
          onSwipeUp?.();
        }
        if (preventScroll) e.preventDefault();
      }
    }

    touchStartRef.current = null;
  }, [onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, threshold, velocityThreshold, preventScroll]);

  // Attach listeners using a callback ref
  const callbackRef = useCallback((node: HTMLDivElement) => {
    if (ref.current) {
        ref.current.removeEventListener('touchstart', handleTouchStart);
        ref.current.removeEventListener('touchend', handleTouchEnd);
    }
    
    ref.current = node;

    if (ref.current) {
        ref.current.addEventListener('touchstart', handleTouchStart, { passive: !preventScroll });
        ref.current.addEventListener('touchend', handleTouchEnd, { passive: !preventScroll });
    }
  }, [handleTouchStart, handleTouchEnd, preventScroll]);


  return { ref: callbackRef as any };
}