import { MatchingService } from '../services/matching.service'

async function main() {
  const cfg = {
    brandIds: [],
    partCategoryIds: [],
    requiredTemplateIds: [4,2,3,1],
    toleranceOverrides: { 1:0.05,2:0.05,3:0.05,4:0.05 },
    catalogItemIds: [53,54],
    maxItems: 100,
    sampleSize: 20,
  }
  // @ts-ignore access private for debug
  const res = await (MatchingService as any)._buildBulkPreview(cfg)
  console.log(JSON.stringify(res, null, 2))
}

main().catch(e=>{ console.error(e); process.exit(1)}).then(()=>process.exit(0))

