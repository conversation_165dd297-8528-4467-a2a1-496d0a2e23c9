import React from 'react'
import { Clock, ShoppingCart, Lock } from 'lucide-react'
import { useIsMobile } from '@/hooks/useMediaQuery'
import BaseCard from './BaseCard'
import { Checkbox } from '@/components/ui/checkbox'
import {
  createCardClickHandler,
  createCatalogItemUrl,
  createBrandBadge,
  createSkuBadge,
  formatCardDate,
  formatAttributeForCard,
} from './card-utils'
import { usePrefetchOnHover } from '@/hooks/usePrefetch'

// Import FullCatalogItem type from lib/types.ts
type FullCatalogItem = {
  id: number
  sku: string
  brand?: { name: string } | null
  image: { path: string; alt?: string | null } | null
  attributes: Array<{
    template: { title: string; unit?: string | null }
    value: string
  }>
  applicabilities?: Array<unknown>
  updatedAt: Date | string
}

export interface CatalogItemCardProps {
  item: FullCatalogItem
  layout?: 'compact' | 'detailed' | 'grid'
  onClick?: () => void
  animationDelay?: number
  showProUpsell?: boolean
  className?: string
  showCheckbox?: boolean
  isSelected?: boolean
  onSelectionChange?: (selected: boolean) => void
}

export default function CatalogItemCard({
  item,
  layout = 'detailed',
  onClick,
  animationDelay,
  showProUpsell = false,
  className,
  showCheckbox = false,
  isSelected = false,
  onSelectionChange,
}: CatalogItemCardProps) {
  const isMobile = useIsMobile()
  const handleClick = createCardClickHandler(createCatalogItemUrl(item.id), onClick)
  const handleMouseEnter = usePrefetchOnHover(item.id, 'catalogItem')

  // Prepare badges
  const badges = []

  if (item.brand?.name) {
    badges.push(createBrandBadge(item.brand.name))
  }

  badges.push(createSkuBadge(item.sku))

  if (showProUpsell && item.applicabilities && item.applicabilities.length > 0) {
    badges.push({
      label: `${item.applicabilities.length} аналогов в PRO`,
      variant: 'secondary' as const,
      icon: Lock,
    })
  }

  // Format attributes
  const formattedAttributes = item.attributes.map(formatAttributeForCard)
  const maxAttributesVisible = isMobile ? 2 : (layout === 'compact' ? 2 : 3);
  const effectiveLayout = isMobile ? 'compact' : layout;

  return (
    <BaseCard
      variant="catalogItem"
      layout={effectiveLayout}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      animationDelay={animationDelay}
      className={isSelected ? `${className} border-primary bg-primary/5` : className}
    >
      {showCheckbox && (
        <div className="absolute top-2 left-2 z-10">
          <Checkbox
            checked={isSelected}
            onCheckedChange={onSelectionChange}
            onClick={(e) => e.stopPropagation()}
            className="bg-background border-2"
            aria-label={`Выбрать ${item.sku}`}
          />
        </div>
      )}
      
      <div className="flex gap-4">
        {/* Media */}
        <BaseCard.Media mediaAsset={item.image} size={isMobile ? 'md' : 'sm'} />

        {/* Content */}
        <div className="flex-1 min-w-0 space-y-3">
          {/* Header */}
          <BaseCard.Header title={item.sku} />

          {/* Badges */}
          {badges.length > 0 && <BaseCard.Badges badges={badges} />}

          {/* Attributes */}
          {formattedAttributes.length > 0 && effectiveLayout !== 'compact' && (
            <BaseCard.Attributes
              attributes={formattedAttributes}
              maxVisible={maxAttributesVisible}
            />
          )}

          {/* Footer */}
          <BaseCard.Footer>
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span>{formatCardDate(item.updatedAt)}</span>
            </div>
          </BaseCard.Footer>

          {/* Actions */}
          {effectiveLayout === 'detailed' && (
            <BaseCard.Actions
              actions={[
                {
                  label: 'Посмотреть артикул',
                  onClick: handleClick,
                  variant: 'gradient',
                  icon: ShoppingCart,
                },
              ]}
            />
          )}
        </div>
      </div>
    </BaseCard>
  )
}

