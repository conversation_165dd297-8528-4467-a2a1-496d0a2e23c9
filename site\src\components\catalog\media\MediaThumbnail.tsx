"use client"

import { FileText, ImageIcon } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import type { MediaAsset } from "@/types/catalog"

interface MediaThumbnailProps {
  mediaAsset?: MediaAsset | null
  className?: string
  size?: "sm" | "md" | "lg"
  showBadge?: boolean
}

export function MediaThumbnail({ mediaAsset, className, size = "md", showBadge = true }: MediaThumbnailProps) {
  const sizeClasses = { sm: "w-12 h-12", md: "w-16 h-16", lg: "w-24 h-24" }

  if (!mediaAsset) {
    return (
      <div className={cn("flex items-center justify-center bg-muted/30 rounded-lg border border-border", sizeClasses[size], className)}>
        <ImageIcon className="h-1/2 w-1/2 text-muted-foreground" />
      </div>
    )
  }

  const isImage = mediaAsset.mimeType.startsWith("image/")
  const isPDF = mediaAsset.mimeType === "application/pdf"

  return (
    <div className={cn("relative", className)}>
      <div className={cn("rounded-lg overflow-hidden border border-border-strong", sizeClasses[size])}>
        {isImage ? (
          <img src={mediaAsset.url || "/placeholder.svg"} alt={mediaAsset.fileName} className="w-full h-full object-cover" loading="lazy" />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-muted/50">
            <FileText className="h-1/2 w-1/2 text-muted-foreground" />
          </div>
        )}
      </div>

      {showBadge && (
        <Badge variant="secondary" className="absolute -top-1 -right-1 text-xs h-5 px-1">{isPDF ? "PDF" : mediaAsset.mimeType.split("/")[1].toUpperCase()}</Badge>
      )}
    </div>
  )
}