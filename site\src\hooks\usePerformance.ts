import { useEffect, useRef, useCallback } from 'react'
import { analytics } from '@/lib/analytics'

const isDev = import.meta.env.DEV

type PerformanceMetrics = {
  componentRenders: Map<string, number[]>
  dataLoads: Map<string, number[]>
  pageLoad: {
    ttfb: number
    domContentLoaded: number
    loadComplete: number
    fcp?: number
    lcp?: number
  } | null
  longTasks: Array<{ duration: number; startTime: number }>
}

const metrics: PerformanceMetrics = {
  componentRenders: new Map(),
  dataLoads: new Map(),
  pageLoad: null,
  longTasks: [],
}

export function useRenderTime(componentName: string, enabled: boolean = isDev) {
  const startMark = `${componentName}-render-start`
  const endMark = `${componentName}-render-end`
  const measureName = `${componentName}-render-time`
  const renderTimeRef = useRef<number | null>(null)

  useEffect(() => {
    if (!enabled || typeof window.performance === 'undefined') return

    performance.mark(startMark)

    return () => {
      performance.mark(endMark)
      const measure = performance.measure(measureName, startMark, endMark)
      const duration = measure.duration
      renderTimeRef.current = duration

      if (!metrics.componentRenders.has(componentName)) {
        metrics.componentRenders.set(componentName, [])
      }
      metrics.componentRenders.get(componentName)?.push(duration)

      if (isDev) {
        console.log(`%c[Perf] ${componentName} render time: ${duration.toFixed(2)}ms`, 'color: #9B59B6')
      }

      if (duration > 100) {
        analytics.componentRenderTimeRecorded(componentName, duration)
      }
      
      // Clean up marks
      performance.clearMarks(startMark)
      performance.clearMarks(endMark)
      performance.clearMeasures(measureName)
    }
  }, [componentName, enabled, startMark, endMark, measureName])

  return { renderTime: renderTimeRef.current }
}

export function useDataLoadTime(queryKey: string, enabled: boolean = isDev) {
    // This hook is better implemented as a side-effect within React Query's onSuccess
    // For now, we'll provide a placeholder.
    return { loadTime: null, isSlowQuery: false }
}


export function usePageLoadMetrics(enabled: boolean = true) {
  useEffect(() => {
    if (!enabled || typeof window.performance === 'undefined' || metrics.pageLoad) return

    const handleLoad = () => {
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (!navigationEntry) return;

      const ttfb = navigationEntry.responseStart - navigationEntry.requestStart
      const domContentLoaded = navigationEntry.domContentLoadedEventEnd - navigationEntry.startTime
      const loadComplete = navigationEntry.loadEventEnd - navigationEntry.startTime

      metrics.pageLoad = { ttfb, domContentLoaded, loadComplete }

      if (isDev) {
        console.log(`%c[Perf] Page Load: TTFB=${ttfb.toFixed(2)}ms, DCL=${domContentLoaded.toFixed(2)}ms, Load=${loadComplete.toFixed(2)}ms`, 'color: #3498DB');
      }
      
      // LCP and FCP
      new PerformanceObserver((entryList) => {
          for (const entry of entryList.getEntries()) {
              if (entry.name === 'first-contentful-paint') {
                  metrics.pageLoad!.fcp = entry.startTime;
                  if (isDev) console.log(`%c[Perf] FCP: ${entry.startTime.toFixed(2)}ms`, 'color: #3498DB');
              }
              if (entry.name === 'largest-contentful-paint') {
                  metrics.pageLoad!.lcp = entry.startTime;
                   if (isDev) console.log(`%c[Perf] LCP: ${entry.startTime.toFixed(2)}ms`, 'color: #3498DB');
              }
          }
      }).observe({ type: 'paint', buffered: true });
    }

    if (document.readyState === 'complete') {
        handleLoad();
    } else {
        window.addEventListener('load', handleLoad, { once: true });
    }

  }, [enabled])
}

export function useLongTaskDetection(threshold: number = 50, enabled: boolean = isDev) {
    useEffect(() => {
        if (!enabled || typeof PerformanceObserver === 'undefined') return;

        const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                metrics.longTasks.push({ duration: entry.duration, startTime: entry.startTime });
                 if (isDev) {
                    console.warn(`[Perf] Long task detected: ${entry.duration.toFixed(2)}ms`, entry);
                }
                analytics.longTaskDetected(entry.duration, entry.startTime);
            }
        });

        observer.observe({ type: 'longtask', buffered: true });

        return () => observer.disconnect();

    }, [enabled, threshold]);
}


export const measureAsync = async <T>(name: string, fn: () => Promise<T>): Promise<T> => {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    if (isDev) console.log(`%c[Perf] Async operation '${name}' took ${duration.toFixed(2)}ms`, 'color: #F1C40F');
    return result;
};

export const measureSync = <T>(name: string, fn: () => T): T => {
    const start = performance.now();
    const result = fn();
    const duration = performance.now() - start;
    if (isDev) console.log(`%c[Perf] Sync operation '${name}' took ${duration.toFixed(2)}ms`, 'color: #F1C40F');
    return result;
};


export const getPerformanceMetrics = (): PerformanceMetrics => metrics;
export const clearPerformanceMetrics = (): void => {
    metrics.componentRenders.clear();
    metrics.dataLoads.clear();
    metrics.longTasks = [];
    metrics.pageLoad = null;
};