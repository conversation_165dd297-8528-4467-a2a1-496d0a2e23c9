import { getSystemDB } from '../db'
import { MatchingService } from '../services/matching.service'

async function main() {
  const db = getSystemDB()
  const config = {
    requiredTemplateIds: [1], // только seal_type для изоляции проверки синонимов
    toleranceOverrides: {},
    catalogItemIds: [53,54],
    maxItems: 100,
    sampleSize: 20,
  }
  const op = await db.bulkMatchingOperation.create({ data: { status: 'DRAFT', config: config as any } })
  const res = await MatchingService.previewBulkMatching({ operationId: op.id } as any)
  console.log(JSON.stringify(res, null, 2))
}

main().catch((e)=>{ console.error(e); process.exit(1) }).then(()=>process.exit(0))

