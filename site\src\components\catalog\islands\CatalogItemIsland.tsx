"use client";

import { Badge } from "@/components/ui/badge";
import { ModernButton } from "@/components/ui/modern-button";
import { ModernCard, ModernCardContent, ModernCardHeader, ModernCardTitle } from "@/components/ui/modern-card";
import { MediaThumbnail } from "@/components/catalog/media/MediaThumbnail";
import { Layers, Building, Wrench, CheckCircle2, Download, Building2, <PERSON>rkles, ArrowRight } from "lucide-react";
import type { Part, CatalogItem as CatalogItemType, FullCatalogItemAttribute } from "@/lib/types";
import { AttributeDisplayTable } from "../attributes";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { EmptyState } from "@/components/shared/EmptyState";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

import { useFeatureAccess, Feature } from "@/hooks/useFeatureAccess";
import { PartApplicabilityPreview } from "@/components/subscription/PartApplicabilityPreview";
import { SubscriptionGate } from "@/components/subscription/SubscriptionGate";
import { ProUpsellBanner } from "@/components/subscription/ProUpsellBanner";
import { findSimilarItems } from "@/lib/similarity";
import { generateCatalogItemCSV, generateCatalogItemJSON, downloadFile, generateExportFilename } from "@/lib/export-utils";
import { analytics } from "@/lib/analytics";
import { useState, useMemo, useRef, lazy, Suspense } from "react";
import { useIsMobile } from '@/hooks/useMediaQuery';
import { useSwipeGesture } from '@/hooks/useSwipeGesture';
import { motion, AnimatePresence } from 'framer-motion';
import { fadeInLeft, fadeInRight } from '@/lib/animation-variants';
import { useRenderTime } from '@/hooks/usePerformance';
import { LoadingState } from '@/components/shared/LoadingState';

const AttributeComparison = lazy(() => import('../attributes/AttributeComparison'));
const MediaGallery = lazy(() => import('../media/MediaGallery'));
const EquipmentApplicabilitySection = lazy(() => import('./EquipmentApplicabilitySection'));

type CatalogItem = Part["applicabilities"][0]["catalogItem"];
type Applicability = Part["applicabilities"][0];
type FullEquipmentApplicability = {
  id: number
  equipmentModelId: number
  notes?: string
  equipmentModel: {
    id: number
    name: string
    brand: {
      id: number
      name: string
    }
    attributes?: Array<{
      id: number
      value: string
      template: {
        id: number
        name: string
        dataType: string
      }
    }>
  }
}

interface CatalogItemIslandProps {
  item: CatalogItem & {
    applicabilities?: Applicability[]
    equipmentApplicabilities?: FullEquipmentApplicability[]
  }
  isPro?: boolean
  applicabilitiesCount?: number
  alternativeItems?: CatalogItemType[]
  similarItems?: CatalogItemType[]
}

export default function CatalogItemIsland({
  item,
  isPro = false,
  applicabilitiesCount = 0,
  alternativeItems = [],
  similarItems = []
}: CatalogItemIslandProps) {

  const { canViewApplicability, isPending } = useFeatureAccess()
  const [activeTab, setActiveTab] = useState("overview")

  const { renderTime } = useRenderTime('CatalogItemIsland', import.meta.env.DEV)

  const isMobile = useIsMobile();
  
  // Tab order for swipe navigation
  const tabOrder = ['overview', 'comparison', 'alternatives', 'equipment', 'similar'];
  const currentTabIndex = tabOrder.indexOf(activeTab);

  // Swipe gesture for tab navigation
  const { ref: swipeRef } = useSwipeGesture({
    onSwipeLeft: () => {
      if (currentTabIndex < tabOrder.length - 1) {
        const nextTab = tabOrder[currentTabIndex + 1];
        handleTabChange(nextTab);
        analytics.tabSwipeNavigated(item.id, 'left', nextTab);
      }
    },
    onSwipeRight: () => {
      if (currentTabIndex > 0) {
        const prevTab = tabOrder[currentTabIndex - 1];
        handleTabChange(prevTab);
        analytics.tabSwipeNavigated(item.id, 'right', prevTab);
      }
    },
    threshold: 50,
    preventScroll: false, // Allow vertical scroll
  });

  // Определяем, есть ли данные applicabilities
  const hasApplicabilitiesData = item.applicabilities && item.applicabilities.length > 0
  // Используем переданный count или длину массива
  const totalApplicabilities = applicabilitiesCount || item.applicabilities?.length || 0

  const handlePartClick = (partId: number) => {
    window.location.href = `/catalog/parts/${partId}`
  }

  const pluralizeAnalogs = (count: number): string => {
    const mod10 = count % 10
    const mod100 = count % 100
    
    if (mod10 === 1 && mod100 !== 11) return 'аналог'
    if (mod10 >= 2 && mod10 <= 4 && (mod100 < 10 || mod100 >= 20)) return 'аналога'
    return 'аналогов'
  }

  // Export handlers
  const handleExportCSV = () => {
    // Расширяем item дополнительными данными для экспорта
    const enrichedItem = {
      ...item,
      alternativeItems,
      equipmentApplicabilities: item.equipmentApplicabilities
    } as unknown as CatalogItemType
    
    const csv = generateCatalogItemCSV(enrichedItem, {
      includeApplicabilities: true,
      includeAlternatives: true,
      includeEquipment: true
    })
    const filename = generateExportFilename(item as unknown as CatalogItemType, 'csv')
    downloadFile(csv, filename, 'text/csv')
    analytics.catalogItemExported(item.id, 'csv', ['applicabilities', 'alternatives', 'equipment'])
  }

  const handleExportJSON = () => {
    // Расширяем item дополнительными данными для экспорта
    const enrichedItem = {
      ...item,
      alternativeItems,
      equipmentApplicabilities: item.equipmentApplicabilities
    } as unknown as CatalogItemType
    
    const json = generateCatalogItemJSON(enrichedItem, {
      includeApplicabilities: true,
      includeAlternatives: true,
      includeEquipment: true,
      includeComparison: true
    })
    const filename = generateExportFilename(item as unknown as CatalogItemType, 'json')
    downloadFile(json, filename, 'application/json')
    analytics.catalogItemExported(item.id, 'json', ['applicabilities', 'alternatives', 'equipment', 'comparison'])
  }

  // Calculate similar items
  const rankedSimilarItems = useMemo(() => {
    if (!isPro || !similarItems || similarItems.length === 0) return []
    return findSimilarItems(item as unknown as CatalogItemType, similarItems, 5)
  }, [item, similarItems, isPro])

  // Track tab changes
  const handleTabChange = (tabName: string) => {
    setActiveTab(tabName)
    analytics.catalogItemTabChanged(item.id, tabName)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <ModernCard>
        <ModernCardHeader className="flex flex-col md:flex-row items-start justify-between">
          <div className="flex-1">
            <div className="flex flex-col md:flex-row items-start gap-4">
              {item.mediaAssets && item.mediaAssets.length > 0 ? (
                <Suspense fallback={<LoadingState variant="card" />}>
                  <MediaGallery
                    assets={item.mediaAssets}
                    layout="grid"
                  columns={isMobile ? 2 : 3}
                  enableLightbox={true}
                  enableLazyLoad={true}
                  contextId={item.id.toString()}
                  contextType="catalogItem"
                  className="w-full md:max-w-xs"
                  />
                </Suspense>
              ) : (
                <MediaThumbnail mediaAsset={item.image} size="lg" />
              )}
              <div className="mt-4 md:mt-0">
                <ModernCardTitle className="text-2xl md:text-3xl font-bold font-mono">{item.sku}</ModernCardTitle>
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="outline"><Building className="h-3 w-3 mr-1.5" />{item.brand.name}</Badge>
                  {item.brand.isOem && <Badge variant="secondary">OEM</Badge>}
                </div>
              </div>
            </div>
            <p className="text-muted-foreground mt-4 max-w-2xl">{item.description}</p>
          </div>
          <div className="flex items-center gap-2 mt-4 md:mt-0">
            {isPro && !isMobile && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <ModernButton variant="outline" size="sm" className="gap-2">
                    <Download className="h-4 w-4" />
                    Экспорт
                  </ModernButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleExportCSV}>
                    Экспорт в CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleExportJSON}>
                    Экспорт в JSON
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
            <a href="/catalog" className="text-sm text-muted-foreground hover:text-primary transition-colors">← К поиску</a>
          </div>
        </ModernCardHeader>
        
        <ModernCardContent>
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <div className="relative">
              <TabsList className="grid w-full grid-cols-3 md:grid-cols-5 overflow-x-auto snap-x snap-mandatory">
                <TabsTrigger value="overview">Обзор</TabsTrigger>
                <TabsTrigger value="comparison">Сравнение</TabsTrigger>
                <TabsTrigger value="alternatives">Альтернативы</TabsTrigger>
                <TabsTrigger value="equipment" className="hidden sm:inline-flex">Техника</TabsTrigger>
                <TabsTrigger value="similar" className="hidden sm:inline-flex">Похожие</TabsTrigger>
              </TabsList>
              <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-card to-transparent pointer-events-none md:hidden" />
            </div>
            
            {isMobile && (
              <div className="text-xs text-muted-foreground text-center py-2 border-b">
                ← Свайп для переключения вкладок →
              </div>
            )}

            <div ref={isMobile ? swipeRef : null} className="mt-4">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={isMobile ? (currentTabIndex > tabOrder.indexOf(activeTab) ? { ...fadeInRight.initial } : { ...fadeInLeft.initial }) : { opacity: 1, x: 0 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={isMobile ? (currentTabIndex > tabOrder.indexOf(activeTab) ? { ...fadeInLeft.exit } : { ...fadeInRight.exit }) : { opacity: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  {/* Overview Tab */}
                  {activeTab === 'overview' && (
                    <TabsContent value="overview" className="space-y-4 md:space-y-6">
                      <div>
                        <h3 className="font-bold text-lg mb-4">Характеристики</h3>
                        <AttributeDisplayTable
                          attributes={item.attributes}
                          showSynonyms={false}
                          showTolerance={false}
                          contextId={item.id}
                          contextType="catalogItem"
                          maxVisible={6}
                          collapsible={true}
                        />
                      </div>
                      <div>
                        <h3 className="font-bold text-lg mb-4 flex items-center gap-2">
                          Группы взаимозаменяемости
                          {totalApplicabilities > 0 && <Badge variant="secondary" className="text-xs">{totalApplicabilities}</Badge>}
                        </h3>
                        {isPending ? (
                          <div className="flex items-center justify-center py-12 border-2 border-dashed rounded-lg">
                            <div className="text-center">
                              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                              <p className="text-sm text-muted-foreground">Загрузка...</p>
                            </div>
                          </div>
                        ) : (
                          <SubscriptionGate
                            feature={Feature.PART_APPLICABILITY}
                            upsellTitle="Хотите увидеть все аналоги?"
                            upsellDescription={totalApplicabilities > 0 ? `Найдено ${totalApplicabilities} ${pluralizeAnalogs(totalApplicabilities)} этой детали. Оформите PRO подписку для доступа к полной базе взаимозаменяемости.` : "Оформите PRO подписку для доступа к группам взаимозаменяемости и поиску аналогов."}
                            upsellFeatures={["Полный список всех аналогов", "Точность совпадения характеристик", "Переход к эталонным группам Part", "Сравнение атрибутов с эталоном"]}
                            fallback={totalApplicabilities > 0 ? (<><ProUpsellBanner /><PartApplicabilityPreview totalCount={totalApplicabilities} previewCount={3} variant="cards" /></>) : (<EmptyState icon={Wrench} title="Не состоит в группах" message="Этот артикул еще не был сопоставлен с эталонной группой." />)}
                          >
                            {hasApplicabilitiesData ? (
                              <div className="space-y-3">
                                {item.applicabilities!.map((app: Applicability) => (
                                  <ModernCard key={app.id} variant="default" className="hover:border-primary/40 transition-colors cursor-pointer" onClick={() => handlePartClick(app.part.id)}>
                                    <ModernCardContent className="p-3 md:p-4 flex items-center justify-between">
                                      <div>
                                        <h4 className="font-semibold text-primary">{app.part.name}</h4>
                                        <p className="text-sm text-muted-foreground">{app.part.partCategory.name}</p>
                                      </div>
                                      <div className="flex items-center gap-4">
                                        <Badge variant={app.accuracy === 'EXACT_MATCH' ? 'default' : 'secondary'} className="gap-1.5"><CheckCircle2 className="h-3 w-3"/>{app.accuracy}</Badge>
                                        <ModernButton variant="outline" size="sm" className="gap-1.5 h-7 text-xs"><Layers className="h-3 w-3" />Перейти к группе</ModernButton>
                                      </div>
                                    </ModernCardContent>
                                  </ModernCard>
                                ))}
                              </div>
                            ) : (
                              <EmptyState icon={Wrench} title="Не состоит в группах" message="Этот артикул еще не был сопоставлен с эталонной группой." />
                            )}
                          </SubscriptionGate>
                        )}
                      </div>
                    </TabsContent>
                  )}

                  {/* Comparison Tab */}
                  {activeTab === 'comparison' && (
                    <TabsContent value="comparison" className="space-y-4">
                      <SubscriptionGate
                        feature={Feature.PART_APPLICABILITY}
                        upsellTitle="Сравнение с эталоном доступно в PRO"
                        upsellDescription="Оформите PRO подписку для доступа к детальному сравнению характеристик с эталонной группой"
                        fallback={item.applicabilities && item.applicabilities.length > 0 ? (<div className="space-y-4"><ProUpsellBanner variant="compact" /><Suspense fallback={<LoadingState variant="text" count={5} />}><AttributeComparison partAttributes={item.applicabilities[0].part.attributes || []} itemAttributes={(item.attributes || []).map((attr: FullCatalogItemAttribute) => ({...attr, value: '***'}))} layout="cards" showMatchScore={false} highlightDifferences={false} /></Suspense></div>) : (<EmptyState icon={Layers} title="Нет данных для сравнения" message="Этот артикул не привязан к эталонной группе" />)}
                      >
                        {item.applicabilities && item.applicabilities.length > 0 ? (<Suspense fallback={<LoadingState variant="text" count={5} />}><AttributeComparison partAttributes={item.applicabilities[0].part.attributes || []} itemAttributes={item.attributes || []} layout="table" showMatchScore={true} highlightDifferences={true} /></Suspense>) : (<EmptyState icon={Layers} title="Нет данных для сравнения" message="Этот артикул не привязан к эталонной группе" />)}
                      </SubscriptionGate>
                    </TabsContent>
                  )}

                  {/* Alternatives Tab */}
                  {activeTab === 'alternatives' && (
                    <TabsContent value="alternatives" className="space-y-4">
                      <SubscriptionGate
                        feature={Feature.PART_APPLICABILITY}
                        upsellTitle="Альтернативные бренды доступны в PRO"
                        upsellDescription={`Найдено ${alternativeItems?.length ?? 0} альтернативных производителей этой детали`}
                        fallback={<ProUpsellBanner variant="compact" />}
                      >
                        {alternativeItems && alternativeItems.length > 0 ? (
                          <div className="space-y-4">
                            <p className="text-sm text-muted-foreground">Найдено {alternativeItems.length} альтернативных артикулов от других производителей</p>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {alternativeItems.map(altItem => (
                                <ModernCard key={altItem.id} className="hover:border-primary/40 transition-colors">
                                  <ModernCardContent className="p-3 md:p-4">
                                    <div className="flex items-start gap-4">
                                      <MediaThumbnail mediaAsset={altItem.image} size="sm" />
                                      <div className="flex-1">
                                        <h4 className="font-semibold font-mono">{altItem.sku}</h4>
                                        <div className="flex items-center gap-2 mt-1"><Badge variant="outline" className="text-xs"><Building className="h-3 w-3 mr-1" />{altItem.brand?.name}</Badge>{altItem.brand?.isOem && <Badge variant="secondary" className="text-xs">OEM</Badge>}</div>
                                      </div>
                                      <ModernButton variant="outline" size="sm" onClick={() => { analytics.alternativeBrandClicked(item.id, altItem.id, altItem.brand?.name || ''); window.location.href = `/catalog/items/${altItem.id}`; }}>Посмотреть<ArrowRight className="h-3 w-3 ml-1" /></ModernButton>
                                    </div>
                                  </ModernCardContent>
                                </ModernCard>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <EmptyState icon={Building2} title="Нет альтернатив" message="Альтернативные производители для этой детали не найдены" />
                        )}
                      </SubscriptionGate>
                    </TabsContent>
                  )}

                  {/* Equipment Tab */}
                  {activeTab === 'equipment' && (
                    <TabsContent value="equipment" className="space-y-4">
                      <SubscriptionGate
                        feature={Feature.PART_APPLICABILITY}
                        upsellTitle="Применимость к технике доступна в PRO"
                        upsellDescription="Узнайте, в какой технике используется эта деталь"
                        fallback={<ProUpsellBanner variant="compact" />}
                      >
                        {item.equipmentApplicabilities && item.equipmentApplicabilities.length > 0 ? (<Suspense fallback={<LoadingState variant="card" count={3} />}><EquipmentApplicabilitySection equipmentApplicabilities={item.equipmentApplicabilities} partId={item.id} /></Suspense>) : (<EmptyState icon={Wrench} title="Нет данных о применимости" message="Информация о применимости к технике пока недоступна" />)}
                      </SubscriptionGate>
                    </TabsContent>
                  )}

                  {/* Similar Tab */}
                  {activeTab === 'similar' && (
                    <TabsContent value="similar" className="space-y-4">
                      <SubscriptionGate
                        feature={Feature.PART_APPLICABILITY}
                        upsellTitle="Рекомендации доступны в PRO"
                        upsellDescription="Найдите похожие детали на основе характеристик"
                        fallback={<ProUpsellBanner variant="compact" />}
                      >
                        {rankedSimilarItems.length > 0 ? (
                          <div className="space-y-4">
                            <p className="text-sm text-muted-foreground">Найдено {rankedSimilarItems.length} похожих артикулов</p>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {rankedSimilarItems.map(({ item: simItem, score, matchingAttributes }) => (
                                <ModernCard key={simItem.id} className="hover:border-primary/40 transition-colors">
                                  <ModernCardContent className="p-3 md:p-4">
                                    <div className="flex items-start gap-4">
                                      <MediaThumbnail mediaAsset={simItem.image} size="sm" />
                                      <div className="flex-1">
                                        <h4 className="font-semibold font-mono">{simItem.sku}</h4>
                                        <Badge variant="outline" className="mt-1 text-xs">{simItem.brand?.name}</Badge>
                                        <div className="mt-2 flex items-center gap-2"><Badge variant="secondary" className="text-xs"><Sparkles className="h-3 w-3 mr-1" />{Math.round(score * 100)}% совпадение</Badge><span className="text-xs text-muted-foreground">{matchingAttributes} общих атрибутов</span></div>
                                      </div>
                                      <ModernButton variant="outline" size="sm" onClick={() => { analytics.similarItemClicked(item.id, simItem.id, score); window.location.href = `/catalog/items/${simItem.id}`; }}>Посмотреть</ModernButton>
                                    </div>
                                  </ModernCardContent>
                                </ModernCard>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <EmptyState icon={Sparkles} title="Нет похожих артикулов" message="Похожие детали не найдены" />
                        )}
                      </SubscriptionGate>
                    </TabsContent>
                  )}
                </motion.div>
              </AnimatePresence>
            </div>
          </Tabs>
        </ModernCardContent>
      </ModernCard>
    </div>
  );
}
