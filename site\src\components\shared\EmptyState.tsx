"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import {
  Inbox,
  SearchX,
  Filter,
  PackageOpen,
  type LucideIcon,
} from "lucide-react";
import {
  ModernCard,
  ModernCardContent,
} from "@/components/ui/modern-card";
import { ModernButton } from "@/components/ui/modern-button";

// ============================================================================
// Types
// ============================================================================

export interface EmptyStateAction {
  label: string;
  onClick: () => void;
  variant?: "default" | "outline";
}

export interface EmptyStateProps {
  icon?: LucideIcon;
  title?: string;
  message?: string;
  variant?: "default" | "search" | "filter" | "minimal";
  action?: EmptyStateAction;
  secondaryAction?: EmptyStateAction;
  className?: string;
}

// ============================================================================
// Variant Presets
// ============================================================================

const variantPresets: Record<
  "default" | "search" | "filter",
  {
    icon: LucideIcon;
    title: string;
    message: string;
  }
> = {
  default: {
    icon: Inbox,
    title: "Пусто",
    message: "Здесь пока ничего нет",
  },
  search: {
    icon: SearchX,
    title: "Ничего не найдено",
    message: "Попробуйте изменить поисковый запрос",
  },
  filter: {
    icon: Filter,
    title: "Нет результатов",
    message: "Попробуйте изменить фильтры или очистить их",
  },
};

// ============================================================================
// Variant Components
// ============================================================================

/**
 * Полный вариант пустого состояния
 */
const EmptyStateDefault: React.FC<Omit<EmptyStateProps, "variant">> = ({
  icon,
  title,
  message,
  action,
  secondaryAction,
  className,
}) => {
  const IconComponent = icon || Inbox;

  return (
    <ModernCard
      variant="elevated"
      className={cn(
        "text-center py-12 border-2 border-dashed border-border-strong",
        className
      )}
    >
      <ModernCardContent>
        <div className="flex flex-col items-center gap-4">
          {/* Icon */}
          <div className="h-12 w-12 rounded-full bg-muted/50 flex items-center justify-center">
            <IconComponent className="h-6 w-6 text-muted-foreground" />
          </div>

          {/* Text */}
          <div className="max-w-md">
            {title && (
              <h3 className="text-lg font-semibold mb-1 text-foreground">
                {title}
              </h3>
            )}
            {message && (
              <p className="text-muted-foreground text-sm leading-relaxed">
                {message}
              </p>
            )}
          </div>

          {/* Actions */}
          {(action || secondaryAction) && (
            <div className="flex flex-col sm:flex-row items-center gap-2 mt-2">
              {action && (
                <ModernButton
                  onClick={action.onClick}
                  variant={action.variant || "default"}
                  size="sm"
                >
                  {action.label}
                </ModernButton>
              )}
              {secondaryAction && (
                <ModernButton
                  onClick={secondaryAction.onClick}
                  variant="ghost"
                  size="sm"
                >
                  {secondaryAction.label}
                </ModernButton>
              )}
            </div>
          )}
        </div>
      </ModernCardContent>
    </ModernCard>
  );
};

/**
 * Минимальный вариант пустого состояния
 */
const EmptyStateMinimal: React.FC<Omit<EmptyStateProps, "variant">> = ({
  icon,
  title,
  message,
  className,
}) => {
  const IconComponent = icon || Inbox;

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center gap-3 py-8",
        className
      )}
    >
      <IconComponent className="h-8 w-8 text-muted-foreground" />
      <div className="text-center">
        {title && (
          <p className="font-medium text-sm text-foreground">{title}</p>
        )}
        {message && (
          <p className="text-xs text-muted-foreground mt-1">{message}</p>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// Main Component
// ============================================================================

/**
 * Компонент пустого состояния с кастомизируемыми сообщениями и действиями
 */
export const EmptyState = React.forwardRef<HTMLDivElement, EmptyStateProps>(
  (
    {
      icon,
      title,
      message,
      variant = "default",
      action,
      secondaryAction,
      className,
    },
    ref
  ) => {
    // Получаем пресет для варианта, если title/message не указаны
    const preset =
      variant !== "minimal" ? variantPresets[variant] : variantPresets.default;
    const finalIcon = icon || preset.icon;
    const finalTitle = title || preset.title;
    const finalMessage = message || preset.message;

    if (variant === "minimal") {
      return (
        <div ref={ref}>
          <EmptyStateMinimal
            icon={finalIcon}
            title={finalTitle}
            message={finalMessage}
            className={className}
          />
        </div>
      );
    }

    return (
      <div ref={ref}>
        <EmptyStateDefault
          icon={finalIcon}
          title={finalTitle}
          message={finalMessage}
          action={action}
          secondaryAction={secondaryAction}
          className={className}
        />
      </div>
    );
  }
);

EmptyState.displayName = "EmptyState";

// Re-export icons for convenience
export { Inbox, SearchX, Filter, PackageOpen };

export default EmptyState;


