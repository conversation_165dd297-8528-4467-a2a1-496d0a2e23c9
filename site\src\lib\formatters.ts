/**
 * Централизованные утилиты форматирования
 * Используются для единообразного отображения дат, чисел, валют и значений атрибутов
 */

const LOCALE = 'ru-RU';
const DEFAULT_CURRENCY = 'RUB';
const FALLBACK_VALUE = '—';

// ============================================================================
// Date Formatting
// ============================================================================

/**
 * Форматирует дату с поддержкой локали
 */
export function formatDate(
  date: Date | string | null,
  format: 'short' | 'long' | 'relative' = 'short'
): string {
  if (!date) return FALLBACK_VALUE;

  const dateObj = typeof date === 'string' ? new Date(date) : date;
  if (isNaN(dateObj.getTime())) return FALLBACK_VALUE;

  if (format === 'relative') {
    return formatRelativeTime(dateObj);
  }

  const options: Intl.DateTimeFormatOptions = format === 'long'
    ? { year: 'numeric', month: 'long', day: 'numeric' }
    : { year: 'numeric', month: '2-digit', day: '2-digit' };

  return new Intl.DateTimeFormat(LOCALE, options).format(dateObj);
}

/**
 * Форматирует дату и время
 */
export function formatDateTime(date: Date | string | null): string {
  if (!date) return FALLBACK_VALUE;

  const dateObj = typeof date === 'string' ? new Date(date) : date;
  if (isNaN(dateObj.getTime())) return FALLBACK_VALUE;

  return new Intl.DateTimeFormat(LOCALE, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  }).format(dateObj);
}

/**
 * Форматирует относительное время (например, "2 часа назад")
 */
export function formatRelativeTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  if (isNaN(dateObj.getTime())) return FALLBACK_VALUE;

  const now = new Date();
  const diffMs = now.getTime() - dateObj.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  const diffMonth = Math.floor(diffDay / 30);
  const diffYear = Math.floor(diffDay / 365);

  const rtf = new Intl.RelativeTimeFormat(LOCALE, { numeric: 'auto' });

  if (diffYear > 0) return rtf.format(-diffYear, 'year');
  if (diffMonth > 0) return rtf.format(-diffMonth, 'month');
  if (diffDay > 0) return rtf.format(-diffDay, 'day');
  if (diffHour > 0) return rtf.format(-diffHour, 'hour');
  if (diffMin > 0) return rtf.format(-diffMin, 'minute');
  return rtf.format(-diffSec, 'second');
}

// ============================================================================
// Number Formatting
// ============================================================================

/**
 * Форматирует числа с разделителями для локали
 */
export function formatNumber(
  value: number | string | null,
  decimals?: number
): string {
  const num = parseNumericValue(value);
  if (num === null) return FALLBACK_VALUE;

  const options: Intl.NumberFormatOptions = decimals !== undefined
    ? { minimumFractionDigits: decimals, maximumFractionDigits: decimals }
    : {};

  return new Intl.NumberFormat(LOCALE, options).format(num);
}

/**
 * Форматирует десятичные числа с фиксированной точностью
 */
export function formatDecimal(
  value: number | string | null,
  decimals: number = 2
): string {
  const num = parseNumericValue(value);
  if (num === null) return FALLBACK_VALUE;

  return new Intl.NumberFormat(LOCALE, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(num);
}

/**
 * Форматирует как процент
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return new Intl.NumberFormat(LOCALE, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value / 100);
}

/**
 * Форматирует большие числа компактно (1.2K, 3.5M)
 */
export function formatCompactNumber(value: number): string {
  return new Intl.NumberFormat(LOCALE, {
    notation: 'compact',
    compactDisplay: 'short',
  }).format(value);
}

// ============================================================================
// Currency Formatting
// ============================================================================

/**
 * Форматирует валюту с символом
 */
export function formatCurrency(
  amount: number | null,
  currency: string = DEFAULT_CURRENCY
): string {
  if (amount === null) return FALLBACK_VALUE;

  return new Intl.NumberFormat(LOCALE, {
    style: 'currency',
    currency,
  }).format(amount);
}

/**
 * Форматирует цену в рублях (алиас для formatCurrency с RUB)
 */
export function formatPrice(amount: number | null): string {
  return formatCurrency(amount, DEFAULT_CURRENCY);
}

// ============================================================================
// Attribute Value Formatting
// ============================================================================

/**
 * Форматирует значение атрибута с опциональной единицей измерения
 */
export function formatAttributeValue(
  value: string | number | null,
  unit?: string | null,
  withUnit: boolean = true
): string {
  if (value === null || value === undefined || value === '') {
    return FALLBACK_VALUE;
  }

  const formattedValue = typeof value === 'number'
    ? formatNumber(value)
    : String(value);

  if (withUnit && unit) {
    return `${formattedValue} ${unit}`;
  }

  return formattedValue;
}

/**
 * Форматирует числовой диапазон (например, "30-35 мм")
 */
export function formatAttributeRange(
  min: number,
  max: number,
  unit?: string | null
): string {
  const formattedMin = formatNumber(min);
  const formattedMax = formatNumber(max);
  const range = `${formattedMin}–${formattedMax}`;

  if (unit) {
    return `${range} ${unit}`;
  }

  return range;
}

/**
 * Форматирует значение с допуском (например, "30±0.5 мм")
 */
export function formatTolerance(
  value: number,
  tolerance: number,
  unit?: string | null
): string {
  const formattedValue = formatNumber(value);
  const formattedTolerance = formatNumber(tolerance);
  const result = `${formattedValue}±${formattedTolerance}`;

  if (unit) {
    return `${result} ${unit}`;
  }

  return result;
}

// ============================================================================
// SKU/Text Formatting
// ============================================================================

/**
 * Форматирует SKU для отображения
 */
export function formatSKU(sku: string): string {
  return sku.trim().toUpperCase();
}

/**
 * Обрезает текст с многоточием
 */
export function truncateText(
  text: string,
  maxLength: number,
  suffix: string = '...'
): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength - suffix.length) + suffix;
}

/**
 * Возвращает текст с подсвеченными совпадениями для поиска
 * Возвращает объект с частями текста и флагом совпадения
 */
export function highlightMatch(
  text: string,
  query: string
): Array<{ text: string; isMatch: boolean }> {
  if (!query.trim()) {
    return [{ text, isMatch: false }];
  }

  const escapedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  const regex = new RegExp(`(${escapedQuery})`, 'gi');
  const parts = text.split(regex);

  return parts.map((part) => {
    // Создаем новый regex для каждой проверки, чтобы избежать проблем с lastIndex
    const testRegex = new RegExp(`^${escapedQuery}$`, 'i');
    return {
      text: part,
      isMatch: testRegex.test(part),
    };
  });
}

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Безопасно парсит числовое значение
 */
export function parseNumericValue(value: string | number | null): number | null {
  if (value === null || value === undefined || value === '') {
    return null;
  }

  if (typeof value === 'number') {
    return isNaN(value) ? null : value;
  }

  // Удаляем пробелы и заменяем запятую на точку
  const cleaned = String(value).replace(/\s/g, '').replace(',', '.');
  const parsed = parseFloat(cleaned);

  return isNaN(parsed) ? null : parsed;
}

/**
 * Type guard для проверки валидного числа
 */
export function isValidNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
}


