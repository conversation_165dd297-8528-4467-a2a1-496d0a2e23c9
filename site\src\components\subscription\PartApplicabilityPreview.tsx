"use client"

import { motion } from "motion/react"
import { Lock, Crown, ArrowRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

export interface PartApplicabilityPreviewProps {
  totalCount: number
  previewCount?: number
  className?: string
  variant?: "table" | "cards"
}

function pluralize(count: number, one: string, few: string, many: string): string {
  const mod10 = count % 10
  const mod100 = count % 100
  
  if (mod10 === 1 && mod100 !== 11) return one
  if (mod10 >= 2 && mod10 <= 4 && (mod100 < 10 || mod100 >= 20)) return few
  return many
}

function TablePreview({ items }: { items: any[] }) {
  return (
    <div className="border rounded-lg overflow-hidden">
      <table className="w-full">
        <thead className="bg-muted">
          <tr>
            <th className="px-4 py-2 text-left">Артикул</th>
            <th className="px-4 py-2 text-left">Бренд</th>
            <th className="px-4 py-2 text-left">Точность</th>
          </tr>
        </thead>
        <tbody>
          {items.map(item => (
            <tr key={item.id} className="border-t">
              <td className="px-4 py-2">{item.sku}</td>
              <td className="px-4 py-2">{item.brand}</td>
              <td className="px-4 py-2">{item.accuracy}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function CardsPreview({ items }: { items: any[] }) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {items.map(item => (
        <div key={item.id} className="border rounded-lg p-4">
          <div className="font-semibold">{item.sku}</div>
          <div className="text-sm text-muted-foreground">{item.brand}</div>
          <div className="text-xs mt-2">{item.accuracy}</div>
        </div>
      ))}
    </div>
  )
}

export function PartApplicabilityPreview({
  totalCount,
  previewCount = 3,
  className,
  variant = "table"
}: PartApplicabilityPreviewProps) {
  const hiddenCount = Math.max(0, totalCount - previewCount)
  
  // Mock данные для превью (размытые)
  const mockItems = Array.from({ length: previewCount }, (_, i) => ({
    id: i,
    sku: `***-${i + 1}***`,
    brand: "***",
    accuracy: "***"
  }))
  
  return (
    <div className={cn("relative", className)}>
      {/* Размытый контент */}
      <div className="blur-sm pointer-events-none select-none">
        {variant === "table" ? (
          <TablePreview items={mockItems} />
        ) : (
          <CardsPreview items={mockItems} />
        )}
      </div>
      
      {/* Overlay с призывом к действию */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm"
      >
        <div className="text-center max-w-md px-4">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-amber-500 to-orange-500 mb-4">
            <Lock className="w-8 h-8 text-white" />
          </div>
          
          <h3 className="text-2xl font-bold mb-2">
            Еще {hiddenCount} {pluralize(hiddenCount, 'аналог', 'аналога', 'аналогов')}
          </h3>
          
          <p className="text-muted-foreground mb-4">
            Оформите PRO подписку, чтобы увидеть полный список взаимозаменяемых деталей с точностью совпадения и сравнением характеристик
          </p>
          
          <div className="flex flex-col sm:flex-row gap-2 justify-center">
            <Button asChild size="lg">
              <a href="/pricing">
                <Crown className="w-4 h-4" />
                Оформить PRO
                <ArrowRight className="w-4 h-4" />
              </a>
            </Button>
            <Button asChild variant="outline" size="lg">
              <a href="/pricing">
                Узнать больше
              </a>
            </Button>
          </div>
          
          <div className="mt-4 flex items-center justify-center gap-2 text-xs text-muted-foreground">
            <Badge variant="secondary" className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0">
              PRO
            </Badge>
            <span>Доступ к {totalCount} аналогам</span>
          </div>
        </div>
      </motion.div>
    </div>
  )
}