import type { CatalogItem, Attribute } from './types'

/**
 * Нормализует значение атрибута для сравнения
 */
export function normalizeAttributeValue(value: string, dataType: string): string {
  let normalized = value.toLowerCase().trim()
  
  // Удаляем единицы измерения для чисел
  if (dataType === 'NUMBER' || dataType === 'DECIMAL') {
    normalized = normalized.replace(/[^\d.,\-]/g, '')
  }
  
  return normalized
}

/**
 * Вычисляет коэффициент Жаккара (Jaccard similarity)
 */
export function calculateJaccardSimilarity(set1: Set<string>, set2: Set<string>): number {
  const intersection = new Set([...set1].filter(x => set2.has(x)))
  const union = new Set([...set1, ...set2])
  
  if (union.size === 0) return 0
  
  return intersection.size / union.size
}

/**
 * Вычисляет схожесть атрибутов между двумя наборами
 */
export function calculateAttributeSimilarity(
  item1Attrs: Attribute[],
  item2Attrs: Attribute[]
): number {
  if (item1Attrs.length === 0 && item2Attrs.length === 0) return 1
  if (item1Attrs.length === 0 || item2Attrs.length === 0) return 0
  
  // Создаем мапы для быстрого поиска
  const attrs1Map = new Map(
    item1Attrs.map(attr => [attr.templateId, attr])
  )
  const attrs2Map = new Map(
    item2Attrs.map(attr => [attr.templateId, attr])
  )
  
  // Находим общие шаблоны атрибутов
  const commonTemplateIds = new Set(
    [...attrs1Map.keys()].filter(id => attrs2Map.has(id))
  )
  
  if (commonTemplateIds.size === 0) return 0
  
  let matchCount = 0
  let totalCount = commonTemplateIds.size
  
  // Сравниваем значения общих атрибутов
  for (const templateId of commonTemplateIds) {
    const attr1 = attrs1Map.get(templateId)!
    const attr2 = attrs2Map.get(templateId)!
    
    const value1 = normalizeAttributeValue(attr1.value, attr1.template?.dataType || 'STRING')
    const value2 = normalizeAttributeValue(attr2.value, attr2.template?.dataType || 'STRING')
    
    if (value1 === value2) {
      matchCount++
    } else if (attr1.template?.dataType === 'NUMBER' || attr1.template?.dataType === 'DECIMAL') {
      // Для чисел используем толерантность 10%
      const num1 = parseFloat(value1)
      const num2 = parseFloat(value2)
      
      if (!isNaN(num1) && !isNaN(num2)) {
        const tolerance = Math.abs(num1) * 0.1
        if (Math.abs(num1 - num2) <= tolerance) {
          matchCount += 0.5 // Частичное совпадение
        }
      }
    }
  }
  
  // Учитываем покрытие атрибутов (сколько общих атрибутов из всех возможных)
  const allTemplateIds = new Set([...attrs1Map.keys(), ...attrs2Map.keys()])
  const coverageScore = commonTemplateIds.size / allTemplateIds.size
  
  // Комбинируем оценку совпадений и покрытия
  const matchScore = matchCount / totalCount
  
  return (matchScore * 0.7) + (coverageScore * 0.3)
}

/**
 * Оценивает схожесть кандидата с целевым артикулом
 */
export function scoreSimilarItem(
  targetItem: CatalogItem,
  candidateItem: CatalogItem
): { score: number; matchingAttributes: number; totalAttributes: number } {
  const score = calculateAttributeSimilarity(
    targetItem.attributes || [],
    candidateItem.attributes || []
  )
  
  // Подсчитываем совпадающие атрибуты
  const targetTemplateIds = new Set(
    (targetItem.attributes || []).map(attr => attr.templateId)
  )
  const candidateTemplateIds = new Set(
    (candidateItem.attributes || []).map(attr => attr.templateId)
  )
  
  const commonTemplateIds = new Set(
    [...targetTemplateIds].filter(id => candidateTemplateIds.has(id))
  )
  
  return {
    score,
    matchingAttributes: commonTemplateIds.size,
    totalAttributes: new Set([...targetTemplateIds, ...candidateTemplateIds]).size
  }
}

/**
 * Находит похожие артикулы
 */
export function findSimilarItems(
  targetItem: CatalogItem,
  candidateItems: CatalogItem[],
  maxResults: number = 5
): Array<{ item: CatalogItem; score: number; matchingAttributes: number }> {
  // Исключаем артикулы из той же эталонной группы (они являются альтернативами, а не похожими)
  const targetPartIds = new Set(
    (targetItem.applicabilities || []).map(app => app.partId)
  )
  
  const filtered = candidateItems.filter(candidate => {
    // Исключаем сам артикул
    if (candidate.id === targetItem.id) return false
    
    // Исключаем артикулы из той же эталонной группы
    const candidatePartIds = (candidate.applicabilities || []).map(app => app.partId)
    const hasCommonPart = candidatePartIds.some(id => targetPartIds.has(id))
    
    return !hasCommonPart
  })
  
  // Оцениваем и сортируем
  const scored = filtered
    .map(candidate => ({
      item: candidate,
      ...scoreSimilarItem(targetItem, candidate)
    }))
    .filter(result => result.score >= 0.3) // Минимальный порог схожести
    .sort((a, b) => b.score - a.score)
    .slice(0, maxResults)
  
  return scored
}

/**
 * Фильтрует артикулы по брендам
 */
export function filterByBrand(
  items: CatalogItem[],
  excludeBrandIds: number[]
): CatalogItem[] {
  const excludeSet = new Set(excludeBrandIds)
  return items.filter(item => !excludeSet.has(item.brandId))
}

/**
 * Фильтрует по минимальному порогу схожести
 */
export function filterByMinimumScore(
  items: Array<{ item: CatalogItem; score: number }>,
  minScore: number = 0.3
): Array<{ item: CatalogItem; score: number }> {
  return items.filter(result => result.score >= minScore)
}

