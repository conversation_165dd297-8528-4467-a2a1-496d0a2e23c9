"use client";

import {
  CheckCircle2,
  Check,
  CheckCircle,
  XCircle,
  AlertCircle,
  HelpCircle,
  ArrowRight,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ModernCard, ModernCardContent, ModernCardHeader, ModernCardTitle } from "@/components/ui/modern-card";
import {
  compareAttributeSets,
  calculateMatchScore,
  getMatchQuality,
  getComparisonStatusColor,
  getComparisonStatusIcon,
  getComparisonStatusLabel,
  type ComparisonResult,
  type ComparisonStatus,
} from "@/lib/comparators";
import { formatAttributeValue, formatNumber } from "@/lib/formatters";
import { useIsMobile } from '@/hooks/useMediaQuery';
// ============================================================================
// Types
// ============================================================================

type AttributeTemplate = {
  id: number;
  name: string;
  title: string;
  dataType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'JSON';
  unit: string | null;
  tolerance: number | null;
  synonymGroups: Array<{
    id: number;
    name: string;
    notes: string | null;
    compatibilityLevel: string;
    canonicalValue: string | null;
    synonyms: Array<{ id: number; value: string }>;
  }>;
};

type PartAttribute = {
  id: number;
  templateId: number;
  value: string;
  template: AttributeTemplate;
};

type CatalogItemAttribute = {
  id: number;
  templateId: number;
  value: string;
  template: AttributeTemplate;
};

export interface AttributeComparisonProps {
  partAttributes: PartAttribute[];
  itemAttributes: CatalogItemAttribute[];
  layout?: 'table' | 'cards' | 'sideBySide';
  showMatchScore?: boolean;
  showOnlyDifferences?: boolean;
  highlightDifferences?: boolean;
  className?: string;
}

// ============================================================================
// Helper Components
// ============================================================================

function StatusIcon({ status }: { status: ComparisonStatus }) {
  const iconName = getComparisonStatusIcon(status);
  const colorClass = getComparisonStatusColor(status);
  const Icon = {
    CheckCircle2,
    Check,
    CheckCircle,
    XCircle,
    AlertCircle,
    HelpCircle,
  }[iconName];

  return <Icon className={`h-4 w-4 ${colorClass.split(' ')[0]}`} />;
}

function MatchScoreCard({ comparisons }: { comparisons: ComparisonResult[] }) {
  const score = calculateMatchScore(comparisons);
  const quality = getMatchQuality(score);

  const exactMatches = comparisons.filter((c) => c.status === 'exact').length;
  const nearMatches = comparisons.filter((c) => c.status === 'near' || c.status === 'legacy').length;
  const mismatches = comparisons.filter((c) => c.status === 'mismatch').length;

  const qualityColors = {
    excellent: 'text-green-600 bg-green-50',
    good: 'text-blue-600 bg-blue-50',
    fair: 'text-yellow-600 bg-yellow-50',
    poor: 'text-red-600 bg-red-50',
  };

  const qualityLabels = {
    excellent: 'Отличное',
    good: 'Хорошее',
    fair: 'Удовлетворительное',
    poor: 'Плохое',
  };

  return (
    <ModernCard className="mb-4">
      <ModernCardHeader>
        <ModernCardTitle className="text-lg">Общая совместимость</ModernCardTitle>
      </ModernCardHeader>
      <ModernCardContent>
        <div className="space-y-3">
          {/* Score and quality */}
          <div className="flex items-center justify-between">
            <div>
              <div className="text-3xl font-bold">{score}%</div>
              <Badge className={qualityColors[quality]}>
                {qualityLabels[quality]} соответствие
              </Badge>
            </div>
            <div className="w-32 h-2 bg-muted rounded-full overflow-hidden">
              <div
                className={`h-full ${
                  quality === 'excellent'
                    ? 'bg-green-500'
                    : quality === 'good'
                    ? 'bg-blue-500'
                    : quality === 'fair'
                    ? 'bg-yellow-500'
                    : 'bg-red-500'
                }`}
                style={{ width: `${score}%` }}
              />
            </div>
          </div>

          {/* Breakdown */}
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-1">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <span>{exactMatches} точных</span>
            </div>
            <div className="flex items-center gap-1">
              <Check className="h-4 w-4 text-blue-500" />
              <span>{nearMatches} близких</span>
            </div>
            <div className="flex items-center gap-1">
              <XCircle className="h-4 w-4 text-red-500" />
              <span>{mismatches} несовпадений</span>
            </div>
          </div>
        </div>
      </ModernCardContent>
    </ModernCard>
  );
}

// ============================================================================
// Layout Components
// ============================================================================

interface ComparisonRowData {
  partAttr: PartAttribute;
  itemAttr?: CatalogItemAttribute;
  comparison: ComparisonResult;
}

function TableLayout({
  rows,
  highlightDifferences,
}: {
  rows: ComparisonRowData[];
  highlightDifferences: boolean;
}) {
  return (
    <div className="border rounded-lg overflow-hidden">
      <table className="w-full">
        <thead className="bg-muted/50">
          <tr>
            <th className="text-left p-3 font-semibold text-sm">Атрибут</th>
            <th className="text-left p-3 font-semibold text-sm">Эталон (Part)</th>
            <th className="text-left p-3 font-semibold text-sm">Артикул (Item)</th>
            <th className="text-center p-3 font-semibold text-sm">Статус</th>
          </tr>
        </thead>
        <tbody>
          {rows.map((row) => {
            const partValue = formatAttributeValue(row.partAttr.value, row.partAttr.template.unit);
            const itemValue = row.itemAttr
              ? formatAttributeValue(row.itemAttr.value, row.itemAttr.template.unit)
              : '—';

            const isDifferent = row.comparison.status !== 'exact';
            const rowClass = highlightDifferences && isDifferent ? 'bg-yellow-50/50' : '';

            return (
              <tr key={row.partAttr.id} className={`border-t ${rowClass}`}>
                <td className="p-3 text-sm font-medium">{row.partAttr.template.title}</td>
                <td className="p-3 text-sm">{partValue}</td>
                <td className="p-3 text-sm">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span
                          className={
                            highlightDifferences && isDifferent ? 'font-bold text-orange-700' : ''
                          }
                        >
                          {itemValue}
                        </span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="text-xs">{row.comparison.notes}</p>
                        {row.comparison.difference !== undefined && (
                          <p className="text-xs font-mono mt-1">
                            Δ: {row.comparison.difference > 0 ? '+' : ''}
                            {formatNumber(row.comparison.difference)}{' '}
                            {row.partAttr.template.unit}
                          </p>
                        )}
                        {row.comparison.matchedSynonymGroup && (
                          <p className="text-xs mt-1">
                            Группа: {row.comparison.matchedSynonymGroup}
                          </p>
                        )}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </td>
                <td className="p-3 text-center">
                  <div className="flex items-center justify-center gap-2">
                    <StatusIcon status={row.comparison.status} />
                    <Badge variant="outline" className="text-xs">
                      {getComparisonStatusLabel(row.comparison.status)}
                    </Badge>
                  </div>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}

function CardsLayout({
  rows,
  highlightDifferences,
}: {
  rows: ComparisonRowData[];
  highlightDifferences: boolean;
}) {
  return (
    <div className="space-y-3">
      {rows.map((row) => {
        const partValue = formatAttributeValue(row.partAttr.value, row.partAttr.template.unit);
        const itemValue = row.itemAttr
          ? formatAttributeValue(row.itemAttr.value, row.itemAttr.template.unit)
          : '—';

        const isDifferent = row.comparison.status !== 'exact';

        return (
          <ModernCard
            key={row.partAttr.id}
            variant="default"
            className={highlightDifferences && isDifferent ? 'border-orange-300 bg-orange-50/30' : ''}
          >
            <ModernCardContent className="p-3 md:p-4">
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-semibold text-sm md:text-base">{row.partAttr.template.title}</h4>
                <Badge variant="outline" className="text-xs flex items-center gap-1">
                  <StatusIcon status={row.comparison.status} />
                  {getComparisonStatusLabel(row.comparison.status)}
                </Badge>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm md:text-base">
                <div>
                  <div className="text-xs text-muted-foreground mb-1">Эталон</div>
                  <div className="font-medium">{partValue}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground mb-1">Артикул</div>
                  <div
                    className={
                      highlightDifferences && isDifferent
                        ? 'font-bold text-orange-700'
                        : 'font-medium'
                    }
                  >
                    {itemValue}
                  </div>
                </div>
              </div>
              {row.comparison.notes && (
                <p className="text-xs text-muted-foreground mt-2 italic">{row.comparison.notes}</p>
              )}
            </ModernCardContent>
          </ModernCard>
        );
      })}
    </div>
  );
}

function SideBySideLayout({
  rows,
  highlightDifferences,
}: {
  rows: ComparisonRowData[];
  highlightDifferences: boolean;
}) {
  return (
    <div className="space-y-2">
      {rows.map((row) => {
        const partValue = formatAttributeValue(row.partAttr.value, row.partAttr.template.unit);
        const itemValue = row.itemAttr
          ? formatAttributeValue(row.itemAttr.value, row.itemAttr.template.unit)
          : '—';

        const isDifferent = row.comparison.status !== 'exact';

        return (
          <div
            key={row.partAttr.id}
            className={`flex items-center gap-4 p-3 rounded border ${
              highlightDifferences && isDifferent ? 'bg-orange-50/30 border-orange-300' : 'bg-muted/20'
            }`}
          >
            <div className="flex-1">
              <div className="text-xs text-muted-foreground mb-1">{row.partAttr.template.title}</div>
              <div className="font-medium text-sm">{partValue}</div>
            </div>
            <div className="flex items-center gap-2">
              <ArrowRight className="h-4 w-4 text-muted-foreground" />
              <StatusIcon status={row.comparison.status} />
            </div>
            <div className="flex-1">
              <div className="text-xs text-muted-foreground mb-1">Артикул</div>
              <div
                className={`font-medium text-sm ${
                  highlightDifferences && isDifferent ? 'font-bold text-orange-700' : ''
                }`}
              >
                {itemValue}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}

// ============================================================================
// Main Component
// ============================================================================

export default function AttributeComparison({
  partAttributes,
  itemAttributes,
  layout = 'table',
  showMatchScore = false,
  showOnlyDifferences = false,
  highlightDifferences = false,
  className = '',
}: AttributeComparisonProps) {
  // Perform comparison
  const comparisons = compareAttributeSets(partAttributes, itemAttributes);

  // Build rows
  let rows: ComparisonRowData[] = partAttributes.map((partAttr, index) => {
    const itemAttr = itemAttributes.find((attr) => attr.templateId === partAttr.templateId);
    return {
      partAttr,
      itemAttr,
      comparison: comparisons[index],
    };
  });

  // Filter by differences if needed
  if (showOnlyDifferences) {
    rows = rows.filter((row) => row.comparison.status !== 'exact');
  }

  const isMobile = useIsMobile();
  const effectiveLayout = isMobile ? 'cards' : layout;

  return (
    <div className={className}>
      {showMatchScore && <MatchScoreCard comparisons={comparisons} />}

      {showOnlyDifferences && rows.length < partAttributes.length && (
        <div className="mb-3 text-sm text-muted-foreground">
          Показано {rows.length} из {partAttributes.length} атрибутов (только различия)
        </div>
      )}

      {rows.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          {showOnlyDifferences
            ? 'Все атрибуты совпадают'
            : 'Нет атрибутов для отображения'}
        </div>
      ) : effectiveLayout === 'table' ? (
        <TableLayout rows={rows} highlightDifferences={highlightDifferences} />
      ) : effectiveLayout === 'cards' ? (
        <CardsLayout rows={rows} highlightDifferences={highlightDifferences} />
      ) : (
        <SideBySideLayout rows={rows} highlightDifferences={highlightDifferences} />
      )}
    </div>
  );
}

