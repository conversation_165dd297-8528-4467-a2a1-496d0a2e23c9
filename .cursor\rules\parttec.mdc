---
alwaysApply: true
---
- Категорически нельзя использовать @apply атрибут в <style>. Пиши все классы в сами теги.
- Vue компоненты импортируются как Astro islands  компоненты с атрибутом client:load или client:only="vue" 
- React компоненты импортируются как Astro islands  компоненты с атрибутом client:load или client:only="react" 
- Не запускай  "Open in Browser", предлагай проверить мне.
- Следуй кристально чистой архитектуре проекта.
- Volt UI как библиотека компонентов для /cpanel (admin panel)
- shadcn/ui для site (клиентская часть)
- МАКСИМАЛЬНО ПРОСТОЙ КОД, без лишнего функционала.
- Мы делаем максимально гибкую систему, которая будет легко расширяться и поддерживаться. 
- По возможности реализовывай автогенерацию форм на базе Zod схем.
- Используй по максимуму где это возможно уже сгенерированные Zenstack:  Zod схемы и tRPC роуты.
- Мы не создаем демо код, тестовый код! Мы сразу приступаем к реальному функционалу!
- Учти что категорически запрещено дублировать функционал, везде и всегда должны быть единые компоненты! 
- Категорически запрещено нарушать принцип DRY 
- Категорически запрещено нарушать принцип SOLID
- Категорически запрещено нарушать принцип KISS
- Lucide-vue / Lucide react иконки
- Volt UI (unstyled primevue) + Tailwind 4 для админ панели (cpanel) и shadcn/ui + tailwind 4 для /site (клиентская часть)
- Роутинг и навигация Astro View Transition (метод navigate(path:string)) https://docs.astro.build/en/reference/modules/astro-transitions/#navigate
- Запрещено использовать Primevue Icons вида <Icon name="pi pi-pencil" class="w-3 h-3" />, вместо этого используй иконки Lucide!
- Тип any КАТЕГОРИЧЕСКИ, КРИТИЧЕСКИ ЗАПРЕЩЕН! 
- КРИТИЧЕСКИ ЗАПРЕЩЕНО ДЕЛАТЬ ЗАГЛУШКИ, МОКИ, ДЕМО КОД, ТЕСТОВЫЙ КОД.