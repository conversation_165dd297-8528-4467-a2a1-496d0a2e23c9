"use client"

import { BulkSelectionProvider, useBulkSelection } from "@/contexts/BulkSelectionContext"
import { BulkActionsToolbar } from "../BulkActionsToolbar"
import { SearchInsights } from "../insights"
import { Checkbox } from "@/components/ui/checkbox"
import { useCatalogGlobalState } from "@/lib/catalog-state"
import { BarChart3, Grid3X3, List, Database } from "lucide-react"
import { ModernButton } from "@/components/ui/modern-button"
import { ModernCard, ModernCardContent } from "@/components/ui/modern-card"
import { useCatalogSearch } from "../pro/useCatalogSearch"
import { MobileFiltersDrawer } from "../pro/MobileFiltersDrawer"
import { ProUpsellBanner } from "@/components/subscription/ProUpsellBanner"
import { PartCard } from '../PartCard'
import CatalogItemCard from '../cards/CatalogItemCard'
import { LoadingState } from '@/components/shared/LoadingState'
import { analytics } from "@/lib/analytics"

export default function ResultsContent() {
  return (
    <BulkSelectionProvider>
      <ResultsContentInner />
    </BulkSelectionProvider>
  )
}

function ResultsContentInner() {
  const { results, isLoading, totalCount, clearFilters, isPro, resultType, metadata } = useCatalogSearch()
  const { viewMode, setViewMode } = useCatalogGlobalState()
  const { selectedIds, selectAll, deselectAll, selectedCount, hasSelection, selectItem, deselectItem } = useBulkSelection()

  // Обработчики для view mode с аналитикой
  const handleViewModeChange = (mode: 'detailed' | 'grid' | 'table') => {
    setViewMode(mode)
    analytics.viewModeChanged(mode)
  }
  
  return (
    <div className="flex-1 overflow-y-auto">
      <div className="container max-w-none p-4">
        {/* Search Insights панель */}
        {!isLoading && results.length > 0 && (
          <SearchInsights
            results={results}
            metadata={metadata}
            isPro={isPro}
            className="mb-4"
          />
        )}
        
        {/* Bulk Actions Toolbar */}
        {hasSelection && (
          <BulkActionsToolbar
            selectedItems={results.filter((r: { id: number }) => selectedIds.has(r.id))}
            isPro={isPro}
            onClearSelection={deselectAll}
            className="mb-4"
          />
        )}
        
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Checkbox
              checked={selectedCount === results.length && results.length > 0}
              onCheckedChange={(checked) => {
                if (checked) {
                  selectAll(results.map((r: { id: number }) => r.id))
                  analytics.bulkSelectionStarted(results.length, isPro ? 'part' : 'catalogItem')
                } else {
                  deselectAll()
                }
              }}
              aria-label="Выбрать все"
            />
            <h2 className="text-xl font-bold tracking-tight">
              Всего {isPro ? 'групп' : 'артикулов'}: {totalCount}
              {hasSelection && ` (${selectedCount} выбрано)`}
            </h2>
          </div>

          <div className="flex items-center gap-2">
            {/* Мобильный фильтр drawer - использует FilterSystem context */}
            <MobileFiltersDrawer />

            <div className="flex items-center gap-1 p-1 bg-muted/50 rounded border border-border/40">
              <ModernButton 
                variant={viewMode === "detailed" ? "default" : "ghost"} 
                size="sm" 
                onClick={() => handleViewModeChange("detailed")} 
                className="h-7 w-7 p-0"
              >
                <List className="h-3 w-3" />
              </ModernButton>
              <ModernButton 
                variant={viewMode === "grid" ? "default" : "ghost"} 
                size="sm" 
                onClick={() => handleViewModeChange("grid")} 
                className="h-7 w-7 p-0"
              >
                <Grid3X3 className="h-3 w-3" />
              </ModernButton>
              <ModernButton 
                variant={viewMode === "table" ? "default" : "ghost"} 
                size="sm" 
                onClick={() => handleViewModeChange("table")} 
                className="h-7 w-7 p-0"
              >
                <BarChart3 className="h-3 w-3" />
              </ModernButton>
            </div>
          </div>
        </div>

        {isLoading && <LoadingState variant="card" count={5} />}

        {!isLoading && results.length === 0 ? (
          <ModernCard variant="elevated" className="text-center py-12 border-2 border-dashed border-border-strong">
            <ModernCardContent>
              <div className="flex flex-col items-center gap-3">
                <div className="h-12 w-12 rounded-full bg-muted/50 flex items-center justify-center">
                  <Database className="h-6 w-6 text-muted-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-1">
                    {isPro ? 'Группы не найдены' : 'Артикулы не найдены'}
                  </h3>
                  <p className="text-muted-foreground text-sm max-w-md">
                    Попробуйте изменить критерии поиска или сбросить фильтры.
                  </p>
                </div>
                <ModernButton variant="outline" onClick={clearFilters} size="sm">
                  Очистить фильтры
                </ModernButton>
              </div>
            </ModernCardContent>
          </ModernCard>
        ) : (
          <div className="space-y-3 animate-fade-in">
            {isPro ? (
              // Рендерим Part карточки для PRO пользователей
              results?.map((part: unknown, index: number) => (
                <PartCard
                  key={(part as { id: number }).id}
                  part={part}
                  animationDelay={index * 30}
                  showCheckbox={true}
                  isSelected={selectedIds.has((part as { id: number }).id)}
                  onSelectionChange={(selected) => {
                    if (selected) {
                      selectItem((part as { id: number }).id)
                    } else {
                      deselectItem((part as { id: number }).id)
                    }
                  }}
                />
              ))
            ) : (
              // Рендерим CatalogItem карточки для FREE пользователей с ProUpsellBanner
              results?.map((item: unknown, index: number) => (
                <div key={(item as { id: number }).id}>
                  <CatalogItemCard
                    item={item}
                    layout="detailed"
                    animationDelay={index * 30}
                    showProUpsell={true}
                    showCheckbox={true}
                    isSelected={selectedIds.has((item as { id: number }).id)}
                    onSelectionChange={(selected) => {
                      if (selected) {
                        selectItem((item as { id: number }).id)
                      } else {
                        deselectItem((item as { id: number }).id)
                      }
                    }}
                  />
                  {(index + 1) % 4 === 0 && index !== results.length - 1 && (
                    <ProUpsellBanner
                      variant="compact"
                      title="Хотите увидеть группы взаимозаменяемости?"
                      description="Оформите PRO подписку для доступа к эталонным группам Part и расширенному поиску"
                      features={[
                        "Поиск по группам взаимозаменяемости",
                        "Сравнение аналогов по эталонным атрибутам",
                        "AI-ассистент для умного поиска",
                        "Экспорт данных в Excel/CSV"
                      ]}
                      ctaText="Узнать больше о PRO"
                    />
                  )}
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  )
}

