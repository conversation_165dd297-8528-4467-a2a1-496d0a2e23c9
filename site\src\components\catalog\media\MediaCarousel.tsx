import { useState, useEffect, useRef } from 'react'
import { motion, type PanInfo } from 'motion/react'
import type { MediaAsset } from '@/lib/types'
import {
  isImage,
  isVideo,
  getMediaType,
  detectSwipe,
  formatFileSize,
} from './media-utils'
import { drawerSlideIn } from '@/lib/animation-variants'
import {
  Drawer,
  DrawerContent,
  DrawerTitle,
} from '@/components/ui/drawer'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  X,
  Download,
  Play,
  Pause,
  ChevronLeft,
  ChevronRight,
  FileText,
} from 'lucide-react'
import { cn } from '@/lib/utils'

export interface MediaCarouselProps {
  assets: MediaAsset[]
  initialIndex?: number
  isOpen: boolean
  onClose: () => void
  autoPlay?: boolean
  autoPlayInterval?: number
  showIndicators?: boolean
  className?: string
}

export default function MediaCarousel({
  assets,
  initialIndex = 0,
  isOpen,
  onClose,
  autoPlay = false,
  autoPlayInterval = 3000,
  showIndicators = true,
  className,
}: MediaCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex)
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const autoPlayTimerRef = useRef<NodeJS.Timeout>()
  const carouselRef = useRef<HTMLDivElement>(null)

  const currentAsset = assets[currentIndex]
  const currentType = currentAsset ? getMediaType(currentAsset.mimeType) : 'other'

  // Update initial index
  useEffect(() => {
    setCurrentIndex(initialIndex)
  }, [initialIndex])

  // Auto-play functionality
  useEffect(() => {
    if (!isOpen || !isAutoPlaying || assets.length <= 1) return

    autoPlayTimerRef.current = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % assets.length)
    }, autoPlayInterval)

    return () => {
      if (autoPlayTimerRef.current) {
        clearInterval(autoPlayTimerRef.current)
      }
    }
  }, [isOpen, isAutoPlaying, assets.length, autoPlayInterval])

  // Pause auto-play on user interaction
  const pauseAutoPlay = () => {
    if (autoPlayTimerRef.current) {
      clearInterval(autoPlayTimerRef.current)
    }
    setIsAutoPlaying(false)
  }

  // Resume auto-play after inactivity
  useEffect(() => {
    if (!autoPlay || isAutoPlaying) return

    const timer = setTimeout(() => {
      setIsAutoPlaying(true)
    }, 2000)

    return () => clearTimeout(timer)
  }, [currentIndex, autoPlay, isAutoPlaying])

  const goToPrevious = () => {
    pauseAutoPlay()
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }

  const goToNext = () => {
    pauseAutoPlay()
    if (currentIndex < assets.length - 1) {
      setCurrentIndex(currentIndex + 1)
    } else if (isAutoPlaying) {
      setCurrentIndex(0) // Loop back to first
    }
  }

  const goToIndex = (index: number) => {
    pauseAutoPlay()
    setCurrentIndex(index)
  }

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying)
  }

  const handleDownload = () => {
    if (!currentAsset) return

    const link = document.createElement('a')
    link.href = currentAsset.url
    link.download = currentAsset.fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleDragStart = (event: MouseEvent | TouchEvent | PointerEvent) => {
    setIsDragging(true)
    const point = 'touches' in event ? event.touches[0] : event as MouseEvent
    setDragStart({ x: point.clientX, y: point.clientY })
    pauseAutoPlay()
  }

  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    setIsDragging(false)

    const point = 'changedTouches' in event ? event.changedTouches[0] : event as MouseEvent
    const swipe = detectSwipe(dragStart.x, dragStart.y, point.clientX, point.clientY, 50)

    // Check velocity for fast swipes
    const velocityThreshold = 500
    const isFastSwipe = Math.abs(info.velocity.x) > velocityThreshold

    if (swipe === 'left' || (isFastSwipe && info.velocity.x < 0)) {
      goToNext()
    } else if (swipe === 'right' || (isFastSwipe && info.velocity.x > 0)) {
      goToPrevious()
    }
  }

  const handleTap = (e: React.MouseEvent) => {
    const rect = carouselRef.current?.getBoundingClientRect()
    if (!rect) return

    const clickX = e.clientX - rect.left
    const clickWidth = rect.width

    // Left third: previous, right third: next
    if (clickX < clickWidth / 3) {
      goToPrevious()
    } else if (clickX > (clickWidth * 2) / 3) {
      goToNext()
    }
  }

  if (!currentAsset) return null

  return (
    <Drawer open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DrawerContent className={cn('h-[90vh] bg-black/95', className)}>
        <DrawerTitle className="sr-only">Карусель медиафайлов</DrawerTitle>

        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-gradient-to-b from-black/60 to-transparent">
          <div className="flex items-center gap-3">
            <span className="text-white font-medium text-sm truncate max-w-[200px]">
              {currentAsset.fileName}
            </span>
            <Badge variant="secondary" className="shrink-0">
              {currentIndex + 1} / {assets.length}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            {autoPlay && (
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleAutoPlay}
                className="text-white hover:bg-white/20"
              >
                {isAutoPlaying ? (
                  <Pause className="w-4 h-4" />
                ) : (
                  <Play className="w-4 h-4" />
                )}
              </Button>
            )}
            <Button
              variant="ghost"
              size="icon"
              onClick={handleDownload}
              className="text-white hover:bg-white/20"
            >
              <Download className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-white hover:bg-white/20"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Main carousel */}
        <div
          ref={carouselRef}
          className="relative flex-1 flex items-center justify-center overflow-hidden"
          onClick={handleTap}
        >
          {/* Navigation buttons (tablet+) */}
          <div className="hidden sm:block">
            <button
              onClick={(e) => {
                e.stopPropagation()
                goToPrevious()
              }}
              disabled={currentIndex === 0}
              className={cn(
                'absolute left-4 z-10 p-3 rounded-full bg-black/50 text-white',
                'hover:bg-black/70 transition-colors disabled:opacity-30 disabled:cursor-not-allowed'
              )}
            >
              <ChevronLeft className="w-6 h-6" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                goToNext()
              }}
              disabled={currentIndex === assets.length - 1 && !isAutoPlaying}
              className={cn(
                'absolute right-4 z-10 p-3 rounded-full bg-black/50 text-white',
                'hover:bg-black/70 transition-colors disabled:opacity-30 disabled:cursor-not-allowed'
              )}
            >
              <ChevronRight className="w-6 h-6" />
            </button>
          </div>

          {/* Image/Media content */}
          <motion.div
            drag="x"
            dragConstraints={{ left: 0, right: 0 }}
            dragElastic={0.2}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            className="w-full h-full flex items-center justify-center px-4"
            animate={{ opacity: isDragging ? 0.7 : 1 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          >
            {currentType === 'image' && (
              <img
                src={currentAsset.url}
                alt={currentAsset.fileName}
                className="max-w-full max-h-[70vh] object-contain"
                draggable={false}
              />
            )}

            {currentType === 'video' && (
              <video
                src={currentAsset.url}
                controls
                autoPlay={false}
                className="max-w-full max-h-[70vh]"
                onPlay={pauseAutoPlay}
              />
            )}

            {currentType === 'pdf' && (
              <div className="flex flex-col items-center gap-4">
                <FileText className="w-16 h-16 text-white" />
                <span className="text-white text-center">{currentAsset.fileName}</span>
                <Button
                  variant="secondary"
                  onClick={(e) => {
                    e.stopPropagation()
                    window.open(currentAsset.url, '_blank')
                  }}
                >
                  Открыть PDF
                </Button>
              </div>
            )}
          </motion.div>
        </div>

        {/* Footer */}
        <div className="p-4 bg-gradient-to-t from-black/60 to-transparent">
          {/* Indicators */}
          {showIndicators && assets.length > 1 && (
            <div className="flex items-center justify-center gap-2 mb-3">
              {assets.map((asset, index) => (
                <button
                  key={asset.id}
                  onClick={() => goToIndex(index)}
                  className={cn(
                    'w-2 h-2 rounded-full transition-all',
                    index === currentIndex
                      ? 'bg-primary w-6'
                      : 'bg-white/40 hover:bg-white/60'
                  )}
                  aria-label={`Перейти к изображению ${index + 1}`}
                />
              ))}
            </div>
          )}

          {/* File info */}
          <div className="flex items-center justify-center gap-3 text-sm text-white/60">
            <span>{getMediaType(currentAsset.mimeType).toUpperCase()}</span>
            {currentAsset.fileSize && (
              <>
                <span>•</span>
                <span>{formatFileSize(currentAsset.fileSize)}</span>
              </>
            )}
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  )
}

