/**
 * FilterPresets - UI компонент для управления пресетами фильтров
 * Поддерживает два варианта: dropdown (компактный) и panel (полный UI)
 */

import React, { useState } from 'react';
import { useFilterSystem } from './FilterSystem';
import { formatRelativeTime } from '@/lib/formatters';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Check, Star, Trash2, Save, ChevronDown, Info } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import type { FilterPreset } from '@/types/filters';

// ============================================================================
// Types
// ============================================================================

export interface FilterPresetsProps {
  variant?: 'dropdown' | 'panel';
  className?: string;
}

// ============================================================================
// Component
// ============================================================================

export default function FilterPresets({
  variant = 'dropdown',
  className = '',
}: FilterPresetsProps): React.ReactElement {
  const {
    presets,
    activePreset,
    isDirty,
    activeFiltersCount,
    applyPreset,
    deletePreset,
    saveCurrentAsPreset,
    setAsDefaultPreset,
  } = useFilterSystem();

  // Локальное состояние для диалогов
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [presetToDelete, setPresetToDelete] = useState<FilterPreset | null>(null);
  const [newPresetName, setNewPresetName] = useState('');
  const [newPresetDescription, setNewPresetDescription] = useState('');
  const [makeDefault, setMakeDefault] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);

  // ============================================================================
  // Handlers
  // ============================================================================

  const handleSavePreset = (): void => {
    setSaveError(null);

    if (!newPresetName.trim()) {
      setSaveError('Введите название пресета');
      return;
    }

    try {
      saveCurrentAsPreset(newPresetName, newPresetDescription || undefined);

      if (makeDefault) {
        // Получаем только что созданный пресет и делаем его дефолтным
        const savedPreset = presets.find((p) => p.name === newPresetName);
        if (savedPreset) {
          setAsDefaultPreset(savedPreset.id);
        }
      }

      // Сброс состояния
      setShowSaveDialog(false);
      setNewPresetName('');
      setNewPresetDescription('');
      setMakeDefault(false);
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : 'Не удалось сохранить пресет');
    }
  };

  const handleDeletePreset = (): void => {
    if (!presetToDelete) return;

    try {
      deletePreset(presetToDelete.id);
      setShowDeleteDialog(false);
      setPresetToDelete(null);
    } catch (error) {
      console.error('Failed to delete preset:', error);
    }
  };

  const confirmDelete = (preset: FilterPreset): void => {
    setPresetToDelete(preset);
    setShowDeleteDialog(true);
  };

  const handleSetDefault = (preset: FilterPreset): void => {
    try {
      setAsDefaultPreset(preset.id);
    } catch (error) {
      console.error('Failed to set default preset:', error);
    }
  };

  // ============================================================================
  // Render - Dropdown Variant
  // ============================================================================

  if (variant === 'dropdown') {
    return (
      <>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className={`justify-between ${className}`}>
              <span className="truncate">
                {activePreset ? activePreset.name : 'Фильтры'}
              </span>
              <div className="flex items-center gap-2 ml-2">
                {activeFiltersCount > 0 && (
                  <Badge variant="secondary" className="text-xs h-5 min-w-5 px-1.5">
                    {activeFiltersCount}
                  </Badge>
                )}
                {isDirty && (
                  <Badge variant="outline" className="text-xs h-5 px-1.5">
                    *
                  </Badge>
                )}
                <ChevronDown className="h-4 w-4 shrink-0" />
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-64">
            {presets.length > 0 ? (
              <>
                {presets.map((preset) => (
                  <DropdownMenuItem
                    key={preset.id}
                    onClick={() => applyPreset(preset)}
                    className="flex items-center justify-between gap-2"
                  >
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      {preset.id === activePreset?.id && (
                        <Check className="h-3 w-3 shrink-0" />
                      )}
                      {preset.isDefault && <Star className="h-3 w-3 shrink-0 text-yellow-500" />}
                      <span className="truncate">{preset.name}</span>
                    </div>
                  </DropdownMenuItem>
                ))}
                <DropdownMenuSeparator />
              </>
            ) : (
              <div className="px-2 py-4 text-sm text-muted-foreground text-center">
                Нет сохраненных фильтров
              </div>
            )}
            <DropdownMenuItem onClick={() => setShowSaveDialog(true)}>
              <Save className="h-3 w-3 mr-2" />
              Сохранить текущие
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Dialogs */}
        <SavePresetDialog
          open={showSaveDialog}
          onOpenChange={setShowSaveDialog}
          name={newPresetName}
          setName={setNewPresetName}
          description={newPresetDescription}
          setDescription={setNewPresetDescription}
          makeDefault={makeDefault}
          setMakeDefault={setMakeDefault}
          error={saveError}
          onSave={handleSavePreset}
        />
      </>
    );
  }

  // ============================================================================
  // Render - Panel Variant
  // ============================================================================

  return (
    <>
      <div className={`space-y-3 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h3 className="text-sm font-medium">Сохраненные фильтры</h3>
            {presets.length > 0 && (
              <Badge variant="secondary" className="text-xs h-5 px-1.5">
                {presets.length}
              </Badge>
            )}
          </div>
        </div>

        {presets.length > 0 ? (
          <div className="space-y-2">
            {presets.map((preset) => (
              <div
                key={preset.id}
                className={`border rounded-lg p-3 space-y-2 transition-colors ${
                  preset.id === activePreset?.id
                    ? 'border-primary bg-accent/50'
                    : 'border-border hover:border-accent-foreground/20'
                }`}
              >
                <div className="flex items-start justify-between gap-2">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    {preset.isDefault && (
                      <Star className="h-3.5 w-3.5 shrink-0 text-yellow-500 fill-yellow-500" />
                    )}
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium truncate">{preset.name}</h4>
                      {preset.description && (
                        <p className="text-xs text-muted-foreground line-clamp-2 mt-0.5">
                          {preset.description}
                        </p>
                      )}
                    </div>
                  </div>
                  <PresetDetailsPopover preset={preset} />
                </div>

                <div className="flex items-center justify-between gap-2">
                  <span className="text-xs text-muted-foreground">
                    {formatRelativeTime(preset.updatedAt)}
                  </span>
                  <div className="flex items-center gap-1">
                    {preset.id === activePreset?.id && (
                      <Badge variant="default" className="text-xs h-5 px-1.5">
                        Активен
                      </Badge>
                    )}
                    {preset.id !== activePreset?.id && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 px-2 text-xs"
                        onClick={() => applyPreset(preset)}
                      >
                        Применить
                      </Button>
                    )}
                    {!preset.isDefault && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 px-2"
                        onClick={() => handleSetDefault(preset)}
                        title="Сделать по умолчанию"
                      >
                        <Star className="h-3 w-3" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-7 px-2 text-destructive hover:text-destructive"
                      onClick={() => confirmDelete(preset)}
                      title="Удалить"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="border border-dashed rounded-lg p-6 text-center space-y-2">
            <p className="text-sm text-muted-foreground">
              Нет сохраненных фильтров
            </p>
            <p className="text-xs text-muted-foreground">
              Настройте фильтры и сохраните их для быстрого доступа
            </p>
          </div>
        )}

        <Button
          variant="outline"
          className="w-full"
          onClick={() => setShowSaveDialog(true)}
        >
          <Save className="h-3.5 w-3.5 mr-2" />
          Сохранить текущие фильтры
        </Button>
      </div>

      {/* Dialogs */}
      <SavePresetDialog
        open={showSaveDialog}
        onOpenChange={setShowSaveDialog}
        name={newPresetName}
        setName={setNewPresetName}
        description={newPresetDescription}
        setDescription={setNewPresetDescription}
        makeDefault={makeDefault}
        setMakeDefault={setMakeDefault}
        error={saveError}
        onSave={handleSavePreset}
      />

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Удалить пресет?</DialogTitle>
            <DialogDescription>
              Вы уверены, что хотите удалить пресет "{presetToDelete?.name}"? Это действие
              необратимо.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Отмена
            </Button>
            <Button variant="destructive" onClick={handleDeletePreset}>
              Удалить
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

// ============================================================================
// Sub-components
// ============================================================================

interface SavePresetDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  name: string;
  setName: (name: string) => void;
  description: string;
  setDescription: (description: string) => void;
  makeDefault: boolean;
  setMakeDefault: (makeDefault: boolean) => void;
  error: string | null;
  onSave: () => void;
}

function SavePresetDialog({
  open,
  onOpenChange,
  name,
  setName,
  description,
  setDescription,
  makeDefault,
  setMakeDefault,
  error,
  onSave,
}: SavePresetDialogProps): React.ReactElement {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Сохранить пресет фильтров</DialogTitle>
          <DialogDescription>
            Создайте новый пресет с текущими настройками фильтров
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="preset-name">
              Название <span className="text-destructive">*</span>
            </Label>
            <Input
              id="preset-name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Например, Подшипники 30-50 мм"
              maxLength={50}
            />
            <p className="text-xs text-muted-foreground">
              {name.length}/50 символов
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="preset-description">Описание (опционально)</Label>
            <Input
              id="preset-description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Краткое описание пресета"
              maxLength={200}
            />
            <p className="text-xs text-muted-foreground">
              {description.length}/200 символов
            </p>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="make-default"
              checked={makeDefault}
              onCheckedChange={(checked) => setMakeDefault(checked === true)}
            />
            <label
              htmlFor="make-default"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Сделать пресетом по умолчанию
            </label>
          </div>

          {error && (
            <div className="text-sm text-destructive bg-destructive/10 px-3 py-2 rounded-md">
              {error}
            </div>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Отмена
          </Button>
          <Button onClick={onSave}>Сохранить</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

function PresetDetailsPopover({ preset }: { preset: FilterPreset }): React.ReactElement {
  const categoriesCount = preset.filters.categoryIds?.length || 0;
  const brandsCount = preset.filters.brandIds?.length || 0;
  const attributesCount = Object.keys(preset.filters.attributeFilters || {}).length;

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" className="h-7 w-7 p-0 shrink-0">
          <Info className="h-3.5 w-3.5" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64" align="end">
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Детали пресета</h4>
          <div className="space-y-1.5 text-xs">
            {preset.filters.query && (
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs h-5 px-1.5">
                  Запрос
                </Badge>
                <span className="truncate">{preset.filters.query}</span>
              </div>
            )}
            {categoriesCount > 0 && (
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs h-5 px-1.5">
                  Категории
                </Badge>
                <span>{categoriesCount}</span>
              </div>
            )}
            {brandsCount > 0 && (
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs h-5 px-1.5">
                  Бренды
                </Badge>
                <span>{brandsCount}</span>
              </div>
            )}
            {attributesCount > 0 && (
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs h-5 px-1.5">
                  Атрибуты
                </Badge>
                <span>{attributesCount}</span>
              </div>
            )}
            {preset.filters.isOemOnly && (
              <Badge variant="secondary" className="text-xs h-5 px-1.5">
                Только OEM
              </Badge>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

