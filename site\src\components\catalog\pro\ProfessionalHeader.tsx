"use client"

import { useState, useEffect, useRef } from "react"
import { Search, Bolt, Save, Calculator, Download, Bell, Bot, Crown, Clock, Building2, Layers } from "lucide-react"
import { ModernButton } from "@/components/ui/modern-button"
import { ModernInput } from "@/components/ui/modern-input"
import { Badge } from "@/components/ui/badge"
import { AIAssistantTooltip } from "@/components/subscription"
import { SavedSearchesDropdown } from "../SavedSearchesDropdown"
import { getSearchHistory, addToSearchHistory } from "@/lib/saved-searches"
import type { CatalogSearchFilters } from "@/types/catalog"
import { Command, CommandEmpty, CommandGroup, CommandItem, CommandList } from "cmdk"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { analytics } from "@/lib/analytics"

interface ProfessionalHeaderProps {
  totalCount: number
  filteredCount: number
  searchQuery: string
  onSearchChange: (query: string) => void
  onOpenAI: () => void
  isPro?: boolean
  canUseAI?: boolean
  onSavedSearchApply?: (query: string, filters: CatalogSearchFilters) => void
}

export function ProfessionalHeader({ 
  totalCount, 
  filteredCount, 
  searchQuery, 
  onSearchChange, 
  onOpenAI, 
  isPro = false, 
  canUseAI = false,
  onSavedSearchApply
}: ProfessionalHeaderProps) {
  const [open, setOpen] = useState(false)
  const [suggestions, setSuggestions] = useState<Array<{ value: string; label: string; type: 'recent' | 'brand' | 'category' }>>([])
  const inputRef = useRef<HTMLInputElement>(null)
  
  // Загружаем suggestions при фокусе
  useEffect(() => {
    const loadSuggestions = () => {
      const history = getSearchHistory()
      const recentSuggestions = history.slice(0, 5).map(h => ({
        value: h.query,
        label: h.query,
        type: 'recent' as const
      }))
      
      setSuggestions(recentSuggestions)
    }
    
    if (open) {
      loadSuggestions()
    }
  }, [open])
  
  // Обработчик выбора suggestion
  const handleSelectSuggestion = (value: string, type: string) => {
    onSearchChange(value)
    setOpen(false)
    
    // Трекинг аналитики
    analytics.autocompleteSelected(type as 'recent' | 'brand' | 'category', value)
    
    // Добавляем в историю
    setTimeout(() => {
      addToSearchHistory(value, { query: value, categoryIds: [], brandIds: [], isOemOnly: false, attributeFilters: {} })
    }, 100)
  }
  
  // Обработчик изменения input
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchChange(e.target.value)
    if (e.target.value.length > 0) {
      setOpen(true)
    }
  }
  
  // Обработчик Enter
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && searchQuery.trim()) {
      addToSearchHistory(searchQuery, { query: searchQuery, categoryIds: [], brandIds: [], isOemOnly: false, attributeFilters: {} })
      setOpen(false)
    }
  }
  
  return (
    <header className="sticky top-14 z-40 w-full border-b border-border/40 bg-background/80 backdrop-blur-xl md:top-0">
      <div className="container flex h-16 max-w-screen-2xl items-center justify-between px-4 md:px-6">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <div className="flex bg-zinc-900 h-10 w-10 items-center justify-center rounded-xl shadow-lg">
              <Bolt className="h-5 w-5 text-primary-foreground" />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold tracking-tight">Каталог</h1>
            </div>
            {isPro && (
              <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0 text-xs">
                <Crown className="h-3 w-3 mr-1" />
                PRO
              </Badge>
            )}
          </div>
        </div>

        <div className="flex flex-1 items-center justify-center px-4 md:px-8 gap-2">
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <div className="relative w-full max-w-lg">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground pointer-events-none z-10" />
                <ModernInput
                  ref={inputRef}
                  placeholder="Поиск по артикулу, описанию, бренду..."
                  value={searchQuery}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyDown}
                  onFocus={() => setOpen(true)}
                  className="pl-10 pr-4"
                  variant="ghost"
                />
              </div>
            </PopoverTrigger>
            <PopoverContent 
              className="w-[var(--radix-popover-trigger-width)] p-0" 
              align="start"
              onOpenAutoFocus={(e) => e.preventDefault()}
            >
              <Command>
                <CommandList className="max-h-64">
                  <CommandEmpty className="py-6 text-center text-sm text-muted-foreground">
                    Нет результатов
                  </CommandEmpty>
                  {suggestions.length > 0 && (
                    <CommandGroup heading="Недавние поиски">
                      {suggestions.map((suggestion, index) => (
                        <CommandItem
                          key={index}
                          value={suggestion.value}
                          onSelect={() => handleSelectSuggestion(suggestion.value, suggestion.type)}
                          className="flex items-center gap-2 cursor-pointer"
                        >
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span className="flex-1">{suggestion.label}</span>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  )}
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
          
          {onSavedSearchApply && (
            <SavedSearchesDropdown
              onApply={(search) => onSavedSearchApply(search.query, search.filters)}
            />
          )}
        </div>

        <div className="flex items-center gap-3">
          <Badge variant="outline" className="hidden sm:flex text-xs">
            {filteredCount} / {totalCount}
          </Badge>

          <div className="flex items-center gap-2">
            {/* <ModernButton variant="ghost" size="icon" className="relative">
              <Bell className="h-4 w-4" />
              <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-destructive"></span>
            </ModernButton> */}


            {/* <ModernButton variant="outline" size="sm">
              <Download className="h-4 w-4" />
              <span className="hidden sm:inline">Экспорт</span>
            </ModernButton> */}

            {canUseAI ? (
              <ModernButton size="sm" onClick={onOpenAI} className="gap-2">
                <Bot className="h-4 w-4" />
                <span className="hidden sm:inline">AI Помощник</span>
              </ModernButton>
            ) : (
              <AIAssistantTooltip>
                <ModernButton 
                  size="sm" 
                  onClick={onOpenAI} 
                  className="gap-2"
                  disabled={!canUseAI}
                >
                  <Bot className="h-4 w-4" />
                  <span className="hidden sm:inline">AI Помощник</span>
                  {!canUseAI && (
                    <Badge className="ml-1 bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0 text-xs px-1.5 py-0">
                      <Crown className="h-2.5 w-2.5" />
                      PRO
                    </Badge>
                  )}
                </ModernButton>
              </AIAssistantTooltip>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}

