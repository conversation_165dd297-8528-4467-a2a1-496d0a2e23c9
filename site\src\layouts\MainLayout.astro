---
import "../styles/global.css";
import { AuthNav } from "@/components/navigation/AuthNav";
import { ThemeToggle } from "@/components/theme/ThemeToggle";
import { ViewTransitions } from "astro:transitions";

interface Props {
  title: string;
  description?: string;
}

const { title, description = "PartTec3 - Каталог взаимозаменяемых запчастей" } =
  Astro.props;
---

<html lang="ru" data-astro-transition-fallback="animate">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover"
    />
    <meta name="description" content={description} />
    
    <!-- Mobile web app capable -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />

    <!-- Prevent text size adjustment on orientation change -->
    <meta name="format-detection" content="telephone=no" />
    <title>{title} | PartTec3</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />

    <ViewTransitions />
    
    <!-- Custom View Transitions Animations -->
    <style is:global>
      ::view-transition-old(root),
      ::view-transition-new(root) {
        animation-duration: 0.3s;
        animation-timing-function: ease-in-out;
      }

      ::view-transition-old(root) {
        animation-name: fade-out;
      }

      ::view-transition-new(root) {
        animation-name: fade-in;
      }

      /* Smooth header/footer transitions */
      ::view-transition-old(header),
      ::view-transition-new(header),
      ::view-transition-old(footer),
      ::view-transition-new(footer) {
        animation: none;
      }

      @keyframes fade-out {
        from { opacity: 1; }
        to { opacity: 0; }
      }

      @keyframes fade-in {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      /* Respect reduced motion */
      @media (prefers-reduced-motion: reduce) {
        ::view-transition-old(root),
        ::view-transition-new(root) {
          animation: none;
        }
      }

      html {
        -webkit-tap-highlight-color: transparent;
        touch-action: manipulation;
      }

      body {
        overscroll-behavior-y: contain;
        -webkit-overflow-scrolling: touch;
      }
    </style>
  </head>
  <body class="min-h-screen bg-background font-sans antialiased">
    <!-- Скрипт для сохранения темы и управления view transitions -->
    <script is:inline>
      // Функция для применения темы
      function applyTheme(theme) {
        const root = document.documentElement;
        if (theme === 'dark') {
          root.classList.add('dark');
        } else {
          root.classList.remove('dark');
        }
      }

      // Функция для получения текущей темы
      function getStoredTheme() {
        if (typeof window === 'undefined') return 'light';
        const stored = localStorage.getItem('theme');
        if (stored && ['light', 'dark'].includes(stored)) {
          return stored;
        }
        if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
          return 'dark';
        }
        return 'light';
      }

      // Проверка prefers-reduced-motion
      const prefersReducedMotion = 
        typeof window !== 'undefined' && 
        window.matchMedia('(prefers-reduced-motion: reduce)').matches;

      // Применяем тему при загрузке страницы
      applyTheme(getStoredTheme());

      // Loading indicator state
      let loadingIndicator = null;

      // astro:page-load - начальная загрузка страницы
      document.addEventListener('astro:page-load', () => {
        applyTheme(getStoredTheme());
      });

      // astro:before-preparation - перед началом подготовки перехода
      document.addEventListener('astro:before-preparation', () => {
        if (!prefersReducedMotion) {
          // Добавляем класс для плавного перехода
          document.body.classList.add('transitioning');
        }
      });

      // astro:before-swap - перед свапом документа
      document.addEventListener('astro:before-swap', (event) => {
        const theme = getStoredTheme();
        
        // Применяем тему к текущему документу
        applyTheme(theme);

        // Применяем тему к новому документу перед свапом
        if (event.newDocument) {
          const newRoot = event.newDocument.documentElement;
          if (theme === 'dark') {
            newRoot.classList.add('dark');
          } else {
            newRoot.classList.remove('dark');
          }
        }
      });

      // astro:after-swap - после свапа страницы
      document.addEventListener('astro:after-swap', () => {
        applyTheme(getStoredTheme());
        
        if (!prefersReducedMotion) {
          document.body.classList.remove('transitioning');
        }
      });

      // astro:after-preparation - после подготовки (финализация)
      document.addEventListener('astro:after-preparation', () => {
        // Очистка, если нужно
      });
    </script>

    <header
      transition:name="header"
      class="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
    >
      <div class="container flex h-14 max-w-screen-2xl items-center">
        <!-- Логотип для мобильной версии -->
        <div class="flex md:hidden items-center gap-2">
          <a href="/" class="flex items-center space-x-2">
            <span class="font-bold text-lg">PartTec3</span>
          </a>
        </div>

        <!-- Логотип для десктопа -->
        <div class="hidden md:flex items-center gap-2">
          <a href="/" class="flex items-center space-x-2">
            <span class="font-bold text-xl">PartTec3</span>
          </a>
        </div>

        <!-- Правая часть хедера -->
        <div class="ml-auto flex items-center gap-2">
          <!-- Переключатель тем -->
          <ThemeToggle client:load />
          <!-- Навигация и меню -->
          <AuthNav client:load />
        </div>
      </div>
    </header>

    <main class="flex-1" transition:animate="fade">
      <slot />
    </main>

    <footer transition:name="footer" class="border-t py-6 md:py-0">
      <div
        class="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row"
      >
        <div
          class="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0"
        >
          <p
            class="text-center text-sm leading-loose text-muted-foreground md:text-left"
          >
            © 2024 PartTec3. Каталог взаимозаменяемых запчастей.
          </p>
        </div>
      </div>
    </footer>
  </body>
</html>
