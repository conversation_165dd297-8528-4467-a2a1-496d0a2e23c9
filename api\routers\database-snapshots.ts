import { z } from 'zod';
import { expertProcedure, createTRPCRouter } from '../trpc';
import { DatabaseSnapshotService } from '../services/database-snapshot.service';
import { getSystemDB } from '../db';

const CreateSnapshotInput = z.object({
  name: z.string().min(3),
  description: z.string().optional(),
  type: z.enum(['FULL_DATABASE', 'SELECTIVE_TABLES']),
  tablesIncluded: z.array(z.string()).optional(),
});

export const databaseSnapshotsRouter = createTRPCRouter({
  list: expertProcedure
    .input(z.object({
      filters: z.any().optional(),
      pagination: z.object({
        skip: z.number().optional(),
        take: z.number().optional(),
      }).optional(),
    }))
    .query(async ({ input }) => {
      return DatabaseSnapshotService.listSnapshots(input.filters, input.pagination);
    }),

  create: expertProcedure
    .input(CreateSnapshotInput)
    .mutation(async ({ input, ctx }) => {
        if (!ctx.user) throw new Error("Not authenticated");
      return DatabaseSnapshotService.createSnapshot({ ...input, userId: ctx.user.id });
    }),

  details: expertProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      return DatabaseSnapshotService.getSnapshotDetails(input.id);
    }),

  restore: expertProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      return DatabaseSnapshotService.restoreSnapshot({ id: input.id });
    }),

  delete: expertProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
        if (!ctx.user) throw new Error("Not authenticated");
      await DatabaseSnapshotService.deleteSnapshot(input.id, ctx.user.id);
      return { success: true };
    }),
    
  getTablesList: expertProcedure
    .query(async () => {
        const db = getSystemDB();
        const availableModels = Object.keys(db).filter(k => !k.startsWith('$') && !k.startsWith('_'));
        return availableModels;
    }),

  estimateSize: expertProcedure
    .input(z.object({ tables: z.array(z.string()).optional() }))
    .query(async ({ input }) => {
      // This is a placeholder for a more complex calculation
      return input.tables ? input.tables.length * 5 * 1024 * 1024 : 100 * 1024 * 1024;
    }),
});