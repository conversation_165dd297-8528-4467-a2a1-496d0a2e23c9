import { useQueryClient } from '@tanstack/react-query'
import { trpcClient } from '@/lib/trpc'
import { useEffect, useCallback } from 'react'
import { useDebounce } from './useDebounce'

export function usePrefetchCategories() {
  const queryClient = useQueryClient()
  useEffect(() => {
    const prefetch = async () => {
      await queryClient.prefetchQuery({
        queryKey: ['site.catalog.categories', { flat: false, limit: 200 }],
        queryFn: () => trpcClient.site.catalog.categories.query({ flat: false, limit: 200 }),
        staleTime: 10 * 60 * 1000,
      })
    }
    prefetch()
  }, [queryClient])
}

export function usePrefetchBrands() {
  const queryClient = useQueryClient()
  useEffect(() => {
    const prefetch = async () => {
      await queryClient.prefetchQuery({
        queryKey: ['site.catalog.brands', { limit: 100 }],
        queryFn: () => trpcClient.site.catalog.brands.query({ limit: 100 }),
        staleTime: 10 * 60 * 1000,
      })
    }
    prefetch()
  }, [queryClient])
}

export function usePrefetchAttributeTemplates() {
  const queryClient = useQueryClient()
  useEffect(() => {
    const prefetch = async () => {
      await queryClient.prefetchQuery({
        queryKey: ['site.attributes.templates', { limit: 100 }],
        queryFn: () => trpcClient.site.attributes.templates.query({ limit: 100 }),
        staleTime: 15 * 60 * 1000,
      })
    }
    prefetch()
  }, [queryClient])
}

export function usePrefetchOnHover(id: number, type: 'part' | 'catalogItem') {
  const queryClient = useQueryClient()
  const debouncedId = useDebounce(id, 200)

  const prefetch = useCallback(async () => {
    if (typeof window.ontouchstart !== 'undefined') {
      return // не для touch устройств
    }

    if (!debouncedId) return

    if (type === 'part') {
      await queryClient.prefetchQuery({
        queryKey: ['site.catalog.getPartById', { id: debouncedId }],
        queryFn: () => trpcClient.site.catalog.getPartById.query({ id: debouncedId }),
      })
    } else {
      await queryClient.prefetchQuery({
        queryKey: ['site.catalog.getItemById', { id: debouncedId }],
        queryFn: () => trpcClient.site.catalog.getItemById.query({ id: debouncedId }),
      })
    }
  }, [queryClient, debouncedId, type])

  return prefetch
}


export function usePrefetchNextPage(
    queryKey: readonly unknown[],
    queryFn: (params: { pageParam: number }) => Promise<any>,
    pages: any[],
    hasNextPage?: boolean
) {
    const queryClient = useQueryClient();

    useEffect(() => {
        if (hasNextPage) {
            const nextPageParam = pages.length;
            queryClient.prefetchInfiniteQuery({
                queryKey: queryKey,
                queryFn: () => queryFn({ pageParam: nextPageParam }),
                staleTime: 5 * 60 * 1000, // 5 minutes
            });
        }
    }, [hasNextPage, pages, queryKey, queryFn, queryClient]);
}


export function usePrefetchRelatedData(partId: number) {
  const queryClient = useQueryClient()
  const debouncedId = useDebounce(partId, 200)

  useEffect(() => {
    if (!debouncedId) return

    const prefetch = async () => {
        // Prefetch applicabilities
        await queryClient.prefetchQuery({
            queryKey: ['site.catalog.getPartApplicabilities', { partId: debouncedId }],
            queryFn: () => trpcClient.site.catalog.getPartApplicabilities.query({ partId: debouncedId }),
        });
        // Prefetch media
        await queryClient.prefetchQuery({
            queryKey: ['site.media.getAssets', { contextId: debouncedId, contextType: 'part' }],
            queryFn: () => trpcClient.site.media.getAssets.query({ contextId: debouncedId, contextType: 'part' }),
        });
    }

    prefetch();
  }, [queryClient, debouncedId])
}

export function isPrefetched(queryClient: any, queryKey: any[]): boolean {
    return queryClient.getQueryData(queryKey) !== undefined;
}

export function invalidatePrefetchedData(queryClient: any, queryKey: any[]) {
    queryClient.invalidateQueries({ queryKey });
}