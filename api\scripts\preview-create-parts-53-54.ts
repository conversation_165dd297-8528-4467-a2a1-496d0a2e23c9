import { MatchingService } from '../services/matching.service'
import { getSystemDB } from '../db'

async function main() {
  const db = getSystemDB()
  const items = await db.catalogItem.findMany({
    where: { id: { in: [53, 54] } },
    select: { id: true },
  })
  if (items.length === 0) {
    console.log('No items 53/54 in DB')
    return
  }

  const templates = await db.attributeTemplate.findMany({
    where: { name: { in: ['seal_type', 'inner_diameter'] } },
    select: { id: true, name: true, dataType: true },
  })
  const attributeTemplateIds = templates.map(t => t.id)
  if (attributeTemplateIds.length === 0) {
    console.log('No templates found for seal_type/inner_diameter')
    return
  }

  const input = {
    itemIds: items.map(i => i.id),
    partCategoryId: 1, // adjust in UI; here just placeholder category id
    nameTemplate: 'Сальник {seal_type} {inner_diameter}',
    attributeTemplateIds,
    useCanonicalForStrings: true,
    groupByAttributeSignature: true,
    requireAllPlaceholders: false,
  } as const

  const preview = await MatchingService.previewCreatePartsFromCatalogItems(input as any)
  console.log(JSON.stringify(preview, null, 2))
}

main().then(() => process.exit(0)).catch((e) => { console.error(e); process.exit(1) })

