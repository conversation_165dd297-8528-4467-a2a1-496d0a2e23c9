import { useState, useEffect, useRef, useCallback } from 'react'
import { motion, type PanInfo } from 'motion/react'
import type { MediaAsset } from '@/lib/types'
import {
  isImage,
  isVideo,
  isPDF,
  getMediaType,
  preloadImage,
  createKeyboardHandler,
  formatFileSize,
} from './media-utils'
import { modalOverlay, modalContent } from '@/lib/animation-variants'
import {
  Dialog,
  DialogContent,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  X,
  ZoomIn,
  ZoomOut,
  ChevronLeft,
  ChevronRight,
  Download,
  Printer,
  RotateCcw,
} from 'lucide-react'
import { cn } from '@/lib/utils'

export interface MediaZoomProps {
  assets: MediaAsset[]
  initialIndex?: number
  isOpen: boolean
  onClose: () => void
  enableZoom?: boolean
  enablePan?: boolean
  maxZoom?: number
  minZoom?: number
  className?: string
}

interface Transform {
  scale: number
  x: number
  y: number
}

export default function MediaZoom({
  assets,
  initialIndex = 0,
  isOpen,
  onClose,
  enableZoom = true,
  enablePan = true,
  maxZoom = 4,
  minZoom = 1,
  className,
}: MediaZoomProps) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex)
  const [transform, setTransform] = useState<Transform>({ scale: 1, x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [isControlsVisible, setIsControlsVisible] = useState(true)
  const containerRef = useRef<HTMLDivElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const controlsTimerRef = useRef<NodeJS.Timeout>()

  const currentAsset = assets[currentIndex]
  const currentType = currentAsset ? getMediaType(currentAsset.mimeType) : 'other'

  // Reset transform when index changes
  useEffect(() => {
    setTransform({ scale: 1, x: 0, y: 0 })
  }, [currentIndex])

  // Update initial index
  useEffect(() => {
    setCurrentIndex(initialIndex)
  }, [initialIndex])

  // Preload adjacent images
  useEffect(() => {
    if (!isOpen || !currentAsset) return

    const toPreload: string[] = []
    if (currentIndex > 0 && isImage(assets[currentIndex - 1].mimeType)) {
      toPreload.push(assets[currentIndex - 1].url)
    }
    if (currentIndex < assets.length - 1 && isImage(assets[currentIndex + 1].mimeType)) {
      toPreload.push(assets[currentIndex + 1].url)
    }

    toPreload.forEach((url) => preloadImage(url).catch(console.error))
  }, [currentIndex, isOpen, assets, currentAsset])

  // Keyboard navigation
  useEffect(() => {
    if (!isOpen) return

    const handler = createKeyboardHandler({
      onNext: () => {
        if (currentIndex < assets.length - 1) {
          setCurrentIndex(currentIndex + 1)
        }
      },
      onPrev: () => {
        if (currentIndex > 0) {
          setCurrentIndex(currentIndex - 1)
        }
      },
      onClose: onClose,
      onZoomIn: () => handleZoomIn(),
      onZoomOut: () => handleZoomOut(),
    })

    window.addEventListener('keydown', handler)
    return () => window.removeEventListener('keydown', handler)
  }, [isOpen, currentIndex, assets.length, onClose, transform.scale])

  // Auto-hide controls
  useEffect(() => {
    if (!isOpen) return

    const resetTimer = () => {
      setIsControlsVisible(true)
      if (controlsTimerRef.current) {
        clearTimeout(controlsTimerRef.current)
      }
      controlsTimerRef.current = setTimeout(() => {
        setIsControlsVisible(false)
      }, 3000)
    }

    const handleMouseMove = () => resetTimer()

    resetTimer()
    window.addEventListener('mousemove', handleMouseMove)

    return () => {
      if (controlsTimerRef.current) {
        clearTimeout(controlsTimerRef.current)
      }
      window.removeEventListener('mousemove', handleMouseMove)
    }
  }, [isOpen])

  const constrainPan = useCallback(
    (value: number, axis: 'x' | 'y'): number => {
      if (!imageRef.current || !containerRef.current) return value

      const containerRect = containerRef.current.getBoundingClientRect()
      
      // Use natural dimensions, not scaled dimensions
      const naturalWidth = imageRef.current.naturalWidth
      const naturalHeight = imageRef.current.naturalHeight
      
      // Calculate the displayed size based on max-width/max-height constraints
      const maxDisplayWidth = containerRect.width * 0.9
      const maxDisplayHeight = containerRect.height * 0.9
      const aspectRatio = naturalWidth / naturalHeight
      
      let displayWidth = naturalWidth
      let displayHeight = naturalHeight
      
      if (displayWidth > maxDisplayWidth) {
        displayWidth = maxDisplayWidth
        displayHeight = displayWidth / aspectRatio
      }
      if (displayHeight > maxDisplayHeight) {
        displayHeight = maxDisplayHeight
        displayWidth = displayHeight * aspectRatio
      }

      const imageSize = axis === 'x' ? displayWidth : displayHeight
      const containerSize = axis === 'x' ? containerRect.width : containerRect.height

      const scaledImageSize = imageSize * transform.scale
      const maxPan = Math.max(0, (scaledImageSize - containerSize) / 2)
      
      return Math.max(-maxPan, Math.min(maxPan, value))
    },
    [transform.scale]
  )

  const handleZoomIn = () => {
    if (!enableZoom) return
    const newScale = Math.min(maxZoom, transform.scale + 0.5)
    setTransform({ ...transform, scale: newScale })
  }

  const handleZoomOut = () => {
    if (!enableZoom) return
    const newScale = Math.max(minZoom, transform.scale - 0.5)
    if (newScale === minZoom) {
      setTransform({ scale: minZoom, x: 0, y: 0 })
    } else {
      setTransform({ ...transform, scale: newScale })
    }
  }

  const handleReset = () => {
    setTransform({ scale: 1, x: 0, y: 0 })
  }

  const handleWheel = (e: React.WheelEvent) => {
    if (!enableZoom || currentType !== 'image') return

    e.preventDefault()
    const delta = e.deltaY > 0 ? -0.1 : 0.1
    const newScale = Math.max(minZoom, Math.min(maxZoom, transform.scale + delta))

    if (newScale === minZoom) {
      setTransform({ scale: minZoom, x: 0, y: 0 })
    } else {
      setTransform({ ...transform, scale: newScale })
    }
  }

  const handleDrag = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    if (!enablePan || transform.scale <= 1) return

    const newX = constrainPan(transform.x + info.delta.x, 'x')
    const newY = constrainPan(transform.y + info.delta.y, 'y')

    setTransform({ ...transform, x: newX, y: newY })
  }

  const handleDownload = () => {
    if (!currentAsset) return

    const link = document.createElement('a')
    link.href = currentAsset.url
    link.download = currentAsset.fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handlePrint = () => {
    if (!currentAsset) return
    window.open(currentAsset.url, '_blank')?.print()
  }

  const goToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }

  const goToNext = () => {
    if (currentIndex < assets.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }

  if (!currentAsset) return null

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent
        className={cn(
          'max-w-[100vw] max-h-[100vh] w-screen h-screen p-0 bg-black/95',
          className
        )}
      >
        <DialogTitle className="sr-only">Просмотр медиафайла</DialogTitle>

        {/* Top controls */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: isControlsVisible ? 1 : 0, y: isControlsVisible ? 0 : -20 }}
          className="absolute top-0 left-0 right-0 z-50 bg-gradient-to-b from-black/60 to-transparent p-4"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className="text-white font-medium">{currentAsset.fileName}</span>
              <Badge variant="secondary">
                {currentIndex + 1} / {assets.length}
              </Badge>
              {currentAsset.fileSize && (
                <span className="text-white/60 text-sm">
                  {formatFileSize(currentAsset.fileSize)}
                </span>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleDownload}
                className="text-white hover:bg-white/20"
              >
                <Download className="w-5 h-5" />
              </Button>
              {currentType === 'pdf' && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handlePrint}
                  className="text-white hover:bg-white/20"
                >
                  <Printer className="w-5 h-5" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="text-white hover:bg-white/20"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Main content */}
        <div
          ref={containerRef}
          className="relative w-full h-full flex items-center justify-center overflow-hidden"
          onWheel={handleWheel}
        >
          {/* Navigation buttons */}
          {assets.length > 1 && (
            <>
              <motion.button
                initial={{ opacity: 0 }}
                animate={{ opacity: isControlsVisible ? 1 : 0 }}
                onClick={goToPrevious}
                disabled={currentIndex === 0}
                className={cn(
                  'absolute left-4 z-40 p-3 rounded-full bg-black/50 text-white',
                  'hover:bg-black/70 transition-colors disabled:opacity-30 disabled:cursor-not-allowed'
                )}
              >
                <ChevronLeft className="w-6 h-6" />
              </motion.button>
              <motion.button
                initial={{ opacity: 0 }}
                animate={{ opacity: isControlsVisible ? 1 : 0 }}
                onClick={goToNext}
                disabled={currentIndex === assets.length - 1}
                className={cn(
                  'absolute right-4 z-40 p-3 rounded-full bg-black/50 text-white',
                  'hover:bg-black/70 transition-colors disabled:opacity-30 disabled:cursor-not-allowed'
                )}
              >
                <ChevronRight className="w-6 h-6" />
              </motion.button>
            </>
          )}

          {/* Media content */}
          {currentType === 'image' && (
            <motion.img
              ref={imageRef}
              src={currentAsset.url}
              alt={currentAsset.fileName}
              drag={enablePan && transform.scale > 1}
              dragConstraints={containerRef}
              dragElastic={0}
              onDragStart={() => setIsDragging(true)}
              onDragEnd={(e, info) => {
                setIsDragging(false)
                handleDrag(e, info)
              }}
              className={cn(
                'max-w-[90vw] max-h-[90vh] object-contain',
                transform.scale > 1 && enablePan ? 'cursor-grab active:cursor-grabbing' : 'cursor-default'
              )}
              style={{
                transform: `translate(${transform.x}px, ${transform.y}px) scale(${transform.scale})`,
                transformOrigin: 'center',
                transition: isDragging ? 'none' : 'transform 0.3s ease-out',
                willChange: 'transform',
              }}
            />
          )}

          {currentType === 'video' && (
            <video
              src={currentAsset.url}
              controls
              autoPlay={false}
              className="max-w-[90vw] max-h-[90vh]"
            />
          )}

          {currentType === 'pdf' && (
            <iframe
              src={currentAsset.url}
              title={currentAsset.fileName}
              className="w-[90vw] h-[90vh] bg-white"
            />
          )}
        </div>

        {/* Bottom controls */}
        {enableZoom && currentType === 'image' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isControlsVisible ? 1 : 0, y: isControlsVisible ? 0 : 20 }}
            className="absolute bottom-0 left-0 right-0 z-50 bg-gradient-to-t from-black/60 to-transparent p-4"
          >
            <div className="flex items-center justify-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleZoomOut}
                disabled={transform.scale <= minZoom}
                className="text-white hover:bg-white/20"
              >
                <ZoomOut className="w-5 h-5" />
              </Button>
              <span className="text-white min-w-[60px] text-center">
                {Math.round(transform.scale * 100)}%
              </span>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleZoomIn}
                disabled={transform.scale >= maxZoom}
                className="text-white hover:bg-white/20"
              >
                <ZoomIn className="w-5 h-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleReset}
                disabled={transform.scale === 1 && transform.x === 0 && transform.y === 0}
                className="text-white hover:bg-white/20 ml-2"
              >
                <RotateCcw className="w-5 h-5" />
              </Button>
            </div>
          </motion.div>
        )}
      </DialogContent>
    </Dialog>
  )
}

