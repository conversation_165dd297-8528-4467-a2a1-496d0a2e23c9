import { useState, useEffect } from 'react';

export const MOBILE_BREAKPOINT = 640;
export const TABLET_BREAKPOINT = 1024;

function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }

    const mediaQueryList = window.matchMedia(query);
    let active = true;

    const listener = () => {
      if (active) {
        setMatches(mediaQueryList.matches);
      }
    };

    // Initial check
    listener();

    // Debounced listener for resize events
    let timeoutId: NodeJS.Timeout;
    const debouncedListener = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(listener, 100);
    };

    mediaQueryList.addEventListener('change', debouncedListener);

    return () => {
      active = false;
      mediaQueryList.removeEventListener('change', debouncedListener);
      clearTimeout(timeoutId);
    };
  }, [query]);

  return matches;
}

export function useIsMobile(): boolean {
  return useMediaQuery(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
}

export function useIsTablet(): boolean {
  return useMediaQuery(`(min-width: ${MOBILE_BREAKPOINT}px) and (max-width: ${TABLET_BREAKPOINT - 1}px)`);
}

export function useIsDesktop(): boolean {
  return useMediaQuery(`(min-width: ${TABLET_BREAKPOINT}px)`);
}

export function useBreakpoint(): 'mobile' | 'tablet' | 'desktop' {
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  
  if (isMobile) return 'mobile';
  if (isTablet) return 'tablet';
  return 'desktop';
}

export default useMediaQuery;