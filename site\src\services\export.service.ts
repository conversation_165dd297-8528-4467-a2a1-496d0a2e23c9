import * as XLSX from 'xlsx'
import type { Part } from '@/lib/types'
import type { CatalogSearchFilters } from '@/types/catalog'
import { 
  downloadFile,
  type ExportOptions 
} from '@/lib/export-utils'
import { formatAttributeValue } from '@/lib/formatters'

type CatalogItem = {
  id: number
  sku: string
  description: string | null
  brand?: {
    id: number
    name: string
    isOem: boolean
  } | null
  attributes?: Array<{
    id: number
    templateId: number
    value: string
    template?: {
      name: string
      dataType: string
    } | null
  }>
  applicabilities?: Array<{
    partId: number
    accuracy: number | null
    notes: string | null
    part?: {
      name: string
      partCategory?: {
        name: string
      } | null
    } | null
  }>
  image?: {
    id: number
    url: string
    fileName: string | null
  } | null
  mediaAssets?: Array<{
    id: number
    url: string
    fileName: string | null
    type: string
  }>
}

// Вспомогательная функция для экранирования CSV
function escapeCsvValue(value: string): string {
  if (value.includes('"') || value.includes(',') || value.includes('\n')) {
    return `"${value.replace(/"/g, '""')}"`
  }
  return value
}

/**
 * Генерировать имя файла для экспорта
 */
export function generateExportFilename(
  type: 'csv' | 'xlsx',
  count: number
): string {
  const date = new Date().toISOString().split('T')[0]
  return `catalog-export-${count}-items-${date}.${type}`
}

/**
 * Подготовить данные для экспорта
 */
function prepareExportData(
  items: Array<Part | CatalogItem>,
  options: ExportOptions
): Array<Record<string, string | number | boolean>> {
  return items.map(item => {
    const row: Record<string, string | number | boolean> = {
      'ID': item.id,
      'SKU': item.sku,
      'Бренд': item.brand?.name || '',
      'OEM': item.brand?.isOem ? 'Да' : 'Нет',
      'Описание': item.description || '',
    }
    
    // Добавляем атрибуты
    if (item.attributes) {
      for (const attr of item.attributes) {
        const name = attr.template?.name || `Атрибут ${attr.templateId}`
        const value = formatAttributeValue(attr.value, attr.template?.dataType || 'STRING')
        row[name] = value
      }
    }
    
    // Добавляем применимость если запрошено
    if (options.includeApplicabilities && item.applicabilities) {
      row['Применимость'] = item.applicabilities
        .map(app => app.part?.name || '')
        .join('; ')
    }
    
    return row
  })
}

/**
 * Генерировать сводку для экспорта
 */
function generateExportSummary(
  items: Array<Part | CatalogItem>,
  filters: CatalogSearchFilters
): Record<string, string | number> {
  const summary: Record<string, string | number> = {
    'Всего элементов': items.length,
    'Дата экспорта': new Date().toLocaleString('ru-RU'),
    'Поисковый запрос': filters.query || '(не указан)',
  }
  
  if (filters.categoryIds && filters.categoryIds.length > 0) {
    summary['Фильтр категорий'] = filters.categoryIds.join(', ')
  }
  
  if (filters.brandIds && filters.brandIds.length > 0) {
    summary['Фильтр брендов'] = filters.brandIds.join(', ')
  }
  
  if (filters.isOemOnly) {
    summary['Только OEM'] = 'Да'
  }
  
  return summary
}

/**
 * Экспортировать артикулы в CSV
 */
export function exportCatalogItemsToCSV(
  items: CatalogItem[],
  options: ExportOptions = {}
): void {
  const rows: string[] = []
  
  // Заголовок с сводкой
  rows.push('=== ЭКСПОРТ КАТАЛОГА ===')
  rows.push(`Всего элементов: ${items.length}`)
  rows.push(`Дата экспорта: ${new Date().toLocaleString('ru-RU')}`)
  rows.push('')
  
  // Заголовки таблицы
  rows.push('ID,SKU,Бренд,OEM,Описание')
  
  // Данные
  for (const item of items) {
    const row = [
      item.id.toString(),
      escapeCsvValue(item.sku),
      escapeCsvValue(item.brand?.name || ''),
      item.brand?.isOem ? 'Да' : 'Нет',
      escapeCsvValue(item.description || '')
    ]
    rows.push(row.join(','))
  }
  
  // Скачать файл
  const content = rows.join('\n')
  const filename = generateExportFilename('csv', items.length)
  downloadFile(content, filename, 'text/csv;charset=utf-8')
}

/**
 * Экспортировать артикулы в Excel
 */
export function exportCatalogItemsToExcel(
  items: CatalogItem[],
  options: ExportOptions = {}
): void {
  // Создать workbook
  const workbook = XLSX.utils.book_new()
  
  // 1. Лист сводки
  const summaryData = [
    ['Экспорт каталога'],
    [''],
    ['Всего элементов', items.length],
    ['Дата экспорта', new Date().toLocaleString('ru-RU')],
  ]
  const summarySheet = XLSX.utils.aoa_to_sheet(summaryData)
  XLSX.utils.book_append_sheet(workbook, summarySheet, 'Сводка')
  
  // 2. Лист с артикулами
  const itemsData = items.map(item => ({
    'ID': item.id,
    'SKU': item.sku,
    'Бренд': item.brand?.name || '',
    'OEM': item.brand?.isOem ? 'Да' : 'Нет',
    'Описание': item.description || '',
  }))
  const itemsSheet = XLSX.utils.json_to_sheet(itemsData)
  
  // Автоматическая ширина колонок
  const maxWidths = [
    { wch: 8 },  // ID
    { wch: 20 }, // SKU
    { wch: 20 }, // Бренд
    { wch: 6 },  // OEM
    { wch: 40 }, // Описание
  ]
  itemsSheet['!cols'] = maxWidths
  
  XLSX.utils.book_append_sheet(workbook, itemsSheet, 'Артикулы')
  
  // 3. Лист с атрибутами (если есть)
  if (options.includeApplicabilities || options.includeAlternatives) {
    const attributesData: Array<Record<string, string | number>> = []
    
    for (const item of items) {
      if (item.attributes) {
        for (const attr of item.attributes) {
          attributesData.push({
            'SKU': item.sku,
            'Атрибут': attr.template?.name || `ID ${attr.templateId}`,
            'Значение': formatAttributeValue(attr.value, attr.template?.dataType || 'STRING'),
          })
        }
      }
    }
    
    if (attributesData.length > 0) {
      const attributesSheet = XLSX.utils.json_to_sheet(attributesData)
      XLSX.utils.book_append_sheet(workbook, attributesSheet, 'Атрибуты')
    }
  }
  
  // Скачать файл
  const filename = generateExportFilename('xlsx', items.length)
  XLSX.writeFile(workbook, filename)
}

/**
 * Экспортировать эталонные группы (Parts) в CSV
 */
export function exportPartsToCSV(
  parts: Part[],
  options: ExportOptions = {}
): void {
  const rows: string[] = []
  
  // Заголовок с сводкой
  rows.push('=== ЭКСПОРТ ЭТАЛОННЫХ ГРУПП ===')
  rows.push(`Всего групп: ${parts.length}`)
  rows.push(`Дата экспорта: ${new Date().toLocaleString('ru-RU')}`)
  rows.push('')
  
  // Заголовки таблицы
  rows.push('ID,Название,Категория,Описание,Артикулов')
  
  // Данные
  for (const part of parts) {
    const row = [
      part.id.toString(),
      escapeCsvValue(part.name),
      escapeCsvValue(part.partCategory?.name || ''),
      escapeCsvValue(part.description || ''),
      part.applicabilities?.length.toString() || '0'
    ]
    rows.push(row.join(','))
  }
  
  // Скачать файл
  const content = rows.join('\n')
  const filename = `parts-export-${parts.length}-items-${new Date().toISOString().split('T')[0]}.csv`
  downloadFile(content, filename, 'text/csv;charset=utf-8')
}

/**
 * Экспортировать эталонные группы (Parts) в Excel
 */
export function exportPartsToExcel(
  parts: Part[],
  options: ExportOptions = {}
): void {
  // Создать workbook
  const workbook = XLSX.utils.book_new()
  
  // 1. Лист сводки
  const summaryData = [
    ['Экспорт эталонных групп'],
    [''],
    ['Всего групп', parts.length],
    ['Дата экспорта', new Date().toLocaleString('ru-RU')],
  ]
  const summarySheet = XLSX.utils.aoa_to_sheet(summaryData)
  XLSX.utils.book_append_sheet(workbook, summarySheet, 'Сводка')
  
  // 2. Лист с группами
  const partsData = parts.map(part => ({
    'ID': part.id,
    'Название': part.name,
    'Категория': part.partCategory?.name || '',
    'Описание': part.description || '',
    'Артикулов': part.applicabilities?.length || 0,
  }))
  const partsSheet = XLSX.utils.json_to_sheet(partsData)
  XLSX.utils.book_append_sheet(workbook, partsSheet, 'Группы')
  
  // 3. Лист с применимостью
  if (options.includeApplicabilities) {
    const applicabilitiesData: Array<Record<string, string | number>> = []
    
    for (const part of parts) {
      if (part.applicabilities) {
        for (const app of part.applicabilities) {
          applicabilitiesData.push({
            'Группа': part.name,
            'SKU': app.catalogItem?.sku || '',
            'Бренд': app.catalogItem?.brand?.name || '',
            'Точность': app.accuracy || '',
            'Примечания': app.notes || '',
          })
        }
      }
    }
    
    if (applicabilitiesData.length > 0) {
      const applicabilitiesSheet = XLSX.utils.json_to_sheet(applicabilitiesData)
      XLSX.utils.book_append_sheet(workbook, applicabilitiesSheet, 'Применимость')
    }
  }
  
  // 4. Лист с техникой
  if (options.includeEquipment) {
    const equipmentData: Array<Record<string, string | number>> = []
    
    for (const part of parts) {
      if (part.equipmentApplicabilities) {
        for (const app of part.equipmentApplicabilities) {
          equipmentData.push({
            'Группа': part.name,
            'Техника': app.equipmentModel?.name || '',
            'Бренд': app.equipmentModel?.brand?.name || '',
            'Примечания': app.notes || '',
          })
        }
      }
    }
    
    if (equipmentData.length > 0) {
      const equipmentSheet = XLSX.utils.json_to_sheet(equipmentData)
      XLSX.utils.book_append_sheet(workbook, equipmentSheet, 'Техника')
    }
  }
  
  // Скачать файл
  const filename = `parts-export-${parts.length}-items-${new Date().toISOString().split('T')[0]}.xlsx`
  XLSX.writeFile(workbook, filename)
}

/**
 * Экспортировать для сравнения (матрица)
 */
export function exportComparisonToCSV(
  items: Array<Part | CatalogItem>
): void {
  if (items.length === 0) return
  
  const rows: string[] = []
  
  // Заголовок
  rows.push('Атрибут,' + items.map(item => escapeCsvValue(item.sku)).join(','))
  
  // Собрать все уникальные атрибуты
  const allAttributeNames = new Set<string>()
  for (const item of items) {
    if (item.attributes) {
      for (const attr of item.attributes) {
        const name = attr.template?.name || `Атрибут ${attr.templateId}`
        allAttributeNames.add(name)
      }
    }
  }
  
  // Создать строки для каждого атрибута
  for (const attrName of Array.from(allAttributeNames).sort()) {
    const values = items.map(item => {
      if (!item.attributes) return ''
      
      const attr = item.attributes.find(a => 
        a.template?.name === attrName
      )
      
      if (attr) {
        return escapeCsvValue(
          formatAttributeValue(attr.value, attr.template?.dataType || 'STRING')
        )
      }
      
      return ''
    })
    
    rows.push(escapeCsvValue(attrName) + ',' + values.join(','))
  }
  
  // Скачать файл
  const content = rows.join('\n')
  const filename = `comparison-${items.length}-items-${new Date().toISOString().split('T')[0]}.csv`
  downloadFile(content, filename, 'text/csv;charset=utf-8')
}

