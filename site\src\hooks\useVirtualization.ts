import { useVirtualizer } from '@tanstack/react-virtual'
import { useRef, useMemo } from 'react'
import type { Virtualizer, VirtualizerOptions, VirtualItem } from '@tanstack/react-virtual'
import type { RefObject } from 'react'

interface UseVirtualizationOptions<T> {
  items: T[]
  estimateSize?: number // Примерная высота элемента (default: 100px)
  overscan?: number // Количество элементов для предзагрузки (default: 5)
  enabled?: boolean // Включить/выключить виртуализацию (default: true)
  getItemKey?: (index: number) => string | number // Функция для получения ключа элемента
}

interface UseVirtualizationReturn<T> {
  virtualizer: Virtualizer<HTMLDivElement, Element> | null
  virtualItems: T[] | VirtualItem[]
  totalSize: number
  scrollToIndex: (index: number, options?: ScrollToOptions) => void
  scrollToOffset: (offset: number, options?: ScrollToOptions) => void
  containerRef: RefObject<HTMLDivElement>
}

export function useVirtualization<T>({
  items,
  estimateSize = 100,
  overscan = 5,
  enabled = true,
  getItemKey,
}: UseVirtualizationOptions<T>): UseVirtualizationReturn<T> {
  const containerRef = useRef<HTMLDivElement>(null)

  const virtualizer = enabled
    ? useVirtualizer({
        count: items.length,
        getScrollElement: () => containerRef.current,
        estimateSize: () => estimateSize,
        overscan,
        getItemKey: getItemKey || ((index) => index),
      })
    : null

  const virtualItems = useMemo(() => {
    if (!enabled || !virtualizer) {
      return items.map((item, index) => ({
        ...item,
        key: getItemKey ? getItemKey(index) : index,
        index,
        start: index * estimateSize,
        size: estimateSize,
        end: (index + 1) * estimateSize,
      }))
    }
    return virtualizer.getVirtualItems()
  }, [virtualizer, items, enabled, estimateSize, getItemKey])

  const totalSize = useMemo(() => {
    if (!enabled || !virtualizer) {
      return items.length * estimateSize
    }
    return virtualizer.getTotalSize()
  }, [virtualizer, items, enabled, estimateSize])

  const scrollToIndex = (index: number, options?: ScrollToOptions) => {
    virtualizer?.scrollToIndex(index, options)
  }

  const scrollToOffset = (offset: number, options?: ScrollToOptions) => {
    virtualizer?.scrollToOffset(offset, options)
  }

  return {
    virtualizer,
    virtualItems: virtualItems as VirtualItem[],
    totalSize,
    scrollToIndex,
    scrollToOffset,
    containerRef,
  }
}

export function useListVirtualization<T>({
  items,
  itemHeight,
  enabled,
  overscan,
}: {
  items: T[]
  itemHeight: number
  enabled?: boolean
  overscan?: number
}) {
  return useVirtualization({
    items,
    estimateSize: itemHeight,
    enabled: enabled !== false && items.length > 20,
    overscan,
  })
}

export function useTableVirtualization<T>({
  items,
  rowHeight,
  enabled,
  overscan,
}: {
  items: T[]
  rowHeight: number
  enabled?: boolean
  overscan?: number
}) {
  return useVirtualization({
    items,
    estimateSize: rowHeight,
    enabled: enabled !== false && items.length > 50,
    overscan: overscan || 10,
  })
}