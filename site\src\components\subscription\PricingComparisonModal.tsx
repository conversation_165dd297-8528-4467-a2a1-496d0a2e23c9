"use client"

import { useState } from "react"
import { Check, X, Crown, Sparkles } from "lucide-react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

export interface PricingComparisonModalProps {
  trigger?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
  defaultOpen?: boolean
}

const comparisonFeatures = [
  {
    category: "Доступ к данным",
    features: [
      {
        name: "Базовый каталог (CatalogItem)",
        free: true,
        pro: true,
        description: "Артикулы, бренды, базовые характеристики"
      },
      {
        name: "Эт<PERSON><PERSON><PERSON><PERSON><PERSON> и группы взаимозаменяемости (Part)",
        free: false,
        pro: true,
        description: "Нормализованные данные, проверенные инженерами"
      },
      {
        name: "Данные о взаимозаменяемости (PartApplicability)",
        free: false,
        pro: true,
        description: "Полный список аналогов с точностью совпадения"
      },
      {
        name: "Информация о поставщиках и ценах",
        free: false,
        pro: true,
        description: "Актуальные цены и наличие у поставщиков"
      }
    ]
  },
  {
    category: "Поиск и фильтры",
    features: [
      {
        name: "Базовый поиск по артикулам",
        free: true,
        pro: true,
        description: "Поиск по SKU и брендам"
      },
      {
        name: "Расширенный поиск по атрибутам",
        free: false,
        pro: true,
        description: "Поиск по техническим параметрам эталонов"
      },
      {
        name: "AI-ассистент для подбора",
        free: false,
        pro: true,
        description: "Умный поиск на естественном языке"
      }
    ]
  },
  {
    category: "Инструменты",
    features: [
      {
        name: "Сравнение характеристик",
        free: false,
        pro: true,
        description: "Детальное сравнение аналогов"
      },
      {
        name: "Экспорт данных (Excel/CSV)",
        free: false,
        pro: true,
        description: "Выгрузка результатов поиска"
      },
      {
        name: "История поиска",
        free: "Ограничено",
        pro: "Безлимитно",
        description: "Сохранение истории запросов"
      }
    ]
  },
  {
    category: "Лимиты",
    features: [
      {
        name: "Запросов в месяц",
        free: "20",
        pro: "Безлимитно",
        description: "Количество поисковых запросов"
      },
      {
        name: "Просмотров деталей",
        free: "50",
        pro: "Безлимитно",
        description: "Просмотр страниц CatalogItem"
      }
    ]
  }
] as const

function renderFeatureValue(value: boolean | string) {
  if (typeof value === 'boolean') {
    return value ? (
      <Check className="w-5 h-5 text-chart-2 mx-auto" />
    ) : (
      <X className="w-5 h-5 text-muted-foreground/30 mx-auto" />
    )
  }
  
  return (
    <span className="text-sm font-medium">{value}</span>
  )
}

export function PricingComparisonModal({
  trigger,
  open,
  onOpenChange,
  defaultOpen
}: PricingComparisonModalProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen ?? false)
  
  const handleOpenChange = (newOpen: boolean) => {
    setIsOpen(newOpen)
    onOpenChange?.(newOpen)
  }
  
  return (
    <Dialog open={open ?? isOpen} onOpenChange={handleOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">
            Сравнение тарифных планов
          </DialogTitle>
          <DialogDescription>
            Выберите план, который подходит именно вам
          </DialogDescription>
        </DialogHeader>
        
        {/* Заголовки колонок */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div /> {/* Пустая ячейка для названий функций */}
          
          {/* FREE колонка */}
          <div className="text-center p-4 border rounded-lg">
            <div className="text-lg font-semibold mb-1">Бесплатный</div>
            <div className="text-3xl font-bold mb-2">0 ₽</div>
            <div className="text-sm text-muted-foreground">/ месяц</div>
          </div>
          
          {/* PRO колонка */}
          <div className="text-center p-4 border-2 border-primary rounded-lg bg-primary/5">
            <Badge className="mb-2 bg-gradient-to-r from-amber-500 to-orange-500 border-0">
              <Crown className="w-3 h-3" />
              Рекомендуем
            </Badge>
            <div className="text-lg font-semibold mb-1">Профессиональный</div>
            <div className="text-3xl font-bold mb-2">2 990 ₽</div>
            <div className="text-sm text-muted-foreground">/ месяц</div>
          </div>
        </div>
        
        {/* Таблица сравнения */}
        <div className="space-y-6">
          {comparisonFeatures.map((category) => (
            <div key={category.category}>
              <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-primary" />
                {category.category}
              </h3>
              
              <div className="space-y-2">
                {category.features.map((feature) => (
                  <div key={feature.name} className="grid grid-cols-3 gap-4 items-center py-2 border-b last:border-0">
                    {/* Название функции */}
                    <div>
                      <div className="font-medium text-sm">{feature.name}</div>
                      <div className="text-xs text-muted-foreground">{feature.description}</div>
                    </div>
                    
                    {/* FREE */}
                    <div className="text-center">
                      {renderFeatureValue(feature.free)}
                    </div>
                    
                    {/* PRO */}
                    <div className="text-center">
                      {renderFeatureValue(feature.pro)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
        
        {/* Footer с кнопками */}
        <div className="grid grid-cols-2 gap-4 mt-6 pt-6 border-t">
          <Button variant="outline" size="lg" onClick={() => handleOpenChange(false)}>
            Остаться на FREE
          </Button>
          <Button size="lg" asChild>
            <a href="/pricing">
              <Crown className="w-4 h-4" />
              Оформить PRO
            </a>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export function PricingComparisonTrigger() {
  return (
    <PricingComparisonModal
      trigger={
        <Button variant="outline" size="sm">
          Сравнить тарифы
        </Button>
      }
    />
  )
}