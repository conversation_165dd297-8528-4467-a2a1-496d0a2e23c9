"use client";

import { useMemo, useState } from "react";
import type { Part } from "@/lib/types";
import { Badge } from "@/components/ui/badge";
import { ModernCard } from "@/components/ui/modern-card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Truck } from "lucide-react";
import { motion } from "motion/react";
import { staggerContainer, staggerItem } from "@/lib/animation-variants";
import { analytics } from "@/lib/analytics";
import { groupBy } from "@/lib/aggregators";

export interface EquipmentApplicabilitySectionProps {
  equipmentApplicabilities: Part["equipmentApplicabilities"];
  partId: number;
}

export default function EquipmentApplicabilitySection({ equipmentApplicabilities, partId }: EquipmentApplicabilitySectionProps) {
  if (!equipmentApplicabilities || equipmentApplicabilities.length === 0) return null;

  const [isOpen, setIsOpen] = useState(true);
  const [search, setSearch] = useState("");
  const [brandFilter, setBrandFilter] = useState<string | "all">("all");

  const stats = useMemo(() => {
    const brandIds = new Set(equipmentApplicabilities.map(e => e.equipmentModel.brand?.id).filter(Boolean));
    return {
      totalModels: equipmentApplicabilities.length,
      brandsCount: brandIds.size,
    };
  }, [equipmentApplicabilities]);

  const filtered = useMemo(() => {
    return equipmentApplicabilities.filter(ea => {
      const name = ea.equipmentModel.name.toLowerCase();
      const brand = ea.equipmentModel.brand?.name?.toLowerCase() || "";
      const matchesSearch = !search || name.includes(search.toLowerCase()) || brand.includes(search.toLowerCase());
      const matchesBrand = brandFilter === "all" || ea.equipmentModel.brand?.name === brandFilter;
      return matchesSearch && matchesBrand;
    });
  }, [equipmentApplicabilities, search, brandFilter]);

  const grouped = useMemo(() => {
    return groupBy(filtered, (ea) => ea.equipmentModel.brand?.name || "Без бренда");
  }, [filtered]);

  const brandOptions = useMemo(() => {
    const names = Array.from(new Set(equipmentApplicabilities.map(e => e.equipmentModel.brand?.name).filter(Boolean))) as string[];
    return names.sort();
  }, [equipmentApplicabilities]);

  return (
    <Collapsible open={isOpen} onOpenChange={(v) => {
      setIsOpen(v);
      if (v) analytics.equipmentSectionViewed(partId, equipmentApplicabilities.length);
    }}>
      <div className="border rounded-lg">
        <CollapsibleTrigger className="w-full p-4 flex items-center justify-between hover:bg-accent/50 transition-colors">
          <div className="flex items-center gap-3">
            <Truck className="h-5 w-5 text-primary" />
            <div className="text-left">
              <h3 className="font-bold text-lg">Применимость к технике</h3>
              <p className="text-sm text-muted-foreground">
                {stats.totalModels} моделей • {stats.brandsCount} брендов
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Badge variant="secondary" className="text-xs">Моделей: {stats.totalModels}</Badge>
            <Badge variant="secondary" className="text-xs">Брендов: {stats.brandsCount}</Badge>
          </div>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className="p-4 pt-0 space-y-4">
            {/* Фильтры */}
            <div className="flex flex-col sm:flex-row gap-2">
              <Input
                placeholder="Поиск модели или бренда"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="sm:max-w-xs"
              />
              <Select value={brandFilter} onValueChange={(v) => setBrandFilter(v as any)}>
                <SelectTrigger className="sm:w-64">
                  <SelectValue placeholder="Бренд" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Все бренды</SelectItem>
                  {brandOptions.map((name) => (
                    <SelectItem key={name} value={name}>{name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Группы по брендам */}
            <motion.div variants={staggerContainer} initial="initial" animate="animate" className="space-y-4">
              {Array.from(grouped.entries()).map(([brandName, items]) => (
                <div key={String(brandName)} className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">{brandName}</Badge>
                    <span className="text-xs text-muted-foreground">{items.length} моделей</span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {items.map((ea) => (
                      <motion.div variants={staggerItem} key={ea.id}>
                        <ModernCard variant="default" className="p-4 hover:shadow-md transition">
                          <button
                            type="button"
                            className="w-full text-left"
                            onClick={() => analytics.equipmentModelClicked(ea.equipmentModel.id, partId)}
                          >
                            <div className="flex items-start gap-4">
                              {/* Фото техники (fallback) */}
                              <div className="w-24 h-24 rounded-lg bg-muted flex items-center justify-center flex-shrink-0">
                                <Truck className="h-8 w-8 text-muted-foreground" />
                              </div>

                              <div className="flex-1 space-y-1">
                                <div className="flex items-center gap-2">
                                  <h4 className="font-semibold text-base">{ea.equipmentModel.name}</h4>
                                  {ea.equipmentModel.brand && (
                                    <Badge variant="secondary" className="text-xs">{ea.equipmentModel.brand.name}</Badge>
                                  )}
                                </div>
                                {ea.notes && (
                                  <p className="text-xs text-muted-foreground italic">{ea.notes}</p>
                                )}
                                {/* Ключевые характеристики: первые 4 */}
                                {ea.equipmentModel.attributes && ea.equipmentModel.attributes.length > 0 && (
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {ea.equipmentModel.attributes.slice(0, 4).map((attr) => (
                                      <Badge key={attr.id} variant="outline" className="text-xs">
                                        {attr.template.title}: {attr.value}
                                      </Badge>
                                    ))}
                                    {ea.equipmentModel.attributes.length > 4 && (
                                      <Badge variant="outline" className="text-xs">+{ea.equipmentModel.attributes.length - 4} еще</Badge>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                          </button>
                        </ModernCard>
                      </motion.div>
                    ))}
                  </div>
                </div>
              ))}
            </motion.div>
          </div>
        </CollapsibleContent>
      </div>
    </Collapsible>
  );
}

