import { useState, useEffect, useMemo } from 'react'
import { Search, Image as ImageIcon, Wrench, Loader2 } from 'lucide-react'
import type { Part, CatalogItem, PartCategory, Brand } from '@/types/zod'
import EnhancedSearchForm from './EnhancedSearchForm'
import SearchResults from './SearchResults'
import SearchFilters, { type SearchFilters as SearchFiltersType } from './SearchFilters'
import SearchInsights from './SearchInsights'
import ImageSearchUpload from './ImageSearchUpload'
import EquipmentSearch from './EquipmentSearch'
import { useDebounce } from '@uidotdev/usehooks'
import { useCatalogGlobalState } from '@/lib/catalog-state'
import { addToSearchHistory } from '@/lib/saved-searches'
import { analytics } from '@/lib/analytics'
import { trpc } from '@/lib/trpc'
import { cn } from '@/lib/utils'

export interface SearchIslandProps {
  initialQuery: string
  initialResults: {
    parts: Part[]
    catalogItems: CatalogItem[]
    categories: PartCategory[]
    brands: Brand[]
  }
  isPro: boolean
}

type SearchMode = 'text' | 'image' | 'equipment'

const SEARCH_MODE_KEY = 'search-mode'
const PAGE_SIZE = 50

export default function SearchIsland({
  initialQuery,
  initialResults,
  isPro
}: SearchIslandProps) {
  const [mode, setMode] = useState<SearchMode>(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(SEARCH_MODE_KEY) as SearchMode
      return stored || 'text'
    }
    return 'text'
  })

  // Вся логика ранжирования и typo suggestions на бэкенде
  // Сейчас: PostgreSQL full-text search
  // Будущее: MeiliSearch для продвинутого поиска
  const [query, setQuery] = useState(initialQuery)
  const debouncedQuery = useDebounce(query, 300)
  const [page, setPage] = useState(0)
  const [activeResultTypes, setActiveResultTypes] = useState<Array<'parts' | 'catalogItems' | 'categories' | 'brands'>>(['parts', 'catalogItems', 'categories', 'brands'])
  const [searchStartTime, setSearchStartTime] = useState(0)
  const [searchDuration, setSearchDuration] = useState(0)

  const { data, isLoading: queryLoading, error } = trpc.site.search.unified.useQuery({
    query: debouncedQuery,
    types: activeResultTypes,
    limit: 20,
    offset: page * 20,
    includeTypoSuggestions: true,
  }, {
    enabled: !!debouncedQuery,
    staleTime: 30_000,
  })

  // Результаты уже ранжированы с бэкенда
  const results = data || { parts: [], catalogItems: [], categories: [], brands: [], totalCount: 0 }
  const suggestions = data?.suggestions || []
  const hasMore = data?.hasMore ?? false

  // Фильтрация результатов (client-side для интерактивности)
  const [localFilters, setLocalFilters] = useState<SearchFiltersType>({
    resultTypes: ['parts', 'categories', 'brands'],
    categoryIds: [],
    brandIds: [],
    isOemOnly: false
  })

  const filteredResults = useMemo(() => {
    let parts = results.parts
    let catalogItems = results.catalogItems
    let categories = results.categories
    let brands = results.brands

    // Filter by result types
    if (!localFilters.resultTypes.includes('parts')) {
      parts = []
      catalogItems = []
    }
    if (!localFilters.resultTypes.includes('categories')) {
      categories = []
    }
    if (!localFilters.resultTypes.includes('brands')) {
      brands = []
    }

    // Filter by categories
    if (localFilters.categoryIds.length > 0) {
      parts = parts.filter(part =>
        part.partCategoryId && localFilters.categoryIds.includes(part.partCategoryId)
      )
      // catalogItems filtering by category needs adjustment if not directly available
    }

    // Filter by brands
    if (localFilters.brandIds.length > 0) {
      parts = parts.filter(part =>
        part.brandId && localFilters.brandIds.includes(part.brandId)
      )
      catalogItems = catalogItems.filter(item =>
        item.brandId && localFilters.brandIds.includes(item.brandId)
      )
      brands = brands.filter(brand =>
        localFilters.brandIds.includes(brand.id)
      )
    }

    // Filter by OEM only
    if (localFilters.isOemOnly) {
      // Assuming brand relation is included in query
      // parts = parts.filter(part => part.brand?.isOem)
      // catalogItems = catalogItems.filter(item => item.brand?.isOem)
      brands = brands.filter(brand => brand.isOem)
    }

    return {
      parts,
      catalogItems,
      categories,
      brands
    }
  }, [results, localFilters])

  // Handle search from form
  const handleSearch = (newQuery: string) => {
    if (!newQuery.trim()) return
    
    setSearchStartTime(Date.now())
    setQuery(newQuery)
    setPage(0) // Reset to first page
    addToSearchHistory(newQuery)
    
    // Track analytics
    setTimeout(() => {
      const duration = Date.now() - searchStartTime
      setSearchDuration(duration)
      analytics.searchPerformed(newQuery, results.totalCount, mode, duration)
    }, 500) // Wait for debounced search to likely finish
  }

  // Handle mode change
  const handleModeChange = (newMode: SearchMode) => {
    setMode(newMode)
    analytics.searchModeChanged(newMode)
    
    if (typeof window !== 'undefined') {
      localStorage.setItem(SEARCH_MODE_KEY, newMode)
    }
  }

  // Handle filter change
  const handleFilterChange = (newFilters: SearchFiltersType) => {
    setLocalFilters(newFilters)
    setPage(0); // Reset pagination
    analytics.searchFilterApplied('multiple', JSON.stringify(newFilters))
  }

  // Handle image search
  const handleImageSearch = async (file: File) => {
    setSearchStartTime(Date.now())
    // TODO: Implement tRPC mutation for image upload
    console.log('Image search with file:', file.name)
    analytics.imageSearchPerformed(file.size, file.type, 0)
    setSearchDuration(Date.now() - searchStartTime)
  }

  // Handle equipment search
  const handleEquipmentSelect = (equipmentId: number, equipmentName: string) => {
    setSearchStartTime(Date.now())
    setQuery(equipmentName)
    setPage(0)
    const duration = Date.now() - searchStartTime
    setSearchDuration(duration)
    analytics.equipmentSearchPerformed(equipmentId, equipmentName, results.parts.length)
  }

  // Handle load more (pagination)
  const handleLoadMore = () => {
    if (!hasMore || isLoading) return
    
    const nextPage = page + 1
    setPage(nextPage)
    
    analytics.searchLoadMore(nextPage, results.parts.length + results.catalogItems.length)
  }

  // Sync with URL params
  useEffect(() => {
    if (typeof window === 'undefined') return

    const params = new URLSearchParams(window.location.search)
    const urlQuery = params.get('q')
    const urlMode = params.get('mode') as SearchMode
    
    if (urlQuery && urlQuery !== query) {
      setQuery(urlQuery)
    }
    
    if (urlMode && urlMode !== mode) {
      setMode(urlMode)
    }
  }, [])

  // Update URL when state changes
  useEffect(() => {
    if (typeof window === 'undefined') return

    const params = new URLSearchParams()
    if (query) params.set('q', query)
    if (mode !== 'text') params.set('mode', mode)

    const newUrl = `${window.location.pathname}?${params.toString()}`
    window.history.replaceState({}, '', newUrl)
  }, [query, mode])

  const totalResults = 
    filteredResults.parts.length + 
    filteredResults.catalogItems.length + 
    filteredResults.categories.length + 
    filteredResults.brands.length

  const isLoading = queryLoading

  return (
    <div className="w-full max-w-screen-2xl mx-auto px-4 py-8 space-y-6">
      {/* Mode Tabs */}
      <div className="flex items-center gap-2 border-b pb-2">
        <button
          onClick={() => handleModeChange('text')}
          className={cn(
            'px-4 py-2 rounded-t-lg transition-colors flex items-center gap-2',
            mode === 'text'
              ? 'bg-primary text-primary-foreground'
              : 'hover:bg-muted'
          )}
        >
          <Search className="w-4 h-4" />
          <span>Текстовый поиск</span>
        </button>
        <button
          onClick={() => handleModeChange('image')}
          className={cn(
            'px-4 py-2 rounded-t-lg transition-colors flex items-center gap-2',
            mode === 'image'
              ? 'bg-primary text-primary-foreground'
              : 'hover:bg-muted'
          )}
        >
          <ImageIcon className="w-4 h-4" />
          <span>Поиск по изображению</span>
        </button>
        <button
          onClick={() => handleModeChange('equipment')}
          className={cn(
            'px-4 py-2 rounded-t-lg transition-colors flex items-center gap-2',
            mode === 'equipment'
              ? 'bg-primary text-primary-foreground'
              : 'hover:bg-muted'
          )}
        >
          <Wrench className="w-4 h-4" />
          <span>Поиск по технике</span>
        </button>
      </div>

      {/* Search Forms */}
      {mode === 'text' && (
        <EnhancedSearchForm
          defaultQuery={query || ''}
          onSearch={handleSearch}
          suggestions={suggestions}
        />
      )}

      {mode === 'image' && (
        <ImageSearchUpload
          onImageSearch={handleImageSearch}
          isLoading={isLoading}
        />
      )}

      {mode === 'equipment' && (
        <EquipmentSearch
          onEquipmentSelect={handleEquipmentSelect}
        />
      )}

      {/* Insights */}
      {totalResults > 0 && (
        <SearchInsights
          query={query || ''}
          results={filteredResults}
          totalResults={totalResults}
          searchDuration={searchDuration}
        />
      )}

      {/* Results with Filters */}
      {mode === 'text' && totalResults > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-[280px_1fr] gap-6">
          {/* Filters Sidebar */}
          <SearchFilters
            results={{
              parts: results.parts,
              catalogItems: results.catalogItems,
              categories: results.categories,
              brands: results.brands
            }}
            onFilterChange={handleFilterChange}
          />

          {/* Results */}
          <SearchResults
            query={query || ''}
            results={filteredResults}
            isLoading={isLoading}
            onLoadMore={handleLoadMore}
            hasMore={hasMore}
          />
        </div>
      )}

      {/* Results without filters for other modes */}
      {(mode === 'image' || mode === 'equipment') && (
        <SearchResults
          query={query || ''}
          results={filteredResults}
          isLoading={isLoading}
          onLoadMore={handleLoadMore}
          hasMore={hasMore}
        />
      )}

      {/* Loading State */}
      {isLoading && mode === 'text' && (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <span className="ml-2 text-muted-foreground">Поиск...</span>
        </div>
      )}
    </div>
  )
}
