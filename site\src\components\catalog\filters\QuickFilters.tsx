/**
 * QuickFilters - компонент для быстрого применения предустановленных комбинаций фильтров
 * Поддерживает различные layout варианты и адаптивный дизайн
 */

import React from 'react';
import { motion } from 'motion/react';
import { useFilterSystem } from './FilterSystem';
import { getQuickFilters, areFiltersPartiallyEqual } from './filter-utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Shield, Clock, TrendingUp, Image, Target, MoreHorizontal, Check, X } from 'lucide-react';
import { staggerContainer, staggerItem } from '@/lib/animation-variants';
import type { QuickFilter } from '@/types/filters';

// ============================================================================
// Types
// ============================================================================

export interface QuickFiltersProps {
  layout?: 'horizontal' | 'vertical' | 'grid';
  maxVisible?: number; // Показать только первые N, остальные в "Ещё"
  className?: string;
}

// Иконки для быстрых фильтров
const ICON_MAP = {
  Shield,
  Clock,
  TrendingUp,
  Image,
  Target,
} as const;

// ============================================================================
// Component
// ============================================================================

export default function QuickFilters({
  layout = 'horizontal',
  maxVisible,
  className = '',
}: QuickFiltersProps): React.ReactElement {
  const { filters, applyQuickFilter, clearFilters } = useFilterSystem();

  const quickFilters = getQuickFilters();

  // Определяем, какие фильтры активны через частичное сравнение
  // Сравниваем только те поля, которые определены в quick filter
  const isQuickFilterActive = (quickFilter: QuickFilter): boolean => {
    return areFiltersPartiallyEqual(filters, quickFilter.filters);
  };

  const hasAnyActiveQuickFilter = quickFilters.some((qf) => isQuickFilterActive(qf));

  // Разделяем фильтры на видимые и скрытые (если задан maxVisible)
  const visibleFilters = maxVisible ? quickFilters.slice(0, maxVisible) : quickFilters;
  const hiddenFilters = maxVisible ? quickFilters.slice(maxVisible) : [];

  // Определяем CSS классы для layout
  const layoutClasses = {
    horizontal: 'flex flex-row flex-wrap gap-2',
    vertical: 'flex flex-col gap-2',
    grid: 'grid grid-cols-2 sm:grid-cols-3 gap-2',
  };

  const containerClass = `${layoutClasses[layout]} ${className}`;

  // ============================================================================
  // Handlers
  // ============================================================================

  const handleQuickFilterClick = (quickFilter: QuickFilter): void => {
    if (isQuickFilterActive(quickFilter)) {
      // Если фильтр уже активен, сбрасываем все фильтры
      clearFilters();
    } else {
      // Применяем быстрый фильтр
      applyQuickFilter(quickFilter);
    }
  };

  // ============================================================================
  // Render
  // ============================================================================

  return (
    <motion.div
      className={containerClass}
      variants={staggerContainer}
      initial="initial"
      animate="animate"
    >
      {visibleFilters.map((quickFilter) => (
        <QuickFilterButton
          key={quickFilter.id}
          quickFilter={quickFilter}
          isActive={isQuickFilterActive(quickFilter)}
          onClick={() => handleQuickFilterClick(quickFilter)}
        />
      ))}

      {hiddenFilters.length > 0 && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="gap-2">
              <MoreHorizontal className="h-4 w-4" />
              <span>Ещё...</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            {hiddenFilters.map((quickFilter) => (
              <DropdownMenuItem
                key={quickFilter.id}
                onClick={() => handleQuickFilterClick(quickFilter)}
                className="flex items-center gap-2"
              >
                {renderIcon(quickFilter.icon, 'h-4 w-4')}
                <span>{quickFilter.label}</span>
                {quickFilter.badge && (
                  <Badge variant="secondary" className="text-xs h-5 px-1.5 ml-auto">
                    {quickFilter.badge}
                  </Badge>
                )}
                {isQuickFilterActive(quickFilter) && (
                  <Check className="h-4 w-4 ml-2" />
                )}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {hasAnyActiveQuickFilter && (
        <motion.div variants={staggerItem}>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="gap-2 text-muted-foreground hover:text-foreground"
          >
            <X className="h-4 w-4" />
            <span>Сбросить</span>
          </Button>
        </motion.div>
      )}
    </motion.div>
  );
}

// ============================================================================
// Sub-components
// ============================================================================

interface QuickFilterButtonProps {
  quickFilter: QuickFilter;
  isActive: boolean;
  onClick: () => void;
}

function QuickFilterButton({
  quickFilter,
  isActive,
  onClick,
}: QuickFilterButtonProps): React.ReactElement {
  return (
    <motion.div
      variants={staggerItem}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <Button
        variant={isActive ? 'default' : 'outline'}
        onClick={onClick}
        className="gap-2 relative"
      >
        {renderIcon(quickFilter.icon, 'h-4 w-4')}
        <span>{quickFilter.label}</span>
        {quickFilter.badge && !isActive && (
          <Badge variant="secondary" className="text-xs h-5 px-1.5">
            {quickFilter.badge}
          </Badge>
        )}
        {isActive && <Check className="h-3.5 w-3.5 ml-1" />}
      </Button>
    </motion.div>
  );
}

// ============================================================================
// Utilities
// ============================================================================

function renderIcon(iconName: string | undefined, className: string): React.ReactElement | null {
  if (!iconName) return null;

  const IconComponent = ICON_MAP[iconName as keyof typeof ICON_MAP];
  if (!IconComponent) return null;

  return <IconComponent className={className} />;
}

