"use client";

import { useMemo } from 'react';
import { useUnifiedSearch } from '@/hooks/useUnifiedSearch';
import { useListVirtualization } from '@/hooks/useVirtualization';
import { usePrefetchNextPage } from '@/hooks/usePrefetch';
import { useRenderTime } from '@/hooks/usePerformance';
import { PartCard } from '@/components/catalog/cards/PartCard';
import CatalogItemCard from '@/components/catalog/cards/CatalogItemCard';
import { LoadingState } from '@/components/shared/LoadingState';
import { EmptyState } from '@/components/shared/EmptyState';
import { Database } from 'lucide-react';

export default function SearchIsland() {
  const { query, results, isLoading, hasNextPage, fetchNextPage, isFetchingNextPage, unifiedQuery } = useUnifiedSearch();
  const { renderTime } = useRenderTime('SearchIsland', import.meta.env.DEV);

  const allResults = useMemo(() => {
    return results.pages.flatMap(page => page.items);
  }, [results]);

  const itemHeight = 180; // Средняя высота карточки

  const { virtualizer, virtualItems, totalSize, containerRef } = useListVirtualization({
    items: allResults,
    itemHeight,
    enabled: allResults.length > 20,
    overscan: 5,
  });

  // Prefetching следующей страницы
  usePrefetchNextPage(
    ['unifiedSearch', query],
    unifiedQuery,
    results.pages,
    hasNextPage
  );

  const renderItem = (item: any) => {
    switch (item.type) {
      case 'part':
        return <PartCard part={item.data} animationDelay={0} />;
      case 'catalogItem':
        return <CatalogItemCard item={item.data} animationDelay={0} />;
      // Добавить рендеринг для других типов (category, brand)
      default:
        return null;
    }
  };

  return (
    <div ref={containerRef} className="flex-1 overflow-y-auto">
      {isLoading && <LoadingState variant="card" count={10} />}
      
      {!isLoading && allResults.length === 0 && (
        <EmptyState icon={Database} title="Ничего не найдено" description="Попробуйте изменить ваш запрос." />
      )}

      {!isLoading && allResults.length > 0 && (
        <div
          className="relative"
          style={{ height: `${totalSize}px` }}
        >
          <div
            className="absolute top-0 left-0 w-full"
            style={{
              transform: `translateY(${(virtualItems[0] as any)?.start ?? 0}px)`,
            }}
          >
            {(virtualItems as any[]).map((virtualItem) => {
              const item = allResults[virtualItem.index];
              return (
                <div
                  key={virtualItem.key}
                  data-index={virtualItem.index}
                  ref={virtualizer?.measureElement}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    transform: `translateY(${virtualItem.start}px)`,
                  }}
                >
                  {renderItem(item)}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {isFetchingNextPage && <LoadingState variant="card" count={2} />}

      {/* Trigger для загрузки следующей страницы */}
      {!isLoading && hasNextPage && !isFetchingNextPage && (
        <div ref={(node) => {
          if (node) {
            const observer = new IntersectionObserver(
              (entries) => {
                if (entries[0].isIntersecting) {
                  fetchNextPage();
                }
              },
              { threshold: 1.0 }
            );
            observer.observe(node);
          }
        }}>
          <LoadingState variant="card" count={1} />
        </div>
      )}
    </div>
  );
}