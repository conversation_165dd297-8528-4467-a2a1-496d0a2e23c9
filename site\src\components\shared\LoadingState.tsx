"use client";

import * as React from "react";
import { motion } from "motion/react";
import { cn } from "@/lib/utils";
import { fadeIn, smoothTransition } from '@/lib/animation-variants';
import { ModernCard, ModernCardContent } from "@/components/ui/modern-card";

// ============================================================================
// Types
// ============================================================================

export interface LoadingStateProps {
  variant?: "card" | "table" | "list" | "text" | "page" | "inline";
  count?: number;
  className?: string;
  message?: string;
}

// ============================================================================
// Skeleton Sub-components
// ============================================================================

/**
 * Базовый скелетон-блок с пульсацией
 */
export const SkeletonBox = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <motion.div
    ref={ref}
    className={cn("bg-muted/50 rounded animate-pulse", className)}
    initial={{ opacity: 0.6 }}
    animate={{ opacity: [0.6, 0.8, 0.6] }}
    transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
    {...props}
  />
));
SkeletonBox.displayName = "SkeletonBox";

/**
 * Круглый скелетон (для аватаров/иконок)
 */
export const SkeletonCircle = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { size?: number }
>(({ className, size = 12, ...props }, ref) => (
  <SkeletonBox
    ref={ref}
    className={cn("rounded-full", className)}
    style={{ width: size * 4, height: size * 4 }}
    {...props}
  />
));
SkeletonCircle.displayName = "SkeletonCircle";

/**
 * Текстовая линия скелетона
 */
export const SkeletonText = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { width?: string }
>(({ className, width = "100%", ...props }, ref) => (
  <SkeletonBox
    ref={ref}
    className={cn("h-4", className)}
    style={{ width }}
    {...props}
  />
));
SkeletonText.displayName = "SkeletonText";

/**
 * Скелетон изображения
 */
export const SkeletonImage = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { aspectRatio?: string }
>(({ className, aspectRatio = "1/1", ...props }, ref) => (
  <SkeletonBox
    ref={ref}
    className={cn("w-full", className)}
    style={{ aspectRatio }}
    {...props}
  />
));
SkeletonImage.displayName = "SkeletonImage";

// ============================================================================
// Variant Components
// ============================================================================

/**
 * Скелетон карточки
 */
const SkeletonCard: React.FC = () => (
  <ModernCard variant="elevated" className="p-4">
    <div className="flex gap-4">
      <SkeletonBox className="h-24 w-24 flex-shrink-0" />
      <div className="flex-1 space-y-3">
        <SkeletonText width="75%" className="h-5" />
        <SkeletonText width="50%" className="h-4" />
        <div className="flex gap-2">
          <SkeletonBox className="h-5 w-20 rounded-full" />
          <SkeletonBox className="h-5 w-24 rounded-full" />
        </div>
      </div>
    </div>
  </ModernCard>
);

/**
 * Скелетон строки таблицы
 */
const SkeletonTableRow: React.FC = () => (
  <div className="flex gap-4 p-4 border-b border-border">
    <SkeletonBox className="h-10 w-10 rounded" />
    <div className="flex-1 space-y-2">
      <SkeletonText width="60%" className="h-4" />
      <SkeletonText width="40%" className="h-3" />
    </div>
    <SkeletonBox className="h-8 w-20 rounded" />
  </div>
);

/**
 * Скелетон элемента списка
 */
const SkeletonListItem: React.FC = () => (
  <div className="flex gap-3 p-3 border-b border-border">
    <SkeletonCircle size={10} />
    <div className="flex-1 space-y-2">
      <SkeletonText width="70%" className="h-4" />
      <SkeletonText width="50%" className="h-3" />
    </div>
  </div>
);

/**
 * Скелетон текста
 */
const SkeletonTextBlock: React.FC = () => (
  <div className="space-y-2">
    <SkeletonText width="95%" />
    <SkeletonText width="90%" />
    <SkeletonText width="80%" />
    <SkeletonText width="85%" />
  </div>
);

/**
 * Скелетон полной страницы
 */
const SkeletonPage: React.FC = () => (
  <div className="space-y-6">
    {/* Header */}
    <div className="space-y-2">
      <SkeletonText width="40%" className="h-8" />
      <SkeletonText width="60%" className="h-4" />
    </div>

    {/* Content */}
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="md:col-span-2 space-y-4">
        <SkeletonCard />
        <SkeletonCard />
        <SkeletonCard />
      </div>
      <div className="space-y-4">
        <ModernCard className="p-4 space-y-3">
          <SkeletonText width="50%" className="h-5" />
          <SkeletonBox className="h-40" />
        </ModernCard>
      </div>
    </div>
  </div>
);

/**
 * Встроенный скелетон (для кнопок/бейджей)
 */
const SkeletonInline: React.FC = () => (
  <SkeletonBox className="h-6 w-16 rounded inline-block" />
);

// ============================================================================
// Main Component
// ============================================================================

/**
 * Компонент состояния загрузки с различными вариантами скелетонов
 */
export const LoadingState = React.forwardRef<HTMLDivElement, LoadingStateProps>(
  ({ variant = "card", count = 3, className, message }, ref) => {
    // Поддержка prefers-reduced-motion
    const prefersReducedMotion =
      typeof window !== "undefined" &&
      window.matchMedia("(prefers-reduced-motion: reduce)").matches;

    const renderSkeleton = () => {
      switch (variant) {
        case "card":
          return Array.from({ length: count }).map((_, i) => (
            <SkeletonCard key={i} />
          ));
        case "table":
          return (
            <div className="border border-border rounded-md overflow-hidden">
              {Array.from({ length: count }).map((_, i) => (
                <SkeletonTableRow key={i} />
              ))}
            </div>
          );
        case "list":
          return (
            <div className="border border-border rounded-md overflow-hidden">
              {Array.from({ length: count }).map((_, i) => (
                <SkeletonListItem key={i} />
              ))}
            </div>
          );
        case "text":
          return Array.from({ length: count }).map((_, i) => (
            <SkeletonTextBlock key={i} />
          ));
        case "page":
          return <SkeletonPage />;
        case "inline":
          return <SkeletonInline />;
        default:
          return Array.from({ length: count }).map((_, i) => (
            <SkeletonCard key={i} />
          ));
      }
    };

    return (
      <div ref={ref} className={cn("w-full", className)}>
        {message && (
          <div className="text-center mb-4 text-sm text-muted-foreground">
            {message}
          </div>
        )}
        {prefersReducedMotion ? (
          <div
            className={cn("space-y-3", {
              "space-y-0": variant === "table" || variant === "list",
            })}
          >
            {renderSkeleton()}
          </div>
        ) : (
          <motion.div
            className={cn("space-y-3", {
              "space-y-0": variant === "table" || variant === "list",
            })}
            variants={fadeIn}
            initial="initial"
            animate="animate"
            transition={smoothTransition}
          >
            {renderSkeleton()}
          </motion.div>
        )}
      </div>
    );
  }
);

LoadingState.displayName = "LoadingState";

export default LoadingState;

