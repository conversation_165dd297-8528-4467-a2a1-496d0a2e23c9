---
import MainLayout from "../layouts/MainLayout.astro";
import SearchHeaderIsland from "@/components/catalog/islands/SearchHeaderIsland";
import CatalogIsland from "@/components/catalog/islands/CatalogIsland";
---

<MainLayout title="Каталог запчастей" description="Поиск и просмотр каталога взаимозаменяемых запчастей">
  <Fragment slot="head">
    {/* Preconnect to API server */}
    <link rel="preconnect" href={import.meta.env.PUBLIC_API_URL || 'http://localhost:3000'} />
    <link rel="dns-prefetch" href={import.meta.env.PUBLIC_API_URL || 'http://localhost:3000'} />
  </Fragment>
  <div class="min-h-screen bg-background">
    <SearchHeaderIsland client:load />
    <CatalogIsland client:load />
  </div>
</MainLayout>
