/**
 * Search Provider Abstraction
 * 
 * Позволяет легко переключаться между поисковыми движками:
 * - PostgreSQL (текущий): full-text search с ts_rank
 * - <PERSON><PERSON><PERSON>ear<PERSON> (будущее): продвинутый поиск с typo tolerance
 * 
 * Для переключения: изменить SEARCH_PROVIDER в .env
 */

import type { PrismaClient } from '@prisma/client'
import type { Part, CatalogItem, PartCategory, Brand } from '@/types/zod'

// TODO: This should be imported from a central place, maybe api package
declare const db: PrismaClient

export interface SearchProvider {
  search(params: SearchParams): Promise<SearchResponse>
  suggest(query: string): Promise<string[]>
  index(documents: SearchDocument[]): Promise<void>
}

export type SearchFilters = {
  // Define filter structure
}

export type SearchParams = {
  query: string
  types?: Array<'parts' | 'catalogItems' | 'categories' | 'brands'>
  filters?: SearchFilters
  limit?: number
  offset?: number
  includeTypoSuggestions?: boolean
}

export type SearchResponse = {
  parts: Array<Part & { relevanceScore: number }>
  catalogItems: Array<CatalogItem & { relevanceScore: number }>
  categories: Array<PartCategory & { relevanceScore: number }>
  brands: Array<Brand & { relevanceScore: number }>
  suggestions: string[]
  totalCount: number
  searchTime: number // milliseconds
}

export type SearchDocument = {
  id: string
  type: 'part' | 'catalogItem' | 'category' | 'brand'
  content: string
  metadata: Record<string, any>
}

export class PostgresSearchProvider implements SearchProvider {
  constructor(private db: PrismaClient) {}
  
  async search(params: SearchParams): Promise<SearchResponse> {
    // Реализация через PostgreSQL full-text search
    // Использует ts_rank для ранжирования
    // Возвращает результаты с relevanceScore
    // This is a placeholder implementation. The actual logic is in `site.service.ts` for now.
    console.log('Searching with Postgres:', params)
    return {
      parts: [],
      catalogItems: [],
      categories: [],
      brands: [],
      suggestions: [],
      totalCount: 0,
      searchTime: 0
    }
  }
  
  async suggest(query: string): Promise<string[]> {
    // Реализация через pg_trgm similarity
    // Возвращает похожие запросы
    console.log('Suggesting with Postgres:', query)
    return []
  }
  
  async index(documents: SearchDocument[]): Promise<void> {
    // Для PostgreSQL не требуется (использует встроенные индексы)
    console.log('Indexing with Postgres not required.', documents.length)
    return Promise.resolve()
  }
}

export class MeiliSearchProvider implements SearchProvider {
  // TODO: Implement when MeiliSearch is ready
  // Will use MeiliSearch client library
  // Much simpler implementation with better performance
  async search(params: SearchParams): Promise<SearchResponse> {
    throw new Error('MeiliSearch not implemented yet')
  }
  async suggest(query: string): Promise<string[]> {
    throw new Error('MeiliSearch not implemented yet')
  }
  async index(documents: SearchDocument[]): Promise<void> {
    throw new Error('MeiliSearch not implemented yet')
  }
}

export function createSearchProvider(type: 'postgres' | 'meilisearch' = 'postgres'): SearchProvider {
  if (type === 'meilisearch') {
    throw new Error('MeiliSearch not implemented yet')
    // return new MeiliSearchProvider()
  }
  // This is a placeholder. In a real scenario, the db client would be injected.
  return new PostgresSearchProvider(db)
}

export const SEARCH_PROVIDER = process.env.SEARCH_PROVIDER || 'postgres'