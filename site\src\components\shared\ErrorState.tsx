"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { AlertCircle, RefreshCw, ChevronDown } from "lucide-react";
import {
  ModernCard,
  ModernCardContent,
  ModernCardDescription,
  ModernCardHeader,
  ModernCardTitle,
} from "@/components/ui/modern-card";
import { ModernButton } from "@/components/ui/modern-button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

// ============================================================================
// Types
// ============================================================================

export interface ErrorStateProps {
  error?: Error | string | null;
  title?: string;
  message?: string;
  variant?: "default" | "inline" | "minimal";
  showRetry?: boolean;
  onRetry?: () => void;
  showDetails?: boolean;
  className?: string;
}

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Извлечь сообщение об ошибке из различных типов ошибок
 */
function extractErrorMessage(error: Error | string | null | undefined): string {
  if (!error) return "Неизвестная ошибка";

  // Строковая ошибка
  if (typeof error === "string") {
    return error;
  }

  // Error объект
  if (error instanceof Error) {
    return error.message;
  }

  // tRPC error или React Query error
  if (typeof error === "object" && error !== null && "message" in error) {
    return String(error.message);
  }

  return "Произошла ошибка";
}

/**
 * Получить технические детали ошибки
 */
function getErrorDetails(error: Error | string | null | undefined): string {
  if (!error) return "";

  if (typeof error === "string") return error;

  if (error instanceof Error) {
    const details = [
      `Сообщение: ${error.message}`,
      error.name && `Тип: ${error.name}`,
      error.stack && `Stack trace:\n${error.stack}`,
    ]
      .filter(Boolean)
      .join("\n\n");
    return details;
  }

  // Fallback для других объектов
  try {
    return JSON.stringify(error, null, 2);
  } catch {
    return String(error);
  }
}

// ============================================================================
// Variant Components
// ============================================================================

/**
 * Полный вариант ошибки с карточкой
 */
const ErrorStateDefault: React.FC<Omit<ErrorStateProps, "variant">> = ({
  error,
  title = "Произошла ошибка",
  message,
  showRetry = true,
  onRetry,
  showDetails = false,
  className,
}) => {
  const [isDetailsOpen, setIsDetailsOpen] = React.useState(false);
  const errorMessage = message || extractErrorMessage(error);
  const errorDetails = getErrorDetails(error);

  return (
    <ModernCard
      variant="elevated"
      className={cn(
        "border-red-200 bg-red-50 dark:bg-red-950/20 dark:border-red-900",
        className
      )}
    >
      <ModernCardHeader>
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0">
            <div className="h-10 w-10 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
              <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <ModernCardTitle className="text-red-900 dark:text-red-100">
              {title}
            </ModernCardTitle>
            <ModernCardDescription className="text-red-700 dark:text-red-300 mt-1">
              {errorMessage}
            </ModernCardDescription>
          </div>
        </div>
      </ModernCardHeader>

      <ModernCardContent>
        <div className="flex flex-col gap-3">
          {showRetry && onRetry && (
            <div className="flex gap-2">
              <ModernButton
                onClick={onRetry}
                variant="outline"
                size="sm"
                className="border-red-300 text-red-700 hover:bg-red-100 dark:border-red-800 dark:text-red-300 dark:hover:bg-red-900/30"
              >
                <RefreshCw className="h-4 w-4" />
                Попробовать еще раз
              </ModernButton>
            </div>
          )}

          {showDetails && errorDetails && (
            <Collapsible open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
              <CollapsibleTrigger asChild>
                <button
                  type="button"
                  className={cn(
                    "w-full flex items-center justify-between text-red-700 hover:bg-red-100 dark:text-red-300 dark:hover:bg-red-900/30 p-2 rounded-md transition-colors",
                    "text-xs"
                  )}
                >
                  <span>Технические детали</span>
                  <ChevronDown
                    className={cn(
                      "h-4 w-4 transition-transform",
                      isDetailsOpen && "rotate-180"
                    )}
                  />
                </button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2">
                <pre className="text-xs bg-red-100 dark:bg-red-900/20 p-3 rounded-md overflow-x-auto text-red-900 dark:text-red-200 font-mono">
                  {errorDetails}
                </pre>
              </CollapsibleContent>
            </Collapsible>
          )}
        </div>
      </ModernCardContent>
    </ModernCard>
  );
};

/**
 * Встроенный вариант ошибки (для форм)
 */
const ErrorStateInline: React.FC<Omit<ErrorStateProps, "variant">> = ({
  error,
  message,
  className,
}) => {
  const errorMessage = message || extractErrorMessage(error);

  return (
    <div
      className={cn(
        "flex items-center gap-2 text-sm text-red-600 dark:text-red-400 p-2 rounded-md bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-900",
        className
      )}
    >
      <AlertCircle className="h-4 w-4 flex-shrink-0" />
      <span className="flex-1">{errorMessage}</span>
    </div>
  );
};

/**
 * Минимальный вариант ошибки
 */
const ErrorStateMinimal: React.FC<Omit<ErrorStateProps, "variant">> = ({
  error,
  message,
  className,
}) => {
  const errorMessage = message || extractErrorMessage(error);

  return (
    <div
      className={cn(
        "flex items-center gap-2 text-sm text-red-600 dark:text-red-400",
        className
      )}
    >
      <AlertCircle className="h-4 w-4 flex-shrink-0" />
      <span>{errorMessage}</span>
    </div>
  );
};

// ============================================================================
// Main Component
// ============================================================================

/**
 * Компонент отображения ошибки с возможностью повтора
 */
export const ErrorState = React.forwardRef<HTMLDivElement, ErrorStateProps>(
  ({ variant = "default", ...props }, ref) => {
    switch (variant) {
      case "inline":
        return (
          <div ref={ref}>
            <ErrorStateInline {...props} />
          </div>
        );
      case "minimal":
        return (
          <div ref={ref}>
            <ErrorStateMinimal {...props} />
          </div>
        );
      case "default":
      default:
        return (
          <div ref={ref}>
            <ErrorStateDefault {...props} />
          </div>
        );
    }
  }
);

ErrorState.displayName = "ErrorState";

export default ErrorState;

