import { useState, useRef, useEffect } from 'react'
import { motion, type PanInfo } from 'motion/react'
import type { MediaAsset } from '@/lib/types'
import { ArrowLeftRight, ArrowUpDown } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface MediaComparisonProps {
  beforeImage: MediaAsset
  afterImage: MediaAsset
  beforeLabel?: string
  afterLabel?: string
  initialPosition?: number
  orientation?: 'horizontal' | 'vertical'
  showLabels?: boolean
  className?: string
}

export default function MediaComparison({
  beforeImage,
  afterImage,
  beforeLabel = 'До',
  afterLabel = 'После',
  initialPosition = 50,
  orientation = 'horizontal',
  showLabels = true,
  className,
}: MediaComparisonProps) {
  const [position, setPosition] = useState(initialPosition)
  const [isDragging, setIsDragging] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const step = 5
      if (orientation === 'horizontal') {
        if (e.key === 'ArrowLeft') {
          e.preventDefault()
          setPosition((prev) => Math.max(0, prev - step))
        } else if (e.key === 'ArrowRight') {
          e.preventDefault()
          setPosition((prev) => Math.min(100, prev + step))
        }
      } else {
        if (e.key === 'ArrowUp') {
          e.preventDefault()
          setPosition((prev) => Math.max(0, prev - step))
        } else if (e.key === 'ArrowDown') {
          e.preventDefault()
          setPosition((prev) => Math.min(100, prev + step))
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [orientation])

  const updatePosition = (clientX: number, clientY: number) => {
    const rect = containerRef.current?.getBoundingClientRect()
    if (!rect) return

    const newPosition =
      orientation === 'horizontal'
        ? ((clientX - rect.left) / rect.width) * 100
        : ((clientY - rect.top) / rect.height) * 100

    setPosition(Math.max(0, Math.min(100, newPosition)))
  }

  const handleDrag = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    updatePosition(info.point.x, info.point.y)
  }

  const handleClick = (e: React.MouseEvent) => {
    if (isDragging) return
    updatePosition(e.clientX, e.clientY)
  }

  const clipPath =
    orientation === 'horizontal'
      ? `inset(0 ${100 - position}% 0 0)`
      : `inset(0 0 ${100 - position}% 0)`

  const sliderPosition =
    orientation === 'horizontal'
      ? { left: `${position}%`, top: '50%' }
      : { top: `${position}%`, left: '50%' }

  return (
    <div
      ref={containerRef}
      className={cn('relative w-full h-full overflow-hidden', className)}
      onClick={handleClick}
      style={{
        cursor: orientation === 'horizontal' ? 'col-resize' : 'row-resize',
      }}
    >
      {/* Before image (bottom layer) */}
      <div className="absolute inset-0">
        <img
          src={beforeImage.url}
          alt={beforeImage.fileName}
          className="w-full h-full object-cover"
          draggable={false}
        />
        {showLabels && (
          <div className="absolute top-4 left-4 bg-black/60 backdrop-blur-sm text-white px-3 py-1.5 rounded-md text-sm font-medium">
            {beforeLabel}
          </div>
        )}
      </div>

      {/* After image (top layer with clip-path) */}
      <div
        className="absolute inset-0"
        style={{
          clipPath,
        }}
      >
        <img
          src={afterImage.url}
          alt={afterImage.fileName}
          className="w-full h-full object-cover"
          draggable={false}
        />
        {showLabels && (
          <div className="absolute top-4 right-4 bg-black/60 backdrop-blur-sm text-white px-3 py-1.5 rounded-md text-sm font-medium">
            {afterLabel}
          </div>
        )}
      </div>

      {/* Slider divider */}
      <div
        className={cn(
          'absolute z-10 pointer-events-none',
          orientation === 'horizontal'
            ? 'top-0 bottom-0 w-1 -translate-x-1/2'
            : 'left-0 right-0 h-1 -translate-y-1/2',
          'bg-white shadow-lg'
        )}
        style={sliderPosition}
      />

      {/* Slider handle */}
      <motion.div
        drag={orientation === 'horizontal' ? 'x' : 'y'}
        dragConstraints={containerRef}
        dragElastic={0}
        dragMomentum={false}
        onDragStart={() => setIsDragging(true)}
        onDrag={handleDrag}
        onDragEnd={() => setIsDragging(false)}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        className={cn(
          'absolute z-20 w-10 h-10 sm:w-12 sm:h-12 rounded-full',
          'bg-white border-2 border-primary shadow-lg',
          'flex items-center justify-center',
          'touch-none select-none',
          'transition-shadow hover:shadow-xl',
          orientation === 'horizontal' ? 'cursor-col-resize' : 'cursor-row-resize'
        )}
        style={{
          left: orientation === 'horizontal' ? `${position}%` : '50%',
          top: orientation === 'vertical' ? `${position}%` : '50%',
          transform:
            orientation === 'horizontal'
              ? 'translate(-50%, -50%)'
              : 'translate(-50%, -50%)',
        }}
        aria-label={`Ползунок сравнения, позиция ${Math.round(position)}%`}
        role="slider"
        aria-valuenow={Math.round(position)}
        aria-valuemin={0}
        aria-valuemax={100}
      >
        {orientation === 'horizontal' ? (
          <ArrowLeftRight className="w-5 h-5 text-primary" />
        ) : (
          <ArrowUpDown className="w-5 h-5 text-primary" />
        )}
      </motion.div>
    </div>
  )
}

