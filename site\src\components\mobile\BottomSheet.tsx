import React from 'react';
import {
  <PERSON>er,
  <PERSON>er<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  DrawerTitle,
  DrawerDescription,
  DrawerFooter,
  DrawerClose,
} from '@/components/ui/drawer';
import { useIsMobile } from '@/hooks/useMediaQuery';
import { cn } from '@/lib/utils';

export interface BottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  children: React.ReactNode;
  snapPoints?: (number | string)[];
  defaultSnap?: number | string;
  className?: string;
}

export function BottomSheet({
  isOpen,
  onClose,
  title,
  description,
  children,
  snapPoints,
  defaultSnap,
  className,
}: BottomSheetProps) {
  const isMobile = useIsMobile();

  if (!isMobile) {
    // On desktop, you might want to render nothing or a modal instead.
    // For this case, we render nothing as the trigger is mobile-only.
    return null;
  }

  return (
    <Drawer
      open={isOpen}
      onClose={onClose}
      snapPoints={snapPoints}
      activeSnapPoint={defaultSnap}
    >
      <DrawerContent className={cn("flex flex-col h-full max-h-[95vh]", className)}>
        <DrawerHeader className="text-left">
          <DrawerTitle>{title}</DrawerTitle>
          {description && <DrawerDescription>{description}</DrawerDescription>}
        </DrawerHeader>
        <div className="flex-1 overflow-y-auto p-4">
          {children}
        </div>
      </DrawerContent>
    </Drawer>
  );
}

export default BottomSheet;