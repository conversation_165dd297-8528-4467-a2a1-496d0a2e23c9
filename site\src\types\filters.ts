/**
 * Расширенные типы для фильтров
 */

import type { CatalogSearchFilters } from './catalog';

// ============================================================================
// Filter Presets
// ============================================================================

/**
 * Пресет фильтров для сохранения/загрузки комбинаций фильтров
 */
export type FilterPreset = {
  id: string;
  name: string;
  description?: string;
  filters: CatalogSearchFilters;
  isDefault?: boolean;
  createdAt: Date;
  updatedAt: Date;
};

/**
 * Быстрый фильтр для общих комбинаций
 */
export type QuickFilter = {
  id: string;
  label: string;
  icon?: string;
  filters: Partial<CatalogSearchFilters>;
  badge?: string; // Например, "OEM", "Популярное"
};

// ============================================================================
// Numeric Range
// ============================================================================

/**
 * Числовой диапазон с валидацией
 */
export type NumericRangeFilter = {
  min?: number;
  max?: number;
  unit?: string;
};

// ============================================================================
// Attribute Filters
// ============================================================================

/**
 * Значение фильтра атрибута с несколькими режимами выбора
 */
export type AttributeFilterValue = {
  values?: string[]; // Для строковых атрибутов (множественный выбор)
  numericRange?: NumericRangeFilter; // Для числовых атрибутов
  operator?: 'AND' | 'OR'; // Как комбинировать несколько значений
};

// ============================================================================
// Extended Filters
// ============================================================================

/**
 * Расширенные фильтры поиска с метаданными
 */
export type ExtendedCatalogSearchFilters = CatalogSearchFilters & {
  sortBy?: 'relevance' | 'name' | 'updatedAt' | 'createdAt';
  sortDir?: 'asc' | 'desc';
  page?: number;
  limit?: number;
};

// ============================================================================
// Filter State
// ============================================================================

/**
 * Состояние фильтров для UI компонентов
 */
export type FilterState = {
  filters: CatalogSearchFilters;
  activePreset?: FilterPreset;
  isDirty: boolean; // Изменились ли фильтры с момента применения пресета
  appliedCount: number; // Количество активных фильтров
};

// ============================================================================
// Filter Metadata
// ============================================================================

/**
 * Метаданные фильтров для рендеринга UI
 */
export type FilterMetadata = {
  totalResults: number;
  filteredResults: number;
  availableValues: Record<number, string[]>; // templateId -> доступные значения
  availableValuesWithCounts?: Record<number, Array<{ value: string; count: number }>>; // templateId -> значения с подсчетами
  numericRanges: Record<number, { min: number; max: number }>; // templateId -> диапазон
};

// ============================================================================
// Filter History
// ============================================================================

/**
 * История фильтров для последних поисков
 */
export type FilterHistory = {
  id: string;
  filters: CatalogSearchFilters;
  timestamp: Date;
  resultCount: number;
};

// ============================================================================
// Validation
// ============================================================================

/**
 * Результат валидации фильтров
 */
export type FilterValidationResult = {
  isValid: boolean;
  errors: Record<string, string[]>;
  warnings: Record<string, string[]>;
};


