import type { BrandItem } from '@/types/catalog';
import BaseCard from './cards/BaseCard';
import { createCard<PERSON>lickHandler, createBrandUrl, createOemBadge } from './cards/card-utils';

interface BrandCardProps {
  brand: BrandItem;
  onClick?: () => void;
}

export function BrandCard({ brand, onClick }: BrandCardProps) {
  const handleClick = createCardClickHandler(createBrandUrl(brand.slug), onClick);

  // Prepare OEM badge
  const oemBadge = createOemBadge(brand.isOem);

  return (
    <BaseCard
      variant="brand"
      layout="detailed"
      onClick={handleClick}
    >
      <div className="space-y-3">
        {/* Header */}
        <BaseCard.Header 
          title={brand.name}
          description={brand.country}
        />

        {/* Badges */}
        <BaseCard.Badges badges={[oemBadge]} />

        {/* Counts section */}
        {brand._count && (
          <BaseCard.Body>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Запчасти:</span>
                <span className="font-medium">{brand._count.catalogItems}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Модели:</span>
                <span className="font-medium">{brand._count.equipmentModel}</span>
              </div>
            </div>
          </BaseCard.Body>
        )}
      </div>
    </BaseCard>
  );
}
