"use client"

import { motion } from "motion/react"
import { Star, Users, Database, Building2, CheckCircle, Quote } from "lucide-react"
import { ModernCard, ModernCardContent } from "@/components/ui/modern-card"
import { Badge } from "@/components/ui/badge"

const testimonials = [
  {
    name: "Алексей Петров",
    role: "Главный инженер",
    company: "ТехСервис Урал",
    avatar: "АП",
    rating: 5,
    text: "PartTec изменил наш подход к подбору запчастей. Раньше тратили часы на поиск аналогов в каталогах, теперь находим нужное за минуты. Система сравнения по эталонным характеристикам — это прорыв."
  },
  {
    name: "Мария Соколова",
    role: "Специалист по закупкам",
    company: "СтройМаш",
    avatar: "МС",
    rating: 5,
    text: "Экономим до 40% на закупках благодаря возможности сравнивать цены аналогов. AI-ассистент понимает даже неточные описания деталей. Рекомендую всем, кто работает с техникой."
  },
  {
    name: "Дмитрий Иванов",
    role: "Технический директор",
    company: "АгроТех",
    avatar: "ДИ",
    rating: 5,
    text: "Наконец-то каталог, который понимает инженеров. Нормализованные атрибуты, допуски, группы синонимов — всё продумано. Интеграция с нашей ERP прошла без проблем."
  }
]

const stats = [
  {
    icon: Users,
    value: "2,340+",
    label: "Инженеров и специалистов",
    color: "from-blue-500 to-cyan-500"
  },
  {
    icon: Database,
    value: "150,000+",
    label: "Деталей в базе",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Building2,
    value: "500+",
    label: "Брендов производителей",
    color: "from-purple-500 to-pink-500"
  },
  {
    icon: CheckCircle,
    value: "98%",
    label: "Точность подбора аналогов",
    color: "from-amber-500 to-orange-500"
  }
]

export function SocialProof() {
  return (
    <div className="container mx-auto px-4 py-16">
      {/* Статистика */}
      <div className="mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl lg:text-5xl font-bold mb-4">
            Нам <span className="text-primary">доверяют</span> профессионалы
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Тысячи инженеров и специалистов по закупкам используют PartTec ежедневно
          </p>
        </motion.div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <ModernCard variant="elevated" className="text-center p-6 hover:shadow-strong transition-all duration-300">
                <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br ${stat.color} flex items-center justify-center`}>
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-3xl font-bold text-foreground mb-2">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </ModernCard>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Testimonials */}
      <div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <Badge variant="secondary" className="mb-4">
            <Star className="w-3 h-3 mr-1 fill-current" />
            Отзывы пользователей
          </Badge>
          <h3 className="text-3xl font-bold mb-2">Что говорят наши клиенты</h3>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-6 max-w-7xl mx-auto">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <ModernCard variant="elevated" className="h-full hover:shadow-strong transition-all duration-300">
                <ModernCardContent className="p-6">
                  {/* Quote icon */}
                  <Quote className="w-8 h-8 text-primary/20 mb-4" />
                  
                  {/* Rating */}
                  <div className="flex gap-1 mb-4">
                    {Array.from({ length: testimonial.rating }).map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-amber-500 text-amber-500" />
                    ))}
                  </div>

                  {/* Text */}
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    {testimonial.text}
                  </p>

                  {/* Author */}
                  <div className="flex items-center gap-3 pt-4 border-t border-border">
                    <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center font-semibold text-primary">
                      {testimonial.avatar}
                    </div>
                    <div>
                      <div className="font-semibold text-foreground">{testimonial.name}</div>
                      <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                      <div className="text-xs text-muted-foreground">{testimonial.company}</div>
                    </div>
                  </div>
                </ModernCardContent>
              </ModernCard>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  )
}
