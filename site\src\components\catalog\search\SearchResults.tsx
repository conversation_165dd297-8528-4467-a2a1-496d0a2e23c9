import { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import { Grid, List, ArrowUpDown, Loader2 } from 'lucide-react'
import type { Part, CatalogItem, PartCategory, Brand } from '@/types/zod'
import { PartCard } from '@/components/catalog/PartCard'
import { CategoryCard } from '@/components/catalog/CategoryCard'
import { BrandCard } from '@/components/catalog/BrandCard'
import LoadingState from '@/components/shared/LoadingState'
import EmptyState from '@/components/shared/EmptyState'
import { getRelevanceScoreColor, getRelevanceScoreLabel } from '@/lib/search-ranking'
import { cn } from '@/lib/utils'
import { staggerContainer, fadeInUp } from '@/lib/animation-variants'

export interface SearchResultsProps {
  query: string
  results: {
    parts: Array<Part & { relevanceScore: number }>
    catalogItems: Array<CatalogItem & { relevanceScore: number }>
    categories: Array<PartCategory>
    brands: Array<Brand>
  }
  isLoading: boolean
  viewMode?: 'grouped' | 'unified'
  sortBy?: 'relevance' | 'name' | 'date'
  onLoadMore?: () => void
  hasMore?: boolean
  className?: string
}

type SortOption = 'relevance' | 'name' | 'date'
type ViewMode = 'grouped' | 'unified'

const VIEW_MODE_KEY = 'search-view-mode'

export default function SearchResults({
  query,
  results,
  isLoading,
  viewMode: initialViewMode = 'grouped',
  sortBy: initialSortBy = 'relevance',
  onLoadMore,
  hasMore = false,
  className
}: SearchResultsProps) {
  const [viewMode, setViewMode] = useState<ViewMode>(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(VIEW_MODE_KEY) as ViewMode
      return stored || initialViewMode
    }
    return initialViewMode
  })

  const [sortBy, setSortBy] = useState<SortOption>(initialSortBy)

  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode)
    if (typeof window !== 'undefined') {
      localStorage.setItem(VIEW_MODE_KEY, mode)
    }
  }

  // Sort results
  const sortedResults = useMemo(() => {
    const { parts, catalogItems, categories, brands } = results

    const sortFunction = (a: unknown, b: unknown) => {
      if (sortBy === 'relevance') {
        const scoreA = 'relevanceScore' in a ? (a as { relevanceScore: number }).relevanceScore : 0
        const scoreB = 'relevanceScore' in b ? (b as { relevanceScore: number }).relevanceScore : 0
        return scoreB - scoreA
      } else if (sortBy === 'name') {
        const nameA = 'name' in a ? String(a.name || '') : ('sku' in a ? String(a.sku || '') : '')
        const nameB = 'name' in b ? String(b.name || '') : ('sku' in b ? String(b.sku || '') : '')
        return nameA.localeCompare(nameB)
      } else {
        const dateA = 'updatedAt' in a ? new Date(a.updatedAt as Date).getTime() : 0
        const dateB = 'updatedAt' in b ? new Date(b.updatedAt as Date).getTime() : 0
        return dateB - dateA
      }
    }

    return {
      parts: [...parts].sort(sortFunction),
      catalogItems: [...catalogItems].sort(sortFunction),
      categories: [...categories].sort((a, b) => a.name.localeCompare(b.name)),
      brands: [...brands].sort((a, b) => a.name.localeCompare(b.name))
    }
  }, [results, sortBy])

  const totalResults = useMemo(() => {
    return (
      sortedResults.parts.length +
      sortedResults.catalogItems.length +
      sortedResults.categories.length +
      sortedResults.brands.length
    )
  }, [sortedResults])


  if (isLoading) {
    return (
      <div className={cn('w-full', className)}>
        <LoadingState variant="card" message="Загрузка результатов..." />
      </div>
    )
  }

  if (totalResults === 0) {
    return (
      <div className={cn('w-full', className)}>
        <EmptyState
          title={`Ничего не найдено для "${query}"`}
          message="Попробуйте изменить поисковый запрос"
        >
          <div className="mt-4 space-y-2 text-sm text-muted-foreground">
            <p>Советы по поиску:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>Проверьте правильность написания</li>
              <li>Попробуйте использовать другие ключевые слова</li>
              <li>Используйте более общие термины</li>
              <li>Используйте фильтры для уточнения поиска</li>
            </ul>
          </div>
        </EmptyState>
      </div>
    )
  }

  return (
    <div className={cn('w-full space-y-4', className)}>
      {/* Controls */}
      <div className="flex items-center justify-between gap-4 flex-wrap">
        <div className="text-sm text-muted-foreground">
          Найдено результатов: <span className="font-semibold text-foreground">{totalResults}</span>
        </div>

        <div className="flex items-center gap-2">
          {/* Sort Dropdown */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as SortOption)}
            className="px-3 py-2 border rounded-lg text-sm bg-background"
          >
            <option value="relevance">По релевантности</option>
            <option value="name">По названию</option>
            <option value="date">По дате</option>
          </select>

          {/* View Mode Toggle */}
          <div className="flex items-center border rounded-lg overflow-hidden">
            <button
              onClick={() => handleViewModeChange('grouped')}
              className={cn(
                'px-3 py-2 text-sm transition-colors',
                viewMode === 'grouped'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-background hover:bg-muted'
              )}
              aria-label="Группированный вид"
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => handleViewModeChange('unified')}
              className={cn(
                'px-3 py-2 text-sm transition-colors',
                viewMode === 'unified'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-background hover:bg-muted'
              )}
              aria-label="Единый список"
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Results */}
      {viewMode === 'grouped' ? (
        <div className="space-y-8">
          {/* Parts Section */}
          {sortedResults.parts.length > 0 && (
            <motion.section
              variants={staggerContainer}
              initial="initial"
              animate="animate"
              className="space-y-4"
            >
              <h2 className="text-xl font-semibold flex items-center gap-2">
                Запчасти
                <span className="text-muted-foreground text-base">({sortedResults.parts.length})</span>
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {sortedResults.parts.map((part, index) => (
                  <motion.div key={part.id} variants={fadeInUp} className="relative">
                    <div className="absolute top-2 right-2 z-10">
                      <span
                        className={cn(
                          'px-2 py-1 rounded-full text-xs font-semibold',
                          getRelevanceScoreColor(part.relevanceScore)
                        )}
                        title={`Релевантность: ${Math.round(part.relevanceScore)}% - ${getRelevanceScoreLabel(part.relevanceScore)}`}
                      >
                        {Math.round(part.relevanceScore)}%
                      </span>
                    </div>
                    <PartCard part={part as unknown as PartListItem} animationDelay={index * 50} />
                  </motion.div>
                ))}
              </div>
            </motion.section>
          )}

          {/* Categories Section */}
          {sortedResults.categories.length > 0 && (
            <motion.section
              variants={staggerContainer}
              initial="initial"
              animate="animate"
              className="space-y-4"
            >
              <h2 className="text-xl font-semibold flex items-center gap-2">
                Категории
                <span className="text-muted-foreground text-base">({sortedResults.categories.length})</span>
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {sortedResults.categories.map((category, index) => (
                  <motion.div key={category.id} variants={fadeInUp}>
                    <CategoryCard category={category} animationDelay={index * 50} />
                  </motion.div>
                ))}
              </div>
            </motion.section>
          )}

          {/* Brands Section */}
          {sortedResults.brands.length > 0 && (
            <motion.section
              variants={staggerContainer}
              initial="initial"
              animate="animate"
              className="space-y-4"
            >
              <h2 className="text-xl font-semibold flex items-center gap-2">
                Бренды
                <span className="text-muted-foreground text-base">({sortedResults.brands.length})</span>
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {sortedResults.brands.map((brand, index) => (
                  <motion.div key={brand.id} variants={fadeInUp}>
                    <BrandCard brand={brand} animationDelay={index * 50} />
                  </motion.div>
                ))}
              </div>
            </motion.section>
          )}
        </div>
      ) : (
        // Unified View
        <motion.div
          variants={staggerContainer}
          initial="initial"
          animate="animate"
          className="space-y-4"
        >
          {[
            ...sortedResults.parts.map(p => ({ type: 'part' as const, data: p })),
            ...sortedResults.catalogItems.map(i => ({ type: 'catalogItem' as const, data: i })),
            ...sortedResults.categories.map(c => ({ type: 'category' as const, data: c })),
            ...sortedResults.brands.map(b => ({ type: 'brand' as const, data: b }))
          ].map((item, index) => (
            <motion.div key={`${item.type}-${item.data.id}`} variants={fadeInUp} className="relative">
              <div className="absolute top-2 right-2 z-10 flex items-center gap-2">
                <span className="px-2 py-1 rounded-full text-xs font-semibold bg-muted">
                  {item.type === 'part' ? 'Запчасть' : 
                   item.type === 'catalogItem' ? 'Каталог' :
                   item.type === 'category' ? 'Категория' : 'Бренд'}
                </span>
                {'relevanceScore' in item.data && (
                  <span
                    className={cn(
                      'px-2 py-1 rounded-full text-xs font-semibold',
                      getRelevanceScoreColor(item.data.relevanceScore)
                    )}
                    title={`Релевантность: ${Math.round(item.data.relevanceScore)}% - ${getRelevanceScoreLabel(item.data.relevanceScore)}`}
                  >
                    {Math.round(item.data.relevanceScore)}%
                  </span>
                )}
              </div>
              {item.type === 'part' && (
                <PartCard part={item.data as unknown as PartListItem} animationDelay={index * 50} />
              )}
              {item.type === 'category' && (
                <CategoryCard category={item.data as PartCategory} animationDelay={index * 50} />
              )}
              {item.type === 'brand' && (
                <BrandCard brand={item.data as Brand} animationDelay={index * 50} />
              )}
            </motion.div>
          ))}
        </motion.div>
      )}

      {/* Load More Button */}
      {hasMore && onLoadMore && (
        <div className="flex justify-center py-8">
          <button
            onClick={onLoadMore}
            disabled={isLoading}
            className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Загрузка...
              </>
            ) : (
              <>
                Загрузить ещё
                <ArrowUpDown className="w-4 h-4" />
              </>
            )}
          </button>
        </div>
      )}
    </div>
  )
}

// Type import for PartCard
type PartListItem = Parameters<typeof PartCard>[0]['part']

