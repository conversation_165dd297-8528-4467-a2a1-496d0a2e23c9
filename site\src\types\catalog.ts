export type AttributeTemplate = {
  id: number
  name: string
  title: string
  dataType: "STRING" | "NUMBER" | "BOOLEAN" | "DATE" | "JSON"
  unit: string | null
  isRequired: boolean
  allowedValues: string[]
  tolerance: number | null
}

export type CatalogSearchFilters = {
  query: string
  categoryIds: number[]
  brandIds: number[]
  isOemOnly: boolean
  attributeFilters: Record<
    number,
    {
      values?: string[]
      numericRange?: [number | undefined, number | undefined]
    }
  >
  // Дополнительные опциональные поля для расширенных фильтров
  sortBy?: 'sku' | 'description' | 'name' | 'createdAt' | 'updatedAt'
  sortDir?: 'asc' | 'desc'
  accuracy?: Array<'EXACT_MATCH' | 'MATCH_WITH_NOTES' | 'REQUIRES_MODIFICATION' | 'PARTIAL_MATCH'>
  hasImages?: boolean // Фильтр по наличию изображений
  offset?: number // Offset для пагинации
  limit?: number // Лимит результатов на страницу
}
