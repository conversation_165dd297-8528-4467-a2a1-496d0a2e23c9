import { useState, useEffect, useMemo } from 'react'
import { Search, X, Clock, Bookmark, TrendingUp, Building2, Layers, Mic, Filter } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Combobox,
  ComboboxInput,
  ComboboxContent,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxSeparator
} from '@/components/ui/combobox'
import {
  getSearchHistory,
  addToSearchHistory,
  removeFromHistory,
  loadSavedSearches,
  getPopularSearches,
  type SavedSearch
} from '@/lib/saved-searches'
import { fadeInUp } from '@/lib/animation-variants'
import { cn } from '@/lib/utils'

export interface EnhancedSearchFormProps {
  defaultQuery?: string
  onSearch: (query: string) => void
  suggestions?: string[] // Typo suggestions с бэкенда
  placeholder?: string
  showHistory?: boolean
  className?: string
}

export default function EnhancedSearchForm({
  defaultQuery = '',
  onSearch,
  suggestions: typoSuggestions = [], // Renamed for clarity
  placeholder = 'Поиск запчастей, категорий, брендов...',
  showHistory = true,
  className,
}: EnhancedSearchFormProps) {
  const [query, setQuery] = useState(defaultQuery)
  const [isOpen, setIsOpen] = useState(false)

  // Load suggestions from multiple sources
  const historicalSuggestions = useMemo(() => {
    const items: Array<{ label: string; value: string; type: string; icon: React.ReactNode }> = []

    if (showHistory) {
      const history = getSearchHistory().slice(0, 5)
      history.forEach(item => {
        items.push({
          label: item.query,
          value: item.query,
          type: 'history',
          icon: <Clock className="w-4 h-4" />
        })
      })
    }

    const saved = loadSavedSearches().slice(0, 5)
    saved.forEach(search => {
      items.push({
        label: `${search.name} - ${search.query}`,
        value: search.query,
        type: 'saved',
        icon: <Bookmark className="w-4 h-4" />
      })
    })

    const popular = getPopularSearches(3)
    popular.forEach(searchQuery => {
      if (!items.some(i => i.value === searchQuery)) {
        items.push({
          label: searchQuery,
          value: searchQuery,
          type: 'popular',
          icon: <TrendingUp className="w-4 h-4" />
        })
      }
    })


    return items
  }, [showHistory])

  const handleSearch = () => {
    if (query.trim()) {
      addToSearchHistory(query)
      onSearch(query)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
      setIsOpen(false)
    } else if (e.key === 'Escape') {
      setQuery('')
    }
  }

  const handleSuggestionClick = (value: string) => {
    setQuery(value)
    addToSearchHistory(value)
    onSearch(value)
    setIsOpen(false)
  }

  const handleTypoSuggestionClick = (suggestion: string) => {
    setQuery(suggestion)
    addToSearchHistory(suggestion)
    onSearch(suggestion)
  }

  const handleClear = () => {
    setQuery('')
  }

  return (
    <div className={cn('w-full space-y-3', className)}>
      <div className="relative">
        <Combobox open={isOpen} onOpenChange={setIsOpen}>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground pointer-events-none" />
            <ComboboxInput
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              className="w-full h-12 pl-10 pr-24 text-base border-2 focus:border-primary transition-colors"
            />
            <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
              {query && (
                <button
                  onClick={handleClear}
                  className="p-2 hover:bg-muted rounded-md transition-colors"
                  aria-label="Очистить"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
              <button
                onClick={handleSearch}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors font-medium"
              >
                Найти
              </button>
            </div>
          </div>

          <ComboboxContent>
            {historicalSuggestions.length === 0 ? (
              <ComboboxEmpty>Нет предложений</ComboboxEmpty>
            ) : (
              <>
                {showHistory && historicalSuggestions.filter(s => s.type === 'history').length > 0 && (
                  <ComboboxGroup heading="Недавние поиски">
                    {historicalSuggestions
                      .filter(s => s.type === 'history')
                      .map((item, index) => (
                        <ComboboxItem
                          key={`history-${index}`}
                          value={item.value}
                          onSelect={handleSuggestionClick}
                          className="flex items-center justify-between group"
                        >
                          <div className="flex items-center gap-2">
                            {item.icon}
                            <span>{item.label}</span>
                          </div>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              removeFromHistory(item.value)
                            }}
                            className="opacity-0 group-hover:opacity-100 p-1 hover:bg-muted rounded"
                            aria-label="Удалить из истории"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </ComboboxItem>
                      ))}
                  </ComboboxGroup>
                )}

                {historicalSuggestions.filter(s => s.type === 'saved').length > 0 && (
                  <>
                    <ComboboxSeparator />
                    <ComboboxGroup heading="Сохранённые поиски">
                      {historicalSuggestions
                        .filter(s => s.type === 'saved')
                        .map((item, index) => (
                          <ComboboxItem
                            key={`saved-${index}`}
                            value={item.value}
                            onSelect={handleSuggestionClick}
                          >
                            <div className="flex items-center gap-2">
                              {item.icon}
                              <span className="text-sm">{item.label}</span>
                            </div>
                          </ComboboxItem>
                        ))}
                    </ComboboxGroup>
                  </>
                )}

                {historicalSuggestions.filter(s => s.type === 'popular').length > 0 && (
                  <>
                    <ComboboxSeparator />
                    <ComboboxGroup heading="Популярные запросы">
                      {historicalSuggestions
                        .filter(s => s.type === 'popular')
                        .map((item, index) => (
                          <ComboboxItem
                            key={`popular-${index}`}
                            value={item.value}
                            onSelect={handleSuggestionClick}
                          >
                            <div className="flex items-center gap-2">
                              {item.icon}
                              <span>{item.label}</span>
                            </div>
                          </ComboboxItem>
                        ))}
                    </ComboboxGroup>
                  </>
                )}
              </>
            )}
          </ComboboxContent>
        </Combobox>
      </div>

      {/* Typo Suggestions */}
      <AnimatePresence>
        {typoSuggestions.length > 0 && (
          <motion.div
            variants={fadeInUp}
            initial="initial"
            animate="animate"
            exit="exit"
            className="p-3 bg-muted/50 rounded-lg border"
          >
            <p className="text-sm text-muted-foreground mb-2">
              Возможно, вы имели в виду:
            </p>
            <div className="flex flex-wrap gap-2">
              {typoSuggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleTypoSuggestionClick(suggestion)}
                  className="px-3 py-1 bg-background hover:bg-primary hover:text-primary-foreground border rounded-md text-sm transition-colors"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

