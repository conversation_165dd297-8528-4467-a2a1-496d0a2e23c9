import { useRef, useState, useEffect, useCallback } from 'react';
import type { RefObject } from 'react';
import { animate } from 'motion';

interface PullToRefreshOptions {
  onRefresh: () => Promise<any>;
  threshold?: number;
  resistance?: number;
  enabled?: boolean;
}

export function usePullToRefresh({
  onRefresh,
  threshold = 80,
  resistance = 2.5,
  enabled = true,
}: PullToRefreshOptions): {
  containerRef: RefObject<HTMLDivElement>;
  isPulling: boolean;
  pullDistance: number;
  isRefreshing: boolean;
} {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isPulling, setIsPulling] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const touchStartRef = useRef<{ y: number; x: number } | null>(null);

  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (isRefreshing || !enabled) return;
    const { clientY, clientX } = e.touches[0];
    const container = containerRef.current;
    if (container && container.scrollTop === 0) {
      touchStartRef.current = { y: clientY, x: clientX };
      setIsPulling(true);
    }
  }, [isRefreshing, enabled]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!touchStartRef.current || isRefreshing || !enabled) return;

    const { clientY } = e.touches[0];
    const deltaY = clientY - touchStartRef.current.y;

    if (deltaY < 0) {
      touchStartRef.current = null;
      setIsPulling(false);
      setPullDistance(0);
      return;
    }

    if (window.scrollY === 0) {
        e.preventDefault();
    }


    const resistedPull = deltaY / resistance;
    setPullDistance(resistedPull);
  }, [isRefreshing, enabled, resistance]);

  const handleTouchEnd = useCallback(async () => {
    if (!touchStartRef.current || isRefreshing || !enabled) return;

    if (pullDistance >= threshold) {
      setIsRefreshing(true);
      setPullDistance(threshold); // Lock at threshold
      
      try {
        await onRefresh();
      } finally {
        // Haptic feedback for completion
        if ('vibrate' in navigator) navigator.vibrate(50);
      }
    }

    setIsPulling(false);
    touchStartRef.current = null;
    
    // Animate back
    animate(
      (progress) => {
        setPullDistance(pullDistance * (1 - progress));
      },
      {
        duration: 0.5,
        ease: [0.1, 1, 0.2, 1], // Fast snap-back
        onComplete: () => {
          setIsRefreshing(false);
          setPullDistance(0);
        }
      }
    );

  }, [pullDistance, threshold, isRefreshing, enabled, onRefresh]);

  useEffect(() => {
    const el = containerRef.current;
    const isTouchDevice = 'ontouchstart' in window;

    if (el && isTouchDevice && enabled) {
      el.addEventListener('touchstart', handleTouchStart, { passive: false });
      el.addEventListener('touchmove', handleTouchMove, { passive: false });
      el.addEventListener('touchend', handleTouchEnd);
      el.addEventListener('touchcancel', handleTouchEnd);

      return () => {
        el.removeEventListener('touchstart', handleTouchStart);
        el.removeEventListener('touchmove', handleTouchMove);
        el.removeEventListener('touchend', handleTouchEnd);
        el.removeEventListener('touchcancel', handleTouchEnd);
      };
    }
  }, [handleTouchStart, handleTouchMove, handleTouchEnd, enabled]);

  return { containerRef, isPulling, pullDistance, isRefreshing };
}