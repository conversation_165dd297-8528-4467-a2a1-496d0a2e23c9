"use client"

import { useMemo } from "react"
import { useSubscription } from "./useSubscription"

export enum Feature {
  PART_VIEWING = 'PART_VIEWING',                    // Доступ к страницам Part (эталоны)
  PART_APPLICABILITY = 'PART_APPLICABILITY',        // Доступ к данным PartApplicability (аналоги)
  SUPPLIER_PRICES = 'SUPPLIER_PRICES',              // Доступ к информации о поставщиках и ценах
  AI_ASSISTANT = 'AI_ASSISTANT',                    // Доступ к AI-ассистенту для поиска
  DATA_EXPORT = 'DATA_EXPORT',                      // Возможность экспорта данных в Excel/CSV
  ADVANCED_SEARCH = 'ADVANCED_SEARCH',              // Расширенный поиск по атрибутам Part
}

export function useFeatureAccess() {
  const { isPro, isFree, isAuthenticated, isPending } = useSubscription()
  
  const access = useMemo(() => ({
    // Все Pro функции доступны только для PRO подписки
    canViewParts: isPro,
    canViewApplicability: isPro,
    canViewSupplierPrices: isPro,
    canUseAIAssistant: isPro,
    canExportData: isPro,
    canUseAdvancedSearch: isPro,
    
    // Helper функция для проверки доступа к конкретной фиче
    hasAccess: (feature: Feature): boolean => {
      switch (feature) {
        case Feature.PART_VIEWING:
        case Feature.PART_APPLICABILITY:
        case Feature.SUPPLIER_PRICES:
        case Feature.AI_ASSISTANT:
        case Feature.DATA_EXPORT:
        case Feature.ADVANCED_SEARCH:
          return isPro
        default:
          return false
      }
    },
    
    // Метаданные
    isPro,
    isFree,
    isAuthenticated,
    isPending,
  }), [isPro, isFree, isAuthenticated, isPending])
  
  return access
}

export function hasFeatureAccess(
  feature: Feature,
  subscription: 'FREE' | 'PRO' | null
): boolean {
  if (subscription !== 'PRO') return false
  
  switch (feature) {
    case Feature.PART_VIEWING:
    case Feature.PART_APPLICABILITY:
    case Feature.SUPPLIER_PRICES:
    case Feature.AI_ASSISTANT:
    case Feature.DATA_EXPORT:
    case Feature.ADVANCED_SEARCH:
      return true
    default:
      return false
  }
}