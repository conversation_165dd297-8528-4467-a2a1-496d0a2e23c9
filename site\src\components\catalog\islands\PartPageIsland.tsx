"use client";

import { useState, lazy, Suspense } from "react";
import type { Part } from "@/lib/types";
import { Badge } from "@/components/ui/badge";
import { ModernButton } from "@/components/ui/modern-button";
import { ModernCard, ModernCardContent, ModernCardHeader, ModernCardTitle } from "@/components/ui/modern-card";
import { DataTable } from "@/components/ui/data-table";
import { createColumns } from "../pro/columns";
import { HardDrive, ArrowUp } from "lucide-react";
import { MediaThumbnail } from "@/components/catalog/media/MediaThumbnail";
import { useFeatureAccess, Feature } from "@/hooks/useFeatureAccess"
import { SubscriptionGate } from "@/components/subscription/SubscriptionGate"
import { analytics } from "@/lib/analytics"
import { useEffect } from "react"

import { AttributeDisplay } from "../attributes"
import { useIsMobile } from '@/hooks/useMediaQuery';
import ApplicabilitiesCardView from '../ApplicabilitiesCardView';
import { usePullToRefresh } from '@/hooks/usePullToRefresh';
import { PullToRefreshIndicator } from '@/components/mobile';
import { FloatingActionButton } from '@/components/mobile';
import { useRenderTime } from '@/hooks/usePerformance';
import { LoadingState } from "@/components/shared/LoadingState";



const MediaGallery = lazy(() => import('../media/MediaGallery'));

import EquipmentApplicabilitySection from "./EquipmentApplicabilitySection"

type PartPageIslandProps = {
  part: Part;
};


export default function PartPageIsland({ part }: PartPageIslandProps) {

  const { canViewParts, isPending } = useFeatureAccess()
  const [equipmentOpen, setEquipmentOpen] = useState(false)

  const { renderTime } = useRenderTime('PartPageIsland', import.meta.env.DEV)

  const isMobile = useIsMobile();
  const [showScrollTop, setShowScrollTop] = useState(false);

  // Pull to refresh
  const { containerRef, isPulling, pullDistance, isRefreshing } = usePullToRefresh({
    onRefresh: async () => {
      // Refresh part data - would need to expose refetch from parent
      // For now, reload page
      window.location.reload();
    },
    enabled: isMobile,
    threshold: 80,
  });

  // Track scroll position for FAB
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Track page view on mount
  useEffect(() => {
    if (part && canViewParts) {
      analytics.partPageViewed(part.id, part.name)
    }
  }, [part.id, part.name, canViewParts])
  // Track description view (hero/description section is visible on load)
  useEffect(() => {
    analytics.partDescriptionViewed(part.id)
  }, [part.id])


  const columns = createColumns(part.attributes, part.id);

  return (
    <SubscriptionGate
      feature={Feature.PART_VIEWING}
      upsellTitle="Доступ к эталонам только для PRO"
      upsellDescription="Эта страница содержит коммерческую информацию о группах взаимозаменяемости. Оформите PRO подписку для полного доступа."
      upsellFeatures={[
        "Просмотр эталонных групп Part",
        "Детальное сравнение аналогов",
        "Информация о применимости к технике",
        "Экспорт данных в Excel/CSV",
        "Доступ к synonym groups"
      ]}
    >
      <div ref={containerRef} className="container mx-auto px-4 py-4 md:py-8">
        {isMobile && (
          <PullToRefreshIndicator
            pullDistance={pullDistance}
            threshold={80}
            isRefreshing={isRefreshing}
          />
        )}
        <ModernCard>
          <ModernCardHeader>
            <div className="flex items-start justify-between gap-4">
              <div className="flex items-start gap-6">
                <MediaThumbnail mediaAsset={part.image} size="lg" className="flex-shrink-0" />
                <div>
                  <ModernCardTitle className="text-2xl md:text-3xl font-bold">{part.name}</ModernCardTitle>
                  <Badge variant="outline" className="mt-2">{part.partCategory?.name}</Badge>
                </div>
              </div>
              <ModernButton
                variant="gradient"
                onClick={() => analytics.requestImplementationsClicked(part.id)}
              >
                <HardDrive className="mr-2 h-4 w-4" /> Запросить реализации
              </ModernButton>
            </div>
          </ModernCardHeader>
          <ModernCardContent className="space-y-8">
            {/* Hero Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <MediaThumbnail mediaAsset={part.image || part.mediaAssets[0]} size="lg" />
              </div>
              <div className="space-y-4">
                <div>
                  <h2 className="text-2xl font-bold mb-2">{part.name}</h2>
                  <Badge>{part.partCategory?.name}</Badge>
                </div>
                <p className="text-muted-foreground">
                  {`${part.partCategory?.name || 'Деталь'}: ` + part.attributes.slice(0,3).map(a => `${a.template.title}: ${a.value}${a.template.unit ? ` ${a.template.unit}` : ''}`).join('; ')}
                </p>
                <div className="flex gap-2">
                  <Badge variant="secondary">{part.applicabilities.length} аналогов</Badge>
                  <Badge variant="secondary">{part.equipmentApplicabilities?.length || 0} моделей техники</Badge>
                </div>
              </div>
            </div>

            {/* Галерея медиа */}
            {part.mediaAssets.length > 0 && (
              <div>
                <h3 className="font-bold text-lg mb-4">Фотографии и документация</h3>
                <MediaGallery
                  assets={part.mediaAssets}
                  layout="grid"
                  columns={3}
                  enableLightbox={true}
                  enableLazyLoad={true}
                  showThumbnails={true}
                  contextId={part.id}
                  contextType="part"
                />
              </div>
            )}

            {/* Применимость к технике */}
            {part.equipmentApplicabilities && part.equipmentApplicabilities.length > 0 && (
              <EquipmentApplicabilitySection
                equipmentApplicabilities={part.equipmentApplicabilities}
                partId={part.id}
              />
            )}

            {/* Эталонные характеристики */}
            <div>
              <h3 className="font-bold text-lg mb-4">Технические характеристики эталона</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Все аналоги в таблице ниже соответствуют этим характеристикам
              </p>
              <AttributeDisplay
                attributes={part.attributes}
                layout="list"
                showSynonyms={true}
                showTolerance={true}
                contextId={part.id}
                contextType="part"
              />
            </div>

            {/* Таблица аналогов */}
            <div>
              <h3 className="font-bold text-lg mb-4">Доступные аналоги ({part.applicabilities.length})</h3>
              <DataTable
                columns={columns}
                data={part.applicabilities}
                onSortingChange={(sorting) => {
                  if (sorting.length > 0) {
                    const sort = sorting[0]
                    analytics.dataTableSorted(sort.id, sort.desc ? 'desc' : 'asc', part.id)
                  }
                }}
                onRowSelectionChange={(rowSelection) => {
                  const selectedRows = Object.keys(rowSelection).filter(key => rowSelection[key])
                  selectedRows.forEach(rowIndex => {
                    const catalogItemId = part.applicabilities[parseInt(rowIndex)]?.catalogItem?.id
                    if (catalogItemId) {
                      analytics.dataTableRowSelected(catalogItemId, part.id)
                    }
                  })
                }}
              />
            </div>
          </ModernCardContent>
        </ModernCard>
        {isMobile && showScrollTop && (
          <FloatingActionButton
            actions={[
              {
                icon: ArrowUp,
                label: 'Наверх',
                onClick: () => window.scrollTo({ top: 0, behavior: 'smooth' }),
                variant: 'secondary',
              },
            ]}
            position="bottom-right"
          />
        )}
      </div>
    </SubscriptionGate>
  );
}
